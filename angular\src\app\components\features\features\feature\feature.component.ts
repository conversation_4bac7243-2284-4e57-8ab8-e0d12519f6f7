import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, Signal } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { FeatureSetup } from '@app/_interfaces/feature.interface';
import { UserStore, FeatureStore, CalendarIntegrationStore, HabitStore, JournalStore, MoneyTrackerStore } from '@app/_stores';
import { InputToggleComponent } from '@app/components/shared/inputs/input-toggle/input-toggle.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { FeatureListComponent } from '../feature-list/feature-list.component';
import { Subject } from 'rxjs';
import { JournalSetupFormComponent } from '../../journals/journal-setup-form/journal-setup-form.component';
import { MoneyTrackerSetupFormComponent } from '../../money-tracker/money-tracker-setup-form/money-tracker-setup-form.component';
import { CalendarAccountFormComponent } from '../../calendar-integration/calendar-account-form/calendar-account-form.component';
import { HabitSetupFormComponent } from '../../habits/habit-setup-form/habit-setup-form.component';
import { CacheService } from '@app/_services/cache.service';
import { AlertService } from '@app/_services/alert.service';
import { EntityNameType, FeatureNameType, FeatureStatus } from '@app/_types/generic.type';
import { MapService } from '@app/_services/map.service';

@Component({
  selector: 'app-feature',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    InputToggleComponent
  ],
  templateUrl: './feature.component.html',
  styleUrl: './feature.component.scss'
})

export class FeatureComponent implements OnInit {

  readonly featureStore = inject(FeatureStore);
  readonly userStore = inject(UserStore);
  readonly journalStore = inject(JournalStore);
  readonly habitStore = inject(HabitStore);
  readonly moneyTrackerStore = inject(MoneyTrackerStore);
  readonly calendarStore = inject(CalendarIntegrationStore);
  unSubscribe = new Subject<void>();
  featureName: Signal<Record<FeatureNameType, string>> = this.mapService.featureNameMap;
  featureStatus: Signal<Record<FeatureStatus, string>> = this.mapService.featureStatusMap;
  confirmConfig: { [key in EntityNameType]?: { title: string, message: string, buttonText: string, buttonColor: string } } = {
    todo: {
      title: this.cc.texts()['overlay_hideFeature_todoTitle'],
      message: this.cc.texts()['overlay_hideFeature_todoContent'],
      buttonText: this.cc.texts()['overlay_hideFeature_disable'],
      buttonColor: 'color-11'
    },
    note: {
      title: this.cc.texts()['overlay_hideFeature_noteTitle'],
      message: this.cc.texts()['overlay_hideFeature_noteContent'],
      buttonText: this.cc.texts()['overlay_hideFeature_disable'],
      buttonColor: 'color-11'
    },
    list: {
      title: this.cc.texts()['overlay_hideFeature_listTitle'],
      message: this.cc.texts()['overlay_hideFeature_listContent'],
      buttonText: this.cc.texts()['overlay_hideFeature_disable'],
      buttonColor: 'color-11'
    },
    habit: {
      title: this.cc.texts()['overlay_hideFeature_habitTitle'],
      message: this.cc.texts()['overlay_hideFeature_habitContent'],
      buttonText: this.cc.texts()['overlay_hideFeature_disable'],
      buttonColor: 'color-11'
    },
    journal: {
      title: this.cc.texts()['overlay_hideFeature_journalTitle'],
      message: this.cc.texts()['overlay_hideFeature_journalContent'],
      buttonText: this.cc.texts()['overlay_hideFeature_disable'],
      buttonColor: 'color-11'
    },
    moneyTracker: {
      title: this.cc.texts()['overlay_hideFeature_moneyTrackerTitle'],
      message: this.cc.texts()['overlay_hideFeature_moneyTrackerContent'],
      buttonText: this.cc.texts()['overlay_hideFeature_disable'],
      buttonColor: 'color-11'
    },
    calendarIntegration: {
      title: this.cc.texts()['overlay_hideFeature_calendarTitle'],
      message: this.cc.texts()['overlay_hideFeature_calendarContent'],
      buttonText: this.cc.texts()['overlay_hideFeature_disable'],
      buttonColor: 'color-11'
    }
  }

  constructor(
    public dialog: MatDialog,
    public cc: CacheService,
    private alertService: AlertService,
    private mapService: MapService
  ) {

  }

  ngOnInit() {

  }

  async toggleChange(feature: FeatureSetup, value: boolean) {
    const viewSignals = this.userStore.viewSignals();
    switch (feature.name) {
      case 'todo':
        viewSignals['showTodoFeature'].set(value);
        this.userStore.updateViewSettings();
        break;
      case 'note':
        viewSignals['showNoteFeature'].set(value);
        this.userStore.updateViewSettings();
        break;
      case 'list':
        viewSignals['showListFeature'].set(value);
        this.userStore.updateViewSettings();
        break;
      case 'boolean':
      case 'timer':
      case 'numeric':
      case 'single':
      case 'multiple':
        const setup = this.habitStore.idToSetup()[feature.id];
        this.habitStore.updateSetups([{ ...setup, isPaused: !value }]);
        break;
      case 'journal':
        const journalSetup = this.journalStore.idToSetup()[feature.id];
        this.journalStore.updateJournalSetups([{ ...journalSetup, isPaused: !value }]);
        break;
      case 'moneyTracker':
        const moneyTrackerSetup = this.moneyTrackerStore.idToSetup()[feature.id];
        this.moneyTrackerStore.updateMoneySetups([{ ...moneyTrackerSetup, isPaused: !value }]);
        break;
      case 'google':
      case 'microsoft':
        const calendarIntegration = this.calendarStore.idToAccount()[feature.id];
        this.calendarStore.updateCalendarIntegrations([{ ...calendarIntegration, isPaused: !value }]);
        break;
      default:
        break;
    }
  }

  addFeatures() {
    const dialog = this.dialog.open(FeatureListComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
    })
  }

  openFeatureSetup(feature: FeatureSetup) {
    switch (feature.name) {
      case 'boolean':
      case 'timer':
      case 'numeric':
      case 'single':
      case 'multiple':
        const setupHabitDialog = this.dialog.open(HabitSetupFormComponent, {
          width: '100%',
          maxWidth: '750px',
          maxHeight: '90vh',
          minHeight: '90vh',
          disableClose: true,
          data: {
            mode: 'edit',
            value: feature.entity,
            habitType: feature.name,
          },
        });
        break;
      case 'journal':
        const setupDialog = this.dialog.open(JournalSetupFormComponent, {
          width: '100%',
          maxWidth: '750px',
          maxHeight: '90vh',
          minHeight: '90vh',
          disableClose: true,
          data: {
            mode: 'edit',
            value: feature.entity,
          },
        });
        break;
      case 'moneyTracker':
        const setupMoneyDialog = this.dialog.open(MoneyTrackerSetupFormComponent, {
          width: '100%',
          maxWidth: '750px',
          maxHeight: '90vh',
          minHeight: '90vh',
          disableClose: true,
          data: {
            mode: 'edit',
            value: feature.entity,
          },
        });
        break;

      case 'google':
      case 'microsoft':
        const setupCalendarDialog = this.dialog.open(CalendarAccountFormComponent, {
          width: '100%',
          maxWidth: '750px',
          maxHeight: '90vh',
          minHeight: '90vh',
          disableClose: true,
          data: {
            mode: 'edit',
            value: feature.entity,
          },
        });
        break;

      default:
        break;
    }

  }

}

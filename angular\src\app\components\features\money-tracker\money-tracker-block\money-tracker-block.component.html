<div class="flex justify-between items-center" [ngClass]="blockClass" (click)="openTransaction()" role="button">
    <div class="">
        <p class="ri-pb-2 mb-0" *ngIf="show.includes('label') && isLabel"><span class="entity-badge text-12-400 color-7" >{{ cc.texts()['screen_common_moneyTracker'] }}</span></p>
        <p class="text-16-400 color-8 mb-0">{{ transaction().title }}</p>
        <p class="text-12-400 color-7 mb-0 ri-pt-2" *ngIf="descriptionType !== 'none' && transaction().description">{{ transaction().description | parseText: descriptionType === 'short' ? 'short' : 'full' }}</p>
        <div class="mb-0 ri-pt-2" *ngIf="(show.includes('attachments') && transaction().attachments.length > 0) || show.includes('setup') || isSetup">
            <div class="inline-flex items-center ri-pe-3" *ngIf="show.includes('setup') || isSetup">
                <app-svg name="widget" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7">{{ moneyStore.idToSetup()[transaction() ? transaction().setupId : ''].title }}</span>
            </div>
            <div class="inline-flex items-center ri-pe-3" *ngIf="show.includes('attachments') && transaction().attachments.length > 0">
                <app-svg name="attachment" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7">{{ transaction().attachments.length }}</span>
            </div>
        </div>
        <p class="text-12-400 color-7 mb-0 ri-pt-2 hashtags-block" *ngIf="show.includes('hashtag') && transaction().tags.length > 0">
            <span class="hashtag" *ngFor="let tagId of transaction().tags">#{{userStore.tagMap()[tagId]}}</span>
        </p>
    </div>
    <p class="text-16-400 mb-0 whitespace-nowrap ri-ps-4" [ngClass]="transaction().transactionType === 'income' ? 'color-1' : 'color-11'">{{ setup().currency + ' ' + transaction().amount }}</p>
</div>
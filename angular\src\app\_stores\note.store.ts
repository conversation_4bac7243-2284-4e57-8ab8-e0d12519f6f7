import { patchState, signalStore, withComputed, withHooks, withMeth<PERSON>, withState } from "@ngrx/signals";
import { computed, inject, signal, WritableSignal } from "@angular/core";
import { CryptographyService } from "@app/_services/cryptography.service";
import { FirebaseFunctionService } from "@app/_services/firebase-function.service";
import { Note } from "@app/_interfaces/note.interface";
import { UserStore } from "./user.store";
import { IndexDbService } from "@app/_services/index-db.service";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { DependencyService } from "@app/_services/dependency.service";
import { DatePipe } from "@angular/common";
import { dbVersion } from "@app/_configs/db.config";
import { UtilsService } from "@app/_services/utils.service";
import { FirestoreCollection } from "@app/_enums/firestore-collection.enum";
import { firstValueFrom } from "rxjs";
import { AttachmentType } from "@app/_types/generic.type";
import { MapService } from "@app/_services/map.service";

type NoteState = {
  notes: Note[];
  activeNotes: Note[];
  isLoading: boolean;
  filter: {
    query: WritableSignal<string>;
    isFav: WritableSignal<boolean>;
    moods: WritableSignal<number[]>,
    tags: WritableSignal<string[]>;
    attachments: WritableSignal<AttachmentType[]>,
    startDate: WritableSignal<Date | null>,
    endDate: WritableSignal<Date | null>,
  };
  selectedNoteIds: string[];
};

const initialState: NoteState = {
  notes: [],
  activeNotes: [],
  isLoading: false,
  filter: {
    query: signal<string>(''),
    isFav: signal<boolean>(false),
    moods: signal<number[]>([]),
    tags: signal<string[]>([]),
    attachments: signal<AttachmentType[]>([]),
    startDate: signal<Date | null>(null),
    endDate: signal<Date | null>(null)
  },
  selectedNoteIds: []
};

export const NoteStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),

  withComputed(({ notes, filter, selectedNoteIds }, userStore = inject(UserStore), ds = inject(DependencyService), datePipe = inject(DatePipe), mapService = inject(MapService)) => ({
    filteredNotes: computed(() => {
      const filteredNotes = ds.filterNotes(notes(), filter().query(), filter().isFav(), filter().moods(), filter().tags(), filter().attachments(), filter().startDate(), filter().endDate());
      return filteredNotes.sort((a, b) => {
        const aDate = a.noteUpdatedAt || a.localUpdatedAt || new Date(0);
        const bDate = b.noteUpdatedAt || b.localUpdatedAt || new Date(0);
        return bDate.getTime() - aDate.getTime();
      });
    }),

    selectedNotes: computed(() => {
      const ids = selectedNoteIds();
      return new Map(
        [...ids].map(id => [id, notes().find(note => note.id === id) || null])
      );
    }),

    hashtagGroup: computed(() => {
      const hashtags = userStore.hashtags();

      const sortedNotes = ds.filterNotes(notes(), filter().query(), filter().isFav(), filter().moods(), filter().tags(), filter().attachments(), filter().startDate(), filter().endDate());

      const hashtagMap = new Map(hashtags.map(({ id, tag }) => [id, tag]));

      // Group the note Data based on hashtags
      const { groupedMap, noHashtagNotes } = sortedNotes.reduce(
        (acc, note) => {
          const { groupedMap, noHashtagNotes } = acc;

          if (!note.tags || note.tags.length === 0) {
            noHashtagNotes.push(note);
          } else {
            note.tags.forEach(tagId => {
              const id = tagId || '';
              if (!groupedMap.has(id)) {
                groupedMap.set(id, {
                  id,
                  name: `#${hashtagMap.get(id) || id}`,
                  data: []
                });
              }
              groupedMap.get(id)!.data.push(note);
            });
          }

          return acc;
        },
        {
          groupedMap: new Map<string, { id: string; name: string; data: Note[] }>(),
          noHashtagNotes: [] as Note[]
        }
      );

      const groupedData = [...groupedMap.values()];

      if (noHashtagNotes.length > 0) {
        groupedData.push({
          id: 'no-hashtags',
          name: 'No Hashtags',
          data: noHashtagNotes
        });
      }

      return groupedData;
    }),

    moodsGroup: computed(() => {
      const sortedNotes = ds.filterNotes(notes(), filter().query(), filter().isFav(), filter().moods(), filter().tags(), filter().attachments(), filter().startDate(), filter().endDate());
      const moodsMap = mapService.moodMap();

      // Group the note Data based on hashtags
      const { groupedMap, noEmotionNotes } = sortedNotes.reduce(
        (acc, note) => {
          const { groupedMap, noEmotionNotes } = acc;

          if (!note.emotion && note.emotion !== 0) {
            noEmotionNotes.push(note);
          } else {
            const id = note.emotion;
            if (!groupedMap.has(id)) {
              groupedMap.set(id, {
                id,
                name: `#${moodsMap[id] || id}`,
                data: []
              });
            }
            groupedMap.get(id)!.data.push(note);
          }

          return acc;
        },
        {
          groupedMap: new Map<string, { id: string; name: string; data: Note[] }>(),
          noEmotionNotes: [] as Note[]
        }
      );

      const groupedData = [...groupedMap.values()];

      if (noEmotionNotes.length > 0) {
        groupedData.push({
          id: 'no-moods',
          name: 'No Moods',
          data: noEmotionNotes
        });
      }

      return groupedData;
    }),

    dateGroup: computed(() => {
      const sortedNotes = ds.filterNotes(notes(), filter().query(), filter().isFav(), filter().moods(), filter().tags(), filter().attachments(), filter().startDate(), filter().endDate());
      // Group the note Data based on hashtags
      const groupedMap = new Map<string, { id: string; name: string; data: Note[] }>();

      sortedNotes.forEach(note => {
        if (!note.noteUpdatedAt) return;

        const dateOnly = note.noteUpdatedAt.toLocaleDateString('en-CA'); // Format: YYYY-MM-DD

        if (!groupedMap.has(dateOnly)) {
          groupedMap.set(dateOnly, {
            id: dateOnly,
            name: datePipe.transform(note.noteUpdatedAt, 'dd MMM yyyy, ccc') || '',
            data: []
          });
        }

        groupedMap.get(dateOnly)!.data.push(note);
      });
      const groupedData = [...groupedMap.values()];

      return groupedData;
    }),
  })),

  withMethods((
    store,
    idbService = inject(IndexDbService),
    userStore = inject(UserStore),
    cryptoService = inject(CryptographyService),
    firebaseFunctionService = inject(FirebaseFunctionService),
    utilsService = inject(UtilsService),
  ) => ({
    addNote: async (noteData: Note) => {
      const note: Note = noteData;
      await idbService.add('notes', noteData);

      // Upload data to the backend
      const syncRequest = cryptoService.prepareRawData({ ...note });
      await firebaseFunctionService.uploadData(syncRequest);
    },

    updateNotes: async (notes: Note[]) => {
      const oldNotes = [];
      const newNotes = [];
      for (const note of notes) {
        const oldNote = await firstValueFrom(idbService.getEntityById('notes', note.id));
        const newNote = await idbService.update('notes', note.id, note);
        newNote!.localUpdatedAt = new Date();
        newNote!.cloudUpdatedAt = null;
        newNote!.lastUpdatedAt = new Date();
        newNote!.noteUpdatedAt = new Date();
        oldNotes.push(oldNote);
        newNotes.push(newNote);
      }

      const syncRequest = cryptoService.preparePatchData(
        oldNotes,
        newNotes,
        FirestoreCollection.Notes
      );

      await firebaseFunctionService.uploadData(syncRequest);
    },

    deleteNote: async (notes: Note[]) => {
      const oldNotes = [];
      const newNotes = [];
      for (const note of notes) {
        note.deletedAt = new Date();
        const oldNote = await firstValueFrom(idbService.getEntityById('notes', note.id));
        const newNote = await idbService.update('notes', note.id, note);
        newNote.localUpdatedAt = new Date();
        newNote.cloudUpdatedAt = null;
        oldNotes.push(oldNote);
        newNotes.push(newNote);
      }

      const syncRequest = cryptoService.preparePatchData(
        oldNotes,
        newNotes,
        FirestoreCollection.Notes
      );

      await firebaseFunctionService.uploadData(syncRequest);
    },

    getNewNote: (): Note => {
      const user = userStore.user?.();
      return {
        id: utilsService.getNewId(),
        title: '',
        description: '',
        attachments: [],
        emotion: null,
        uid: user?.uid ?? '',
        ownerName: user?.name,
        ownerEmail: user?.email,
        createdAt: new Date(),
        sessionId: utilsService.getNewId(),
        docVer: dbVersion,
        docCollection: FirestoreCollection.Notes.toString(),
        source: 'client',
        encData: {
          dek: cryptoService.createEncryptedDocKey(),
          encFields: [
            'title',
            'description',
            'ownerName',
            'ownerEmail',
            'members.membersConfig{}.eEmail',
          ]
        },
        position: 3000,
        isPublic: false,
        publicId: null,
        members: { memberHashedEmails: [], membersConfig: {} },
        collaboratorLimit: 5,
        inviteLink: null,
        tags: [],
        isFav: false,
        lastUpdatedBy: user?.uid ?? '',
        noteUpdatedAt: new Date(),
        localUpdatedAt: new Date(),
        cloudUpdatedAt: new Date(),
        lastUpdatedAt: new Date(),
        permaDeletedAt: null,
        deletedAt: null,
      }
    },

    selectNote: (id: string) => {
      patchState(store, (state) => ({ selectedNoteIds: [...state.selectedNoteIds, id] }));
    },

    deselectNote: (id: string) => {
      patchState(store, (state) => ({ selectedNoteIds: state.selectedNoteIds.filter(i => i !== id) }));
    },

    isFiltered: () => {
      return store.filter().tags().length > 0 || store.filter().moods().length > 0 || store.filter().attachments().length > 0 || store.filter().startDate() || store.filter().endDate() || store.filter().isFav() === true || store.filter().query() !== '';
    },

    clearFilter: () => {
      store.filter().query.set('');
      store.filter().isFav.set(false);
      store.filter().moods.set([]);
      store.filter().tags.set([]);
      store.filter().attachments.set([]);
      store.filter().startDate.set(null);
      store.filter().endDate.set(null);
    },
  })),

  withHooks({
    async onInit(
      store,
      idbService = inject(IndexDbService)
    ) {
      idbService.notes$.pipe(takeUntilDestroyed()).subscribe(data => {
        if (data) {
          patchState(store, { notes: data });
          const activeData = data.filter(note => !note.deletedAt);
          patchState(store, { activeNotes: activeData });
        }
      });
    },
  }),
);

export class AttachmentConstants {

  static supportedImageTypes: string[] = ['jpg', 'jpeg', 'png', 'webp'];
  static supportedVideoTypes: string[] = ['mp4', 'mov', 'avi', 'flv', 'wmv', 'mkv'];
  static supportedAudioTypes: string[] = ['mp3', 'wav', 'm4a'];

  static get supportedDocumentTypes(): string[] {
    return [
      'pdf',
      'doc',
      'docx',
      'xls',
      'xlsx',
      'ppt',
      'pptx',
      'txt',
      'gif',
      ...AttachmentConstants.supportedImageTypes,
      ...AttachmentConstants.supportedVideoTypes,
      ...AttachmentConstants.supportedAudioTypes,
    ];
  }

  static maxImageSize: number = 20 * 1024 * 1024; // 20 MB
  static maxVideoSize: number = 50 * 1024 * 1024; // 50 MB
  static maxAudioSize: number = 10 * 1024 * 1024; // 10 MB
  static maxDocumentSize: number = 10 * 1024 * 1024; // 10 MB

  static maxImageSizeMb: number = AttachmentConstants.maxImageSize / (1024 * 1024);
  static maxVideoSizeMb: number = AttachmentConstants.maxVideoSize / (1024 * 1024);
  static maxAudioSizeMb: number = AttachmentConstants.maxAudioSize / (1024 * 1024);
  static maxDocumentSizeMb: number = AttachmentConstants.maxDocumentSize / (1024 * 1024);

  static getMaxFileSize(fileType: AttachmentType, inMb: boolean = false): number {
    switch (fileType) {
      case 'image':
        return inMb ? AttachmentConstants.maxImageSizeMb : AttachmentConstants.maxImageSize;
      case 'video':
        return inMb ? AttachmentConstants.maxVideoSizeMb : AttachmentConstants.maxVideoSize;
      case 'audio':
        return inMb ? AttachmentConstants.maxAudioSizeMb : AttachmentConstants.maxAudioSize;
      case 'document':
        return inMb ? AttachmentConstants.maxDocumentSizeMb : AttachmentConstants.maxDocumentSize;
      default:
        return 0;
    }
  }
}

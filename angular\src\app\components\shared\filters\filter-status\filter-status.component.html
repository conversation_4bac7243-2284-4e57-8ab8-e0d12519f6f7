<button class="btn btn-filter btn-toggle h-full" [matMenuTriggerFor]="filterStatusMenu" [ngClass]="{ 'active': value() !== 'all' }">
    <app-svg name="circleCheck" [color]="value() !== 'all' ? cc.theme.color12 : cc.theme.color7" class="me-1" style="height: 15px; width: 15px;"></app-svg>
    <span>{{value() === 'all' ? cc.texts()['screen_common_status'] : entityStatusMap()[value()]}}</span>
</button>

<mat-menu #filterStatusMenu="matMenu" class="me-menu ri-w-350px">
    <ng-container *ngFor="let status of entityFilterStatus; let i = index;">
        <button mat-menu-item *ngIf="!hiddenValues.includes(status)" (click)="onStatusSelected(status, $event)">
            <span class="text-14-400 color-8 flex items-center justify-between">
                <span>{{entityStatusMap()[status]}}</span>
                <app-svg name="tick" *ngIf="value() === status" [color]="cc.theme.color35"></app-svg>
            </span>
        </button>
    </ng-container>
</mat-menu>
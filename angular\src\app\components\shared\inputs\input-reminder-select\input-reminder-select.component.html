<div class="flex justify-between ri-p-4">
    <p class="text-16-500 color-8 mb-0">{{ cc.texts()['overlay_reminderSelect_remind'] }}</p>
    <app-input-dropdown [config]="remindTypeConfig" [signal]="remindType" (select)="remindTypeChange()"></app-input-dropdown>
</div>
<div class="flex justify-between ri-px-4 ri-pb-4">
    <p class="text-16-500 mb-0" [ngClass]="remindType() === 'ON_TIME' ? 'color-10' : 'color-8'">{{ cc.texts()['overlay_reminderSelect_time'] }}</p>
    <app-input-duration [control]="reminderTime" [maxHour]="23" *ngIf="remindType() !== 'ON_TIME'"></app-input-duration>
    <p class="text-16-500 color-10 mb-0" *ngIf="remindType() === 'ON_TIME'">-</p>
</div>
<!-- <pre class="color-8">{{ruleRecord | json}}</pre> -->
<div class="bottom-section flex items-center justify-between ri-p-4 ri-bt-2">
    <div>
        <app-svg name="trash" role="button" [color]="cc.theme.color11" (click)="deleteReminder()"
            *ngIf="data.type === 'edit'"></app-svg>
    </div>
    <div class="flex items-center">
        <button class="btn-text text-16-500 color-7 me-4" (click)="closeDialog()">{{ cc.texts()['screen_common_close'] }}</button>
        <button type="submit" class="btn-text text-16-500 color-35 check-disabled" (click)="save()" [disabled]="!hasChanges() && data.type === 'edit'">{{ data.type === 'add' ? cc.texts()['overlay_reminderSelect_buttonAdd'] : cc.texts()['screen_common_update'] }}</button>
    </div>
</div>
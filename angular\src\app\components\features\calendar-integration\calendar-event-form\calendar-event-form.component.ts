import { CommonModule } from '@angular/common';
import { Component, Inject, inject, ViewChild } from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, FormsModule, NgForm, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { CalendarEventSetup, CalendarIntegration } from '@app/_interfaces/calendar-integration.interface';
import { EntitySetup } from '@app/_interfaces/feature.interface';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';
import { CalendarIntegrationStore, UserStore } from '@app/_stores';
import { CalendarIntegrationType } from '@app/_types/generic.type';
import { InputDatePeriodComponent } from '@app/components/shared/inputs/input-date-period/input-date-period.component';
import { InputReminderComponent } from '@app/components/shared/inputs/input-reminder/input-reminder.component';
import { InputTextComponent } from '@app/components/shared/inputs/input-text/input-text.component';
import { InputTextareaComponent } from '@app/components/shared/inputs/input-textarea/input-textarea.component';
import { InputTimeComponent } from '@app/components/shared/inputs/input-time/input-time.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { Subject, takeUntil } from 'rxjs';
import { CalendarAccountFormComponent } from '../calendar-account-form/calendar-account-form.component';

@Component({
  selector: 'app-calendar-event-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatMenuModule,
    SvgComponent,
    InputTextComponent,
    InputTextareaComponent,
    InputDatePeriodComponent,
    InputTimeComponent,
    InputReminderComponent
  ],
  templateUrl: './calendar-event-form.component.html',
  styleUrl: './calendar-event-form.component.scss'
})

export class CalendarEventFormComponent {

  @ViewChild('cEventForm') cEventForm!: NgForm;
  unSubscribe = new Subject<void>();
  calendarEventForm: FormGroup;
  calendarEventInitial: CalendarEventSetup;
  readonly calendarStore = inject(CalendarIntegrationStore);
  readonly userStore = inject(UserStore);
  calendarType: CalendarIntegrationType;
  rawData: { 
    hangoutLink?: string,
    showAs?: string,
    sensitivity?: string,
    onlineMeetingUrl?: string,
  };
  calendarAccount: CalendarIntegration;

  constructor(
    private dialogRef: MatDialogRef<CalendarEventFormComponent>,
    private fb: FormBuilder,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'new' | 'edit', value: EntitySetup, dateString: string },
    public cc: CacheService,
    private alertService: AlertService,
    public dialog: MatDialog
  ) {
    const calendarEvent = this.calendarStore.idToEventSetup()[data.value.id];
    console.log("calendare event ====================--->", calendarEvent)
    this.calendarEventInitial = this.initiateForm(calendarEvent);
    this.calendarType = calendarEvent.type;
    this.rawData = JSON.parse(calendarEvent.rawData);
    console.log("rawData ====================--->", this.rawData);
    this.calendarAccount = this.calendarStore.calIdToAccount()[calendarEvent.calendarAccountId];
    console.log("calendarAccount ====================--->", this.calendarAccount)

    this.calendarEventForm = this.fb.group({
      id: new FormControl(this.calendarEventInitial.id, Validators.required),
      title: new FormControl(this.calendarEventInitial.title, Validators.required),
      description: new FormControl(this.calendarEventInitial.description),
      calendarId: new FormControl(this.calendarEventInitial.calendarId, Validators.required),
      calendarName: new FormControl(this.calendarEventInitial.calendarName, Validators.required),
      calendarIntegrationId: new FormControl(this.calendarEventInitial.calendarIntegrationId, Validators.required),
      startAt: new FormControl(this.calendarEventInitial.startAt),
      endAt: new FormControl(this.calendarEventInitial.endAt),
      cloudUpdatedAt: new FormControl(this.calendarEventInitial.cloudUpdatedAt),
      isStartTimeSet: new FormControl(this.calendarEventInitial.isStartTimeSet, Validators.required),
      isTmzAffected: new FormControl(this.calendarEventInitial.isTmzAffected, Validators.required),
      duration: new FormControl(this.calendarEventInitial.duration, Validators.required),
      reminderAt: new FormControl(this.calendarEventInitial.reminderAt, Validators.required),
      repeat: new FormControl(this.calendarEventInitial.repeat, Validators.required),
    });

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  initiateForm(calendarEvent: CalendarEventSetup): any {
    return {
      id: calendarEvent.id,
      title: calendarEvent.title,
      description: calendarEvent.description,
      calendarId: calendarEvent.calendarId,
      calendarName: calendarEvent.calendarName,
      calendarIntegrationId: calendarEvent.calendarIntegrationId,
      startAt: calendarEvent.startAt,
      endAt: calendarEvent.endAt,
      cloudUpdatedAt: calendarEvent.cloudUpdatedAt,
      isStartTimeSet: calendarEvent.isStartTimeSet,
      isTmzAffected: calendarEvent.isTmzAffected,
      duration: calendarEvent.duration,
      reminderAt: calendarEvent.reminderAt,
      repeat: calendarEvent.repeat,
    }
  }

  getFc(fcName: string): FormControl {
    return this.calendarEventForm.get(fcName) as FormControl;
  }

  openAlert() {
    this.alertService.alert(this.cc.texts()['overlay_editCalendarEventAccessDenied_title'], this.cc.texts()['overlay_editCalendarEventAccessDenied_content'], this.cc.texts()['screen_common_ok']);
  }

  openCalendarAccountForm() {
    const setupCalendarDialog = this.dialog.open(CalendarAccountFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      minHeight: '90vh',
      disableClose: true,
      data: {
        mode: 'edit',
        value: this.calendarAccount,
      },
    });
    return setupCalendarDialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.dialogRef.close();
      }
    });;
  }

  async closeDialog() {
    this.dialogRef.close();
  }

}

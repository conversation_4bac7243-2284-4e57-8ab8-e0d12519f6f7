import { Component, effect, Inject, inject, Input, signal, ViewChild, WritableSignal } from '@angular/core';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormControl, FormGroup, FormsModule, NgForm, ReactiveFormsModule, Validators } from "@angular/forms";
import { Subject, takeUntil } from "rxjs";
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from "@angular/material/dialog";
import { Note } from '@app/_interfaces/note.interface';
import { NoteStore } from '@app/_stores';
import { CommonModule } from '@angular/common';
import { CacheService } from '@app/_services/cache.service';
import { UtilsService } from '@app/_services/utils.service';
import * as _ from 'lodash';
import { HashtagComponent } from '@app/components/addons/hashtags/hashtag/hashtag.component';
import { AlertService } from '@app/_services/alert.service';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { InputTextComponent } from '@app/components/shared/inputs/input-text/input-text.component'
import { InputTextEditorComponent } from '@app/components/shared/inputs/input-text-editor/input-text-editor.component';
import { InputHashtagComponent } from '@app/components/shared/inputs/input-hashtag/input-hashtag.component';
import { InputMoodComponent } from '@app/components/shared/inputs/input-mood/input-mood.component';
import { AttachmentListComponent } from '@app/components/addons/attachments/attachment-list/attachment-list.component';
import { InputMoodDropdownComponent } from '@app/components/shared/inputs/input-mood-dropdown/input-mood-dropdown.component';
import { InputFileComponent } from '@app/components/shared/inputs/input-file/input-file.component';
import { Attachment } from '@app/_interfaces/generic.interface';
import { DependencyService } from '@app/_services/dependency.service';
import { CollaboratorComponent } from '@app/components/addons/collaborators/collaborator/collaborator.component';
import { MatMenuModule } from '@angular/material/menu';
import { FirebaseService } from '@app/_services/firebase.service';
import html2canvas from 'html2canvas';
import { FirestoreCollection } from '@app/_enums/firestore-collection.enum';
import { FirebaseFunctionService } from '@app/_services/firebase-function.service';
import { QrCodeDialogComponent } from '@app/components/shared/qr-code-dialog/qr-code-dialog.component';
import { InputToggleComponent } from '@app/components/shared/inputs/input-toggle/input-toggle.component';
import { notOnlyWhitespace } from '@app/_directives/form-validator.directive';
import { DateFormatPipe } from '@app/_pipes/date-format.pipe';

@Component({
  selector: 'app-note-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SvgComponent,
    InputTextComponent,
    InputTextEditorComponent,
    InputHashtagComponent,
    InputMoodComponent,
    InputMoodDropdownComponent,
    AttachmentListComponent,
    InputFileComponent,
    MatMenuModule,
    InputToggleComponent
  ],
  templateUrl: './note-form.component.html',
  styleUrl: './note-form.component.scss'
})

export class NoteFormComponent {

  @ViewChild('ntForm') ntForm!: NgForm;
  unSubscribe = new Subject<void>();
  noteForm: FormGroup;
  noteInitial: Note;
  mode: 'new' | 'edit';
  readonly noteStore = inject(NoteStore);
  newAttachments: FormArray = this.fb.array([]);
  deletedAttachments: FormArray = this.fb.array([]);
  noteData: Note;

  constructor(
    public dialogRef: MatDialogRef<NoteFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'new' | 'edit', value: Note },
    private fb: FormBuilder,
    public cc: CacheService,
    private utilsService: UtilsService,
    public dialog: MatDialog,
    private alertService: AlertService,
    private ds: DependencyService,
    private fbService: FirebaseService,
    private fbfs: FirebaseFunctionService
  ) {
    this.mode = data.mode;
    this.noteData = data.value;
    this.noteInitial = data.mode == 'new' ? this.initiateForm() : this.initiateForm(data.value);
    this.noteStore.selectNote(this.noteInitial.id);

    effect(() => {
      this.noteData = this.noteStore.selectedNotes().get(this.noteInitial.id) || this.noteInitial;
    });

    this.noteForm = this.fb.group({
      id: new FormControl(this.noteInitial.id, Validators.required),
      title: new FormControl(this.noteInitial.title, [Validators.required, Validators.maxLength(120), notOnlyWhitespace()]),
      description: new FormControl(this.noteInitial.description),
      tags: new FormControl(this.noteInitial.tags),
      emotion: new FormControl(this.noteInitial.emotion),
      attachments: this.fb.array([]),
      isPublic: new FormControl(this.noteInitial.isPublic),
      uid: new FormControl(this.noteInitial.uid, Validators.required)
    });

    if (this.noteInitial.attachments && this.noteInitial.attachments.length !== 0) {
      this.clearAttachments();
      this.noteInitial.attachments.forEach((attach: Attachment) => {
        this.attachments.push(this.ds.addAttachmentForm(attach));
      });
    }

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  get attachmentValues(): any[] {
    const attachments = [...this.attachments.value, ...this.newAttachments.value];
    return attachments;
  }

  get attachments(): FormArray {
    return this.noteForm.get('attachments') as FormArray;
  }

  clearAttachments() {
    while (this.attachments.length !== 0) {
      this.attachments.removeAt(0)
    }
  }

  get basePath(): string {
    return `userData/attachments/${this.cc.user ? this.cc.user.uid : ''}/notes/${this.noteInitial.id}`;
  }

  initiateForm(note?: Note): Note {
    return {
      id: note ? note.id : this.utilsService.getNewId(),
      title: note ? note.title : '',
      description: note ? note.description : '',
      tags: note ? note.tags : [],
      emotion: note ? note.emotion : null,
      attachments: note ? note.attachments : [],
      isPublic: note ? note.isPublic : false,
      uid: this.cc.user.uid
    }
  }

  getFc(fcName: string): FormControl {
    return this.noteForm.get(fcName) as FormControl;
  }

  hasChanges() {
    const initial = _.cloneDeep(this.noteInitial);
    const current = _.cloneDeep(this.noteForm.value);
    if (initial.attachments?.length !== current.attachments?.length || this.newAttachments.length > 0 || this.deletedAttachments.length > 0) return true;
    // initial.attachments.sort((a: { key: string; }, b: { key: string; }) => (a.key > b.key ? 1 : -1));
    // current.attachments.sort((a: { key: string; }, b: { key: string; }) => (a.key > b.key ? 1 : -1));
    return !_.isEqual(initial, current);
  }

  deleteAttachments(attachments: Attachment[]) {
    attachments?.forEach(attach => {
      const attachmentFormArray = attach.status === 'local' ? this.newAttachments : this.attachments;
      const index = attachmentFormArray.controls.findIndex(control => control.get('id')?.value === attach.id);
      if (index !== - 1) {
        attachmentFormArray.removeAt(index);
      }
      if (attach.status === 'cloud') {
        this.deletedAttachments.push(this.ds.addAttachmentForm(attach));
      }
    });
  }

  async save() {
    this.cc.isLoading = true;
    if (!this.noteForm.value.id) return;
    if (this.newAttachments.length > 0) {
      const attachmentsRecord: Record<number, { attachments: { path: string; file: Uint8Array }[]; dek?: string }> = {};
      this.newAttachments.value.forEach((attachment: Attachment) => {
        const originalPath = `${this.basePath}/originalFiles/${attachment.id}.${attachment.format}`;
        const files = [{ path: originalPath, file: attachment.originalFile as Uint8Array }];
        if (attachment.fileType === 'image') {
          const optimizedPath = `${this.basePath}/optimizedFiles/${attachment.id}.${attachment.format}`;
          const thumbnailPath = `${this.basePath}/thumbnails/${attachment.id}.${attachment.format}`;
          files.push({ path: optimizedPath, file: attachment.optimizedFile as Uint8Array });
          files.push({ path: thumbnailPath, file: attachment.thumbnail as Uint8Array });
        }
        attachmentsRecord[attachment.id] = { attachments: files, dek: this.data?.value?.encData?.dek || '' };
      });
      const uploadRecord = await this.fbService.uploadFilesAsRecord(attachmentsRecord);
      for (const attach of this.newAttachments.value) {
        if (attach.id in uploadRecord && uploadRecord[attach.id]) {
          const updatedAttach: Attachment = {
            ...attach,
            status: 'cloud',
          };
          this.attachments.push(this.ds.addAttachmentForm(updatedAttach));
        }
      }
    }

    if (this.deletedAttachments.length > 0) {
      const deletedAttachmentRecord: Record<number, string[]> = {};
      this.deletedAttachments.value.forEach((attachment: Attachment) => {
        const originalPath = `${this.basePath}/originalFiles/${attachment.id}.${attachment.format}`;
        const filePaths: string[] = [originalPath];
        if (attachment.fileType === 'image') {
          const optimizedPath = `${this.basePath}/optimizedFiles/${attachment.id}.${attachment.format}`;
          const thumbnailPath = `${this.basePath}/thumbnails/${attachment.id}.${attachment.format}`;
          filePaths.push(optimizedPath);
          filePaths.push(thumbnailPath);
        }
        deletedAttachmentRecord[attachment.id] = filePaths;
      });
      await this.fbService.deleteFilesAsRecord(deletedAttachmentRecord);
    }

    const noteData: Note = this.mode === 'new' ? this.noteStore.getNewNote() : this.data.value;
    const updatedNote = { ...noteData, ...this.noteForm.value };

    if (this.mode === 'new') {
      this.noteStore.addNote(updatedNote);
    } else if (this.mode === 'edit') {
      this.noteStore.updateNotes([updatedNote]);
    }
    this.cc.isLoading = false;
    this.dialogRef.close();
  }

  toggleFavourite() {
    const updatedNote = { ...this.data.value, isFav: !this.data.value.isFav };
    this.noteStore.updateNotes([updatedNote]);
  }

  async togglePublic() {
    console.log("Public change event triggered:");
    const noteData: Note = this.data.value;
    const updatedNote = { ...noteData, isPublic: !noteData.isPublic };
    if (updatedNote.isPublic === true && (!updatedNote.publicId || !updatedNote.inviteLink)) {
      this.cc.isLoading = true;
      const payload = this.getCollaborationPayload(updatedNote);
      try {
        const response = await this.fbfs.callableFunction('collaboration-updateListWithShortLink', payload, true) as any;
        updatedNote.publicId = response.publicId;
        updatedNote.inviteLink = response.inviteLink;
      } catch (error) {
        return
      } finally {
        this.cc.isLoading = false;
      }
    }

    updatedNote.encryptionData = {
      dek: updatedNote.encData?.dek || '',
      encFields: updatedNote.isPublic
        ? ['members.membersConfig{}.eEmail']
        : this.noteStore.getNewNote().encryptionData?.encFields || []
    };
    this.noteStore.updateNotes([updatedNote]);
    this.noteInitial.isPublic = updatedNote.isPublic;
    this.noteForm.get('isPublic')?.setValue(updatedNote.isPublic);
  }

  private getCollaborationPayload(entityData: Note) {
    return {
      id: entityData.id,
      ownerNameOrEmail: entityData.ownerEmail,
      collection: FirestoreCollection.Notes,
    };
  }

  viewQrCode() {
    const qrCodeDialog = this.dialog.open(QrCodeDialogComponent, {
      width: '100%',
      maxWidth: '500px',
      data: {
        title: this.noteForm.value.title,
        type: 'notes',
        link: this.data.value.inviteLink
      }
    });
  }

  deleteNote() {
    this.noteStore.deleteNote([this.data.value]);
    this.dialogRef.close();
  }

  openHashtagsDialog() {
    const dialog = this.dialog.open(HashtagComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        values: this.noteForm.value.tags,
        type: 'map'
      },
    })

    dialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.noteForm.get('tags')?.setValue(result);
      }
    });
  }

  addCollaborator() {
    const confirmDialog = this.dialog.open(CollaboratorComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      data: {
        collection: 'notes',
        entityData: this.data.value,
        isPublic: true,
      },
    });
    return confirmDialog.afterClosed();
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm('Save Changes?', 'You have some unsaved changes', 'SAVE', 'DISCARD', 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
        this.dialogRef.close();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
      this.dialogRef.close();
    } else {
      this.dialogRef.close();
    }
  }

  reset() {
    this.noteForm.reset();
    this.ntForm.resetForm();
    this.noteForm.patchValue(this.noteInitial);
  }

  copyToClipboard() {
    const dynamicText = `${this.data.value.ownerName} shared a note to you\n\n${this.data.value.title}\n\n${this.data.value.description ? this.ds.extractString(this.data.value.description) + '\n\n' : ''}Created with Mevolve.app`;
    navigator.clipboard.writeText(dynamicText).then(() => {
    }).catch(err => {
      console.error('Failed to copy text:', err);
    });
  }

  downloadImage() {
    const datePipe = new DateFormatPipe();
    const date = datePipe.transform(new Date(), 'dd MMM y', this.cc.language());

    const wrapper = document.createElement('div');
    wrapper.style.position = 'absolute';
    wrapper.style.left = '-9999px';
    wrapper.style.top = '0';
    wrapper.style.width = '600px'; // ensure width is consistent
    document.body.appendChild(wrapper);

    const container = document.createElement('div');
    container.style.width = '100%';
    container.style.backgroundColor = this.cc.theme.color5;
    container.style.borderRadius = '8px';
    container.style.overflow = 'hidden';
    container.style.boxSizing = 'border-box';
    container.style.fontFamily = 'Roboto, sans-serif';
    container.style.color = this.cc.theme.color12;
    container.style.paddingBottom = '36px';

    const header = document.createElement('h6');
    header.innerText = this.data.value.title;
    header.style.font = '18px/24px Roboto, sans-serif';
    header.style.padding = '16px';
    header.style.paddingTop = '8px';
    header.style.color = this.cc.theme.color12;
    header.style.backgroundColor = this.cc.theme.color9;
    container.appendChild(header);

    const content = document.createElement('p');
    content.innerText = this.ds.extractString(this.data.value.description);
    content.style.font = '14px/20px Roboto, sans-serif';
    content.style.padding = '16px';
    content.style.color = this.cc.theme.color12;
    container.appendChild(content);

    const footer = document.createElement('p');
    footer.innerText = `${this.data.value.ownerName}, ${date}\nMevolve.app`;
    footer.style.font = '12px/16px Roboto, sans-serif';
    footer.style.textAlign = 'center';
    footer.style.color = this.cc.theme.color7;
    footer.style.paddingTop = '16px';
    container.appendChild(footer);

    // Append container to wrapper
    wrapper.appendChild(container);

    // Render with html2canvas
    html2canvas(wrapper, { backgroundColor: null }).then(canvas => {
      document.body.removeChild(wrapper);
      const link = document.createElement('a');
      link.download = 'note.png';
      link.href = canvas.toDataURL();
      link.click();
    });
  }

  ngOnDestroy() {
    this.unSubscribe?.complete();
    this.unSubscribe?.next();
  }

}

<button class="btn btn-filter btn-toggle h-full" [matMenuTriggerFor]="inputMoodMenu" [ngClass]="{ 'active': signal().length > 0 }">
    <app-svg name="smiley" [color]="signal().length ? cc.theme.color12 : cc.theme.color7" class="pe-1" style="height: 20px; width: 20px;"></app-svg>
    <span>{{ getLabel() }}</span>
    <div class="clear-icon ms-2" *ngIf="signal().length > 0" role="button" (click)="signal.set([]);$event.stopPropagation()">
        <app-svg class="d-flex align-items-center justify-content-center" name="close" [color]="cc.theme.color1"></app-svg>
    </div>
</button>
<mat-menu #inputMoodMenu="matMenu" class="me-menu ri-w-350px">
    <button mat-menu-item *ngFor="let mood of moods" (click)="onMoodSelected(mood, $event)">
        <div class="flex items-center justify-between">
            <span class="text-14-400 color-8 flex items-center">
                <app-svg [name]="moodEmojiMap()[mood]" [color]="cc.theme.color22" class="ri-pe-4"></app-svg>{{moodMap()[mood]}}
            </span>
            <app-svg *ngIf="multiple" name="tick" [color]="signal().includes(mood) ? cc.theme.color35 : cc.theme.color10"></app-svg>
        </div>
    </button>
</mat-menu>
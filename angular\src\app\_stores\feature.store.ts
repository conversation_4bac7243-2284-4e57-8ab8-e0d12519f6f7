import { signalStore, withComputed, withState, withMethods, patchState } from "@ngrx/signals";
import { computed, inject, signal, WritableSignal } from "@angular/core";
import { UserStore } from "./user.store";
import { DependencyService } from "@app/_services/dependency.service";
import { JournalStore } from "./journal.store";
import { CalendarEntitySetup, CalendarEventEntity, EntitySetup, FeatureSetup } from "@app/_interfaces/feature.interface";
import { defaultFeatures, premiumFeatures } from "@app/_datas/features.data";
import { UtilsService } from "@app/_services/utils.service";
import { TodoStore } from "./todo.store";
import { getDateString } from "@app/_utils/utils";
import { MoneyTrackerStore } from "./money-tracker.store";
import { CalendarIntegrationStore } from "./calendar-integration.store";
import { HabitStore } from "./habit.store";
import { EntityNameType, FeatureStatus } from "@app/_types/generic.type";
import { ListStore } from "./list.store";
import { NoteStore } from "./note.store";
import { MapService } from "@app/_services/map.service";

type FeatureState = {
    features: FeatureSetup[];
    activeFeatures: FeatureSetup[];
    premiumFeatures: FeatureSetup[];
    isLoading: boolean;
    firstEntityDateString: string | null;
    filter: { dateString: string, onlyCompleted: boolean, status: WritableSignal<FeatureStatus> };
};

const initialState: FeatureState = {
    features: [],
    activeFeatures: [],
    premiumFeatures: premiumFeatures,
    isLoading: false,
    firstEntityDateString: null,
    filter: { dateString: getDateString(), onlyCompleted: false, status: signal<FeatureStatus>('active') }
};

export const FeatureStore = signalStore(
    { providedIn: 'root' },
    withState(initialState),

    withComputed((
        store,
        userStore = inject(UserStore),
        listStore = inject(ListStore),
        noteStore = inject(NoteStore),
        todoStore = inject(TodoStore),
        journalStore = inject(JournalStore),
        habitStore = inject(HabitStore),
        moneyTrackerStore = inject(MoneyTrackerStore),
        calendarStore = inject(CalendarIntegrationStore),
        ds = inject(DependencyService)
    ) => ({

        storeMap: computed<{ [key in EntityNameType]: any }>(() => {
            return {
                todo: todoStore,
                habit: habitStore,
                journal: journalStore,
                moneyTracker: moneyTrackerStore,
                calendarIntegration: calendarStore,
                list: listStore,
                note: noteStore
            };
        }),

        features: computed<FeatureSetup[]>(() => {
            const settings = userStore.viewSettings();
            const status = store.filter().status();
            const defaultOnes = defaultFeatures.map(f => ({
                ...f,
                disabled: f.name === 'todo' && settings.featureSettings.showTodoFeature ? true
                    : f.name === 'note' && settings.featureSettings.showNoteFeature ? true
                        : f.name === 'list' && settings.featureSettings.showListFeature ? true
                            : false
            }));
            const features = [...(defaultOnes.filter(f => f.status === status))];
            const habitSetups = habitStore.activeSetups();
            const journalSetups = journalStore.activeSetups();
            const moneyTrackerSetups = moneyTrackerStore.activeSetups();
            const calendarintegrations = calendarStore.activeAccounts();

            habitSetups.forEach(habitSetup => {
                const habitFeature = habitStore.getHabitFeature(habitSetup)
                if (habitFeature.status === status) {
                    features.push(habitFeature);
                }
            });
            journalSetups.forEach(journalSetup => {
                const journalFeature = journalStore.getJournalFeature(journalSetup);
                if (journalFeature.status === status) {
                    features.push(journalFeature);
                }
            });
            moneyTrackerSetups.forEach(moneyTrackerSetup => {
                const moneyTrackerFeature = moneyTrackerStore.getMoneyTrackerFeature(moneyTrackerSetup);
                if (moneyTrackerFeature.status === status) {
                    features.push(moneyTrackerFeature);
                }
            });
            calendarintegrations.forEach(calendarintegration => {
                const calendarFeature = calendarStore.getCalendarFeature(calendarintegration);
                if (calendarFeature.status === status) {
                    features.push(calendarFeature);
                }
            });

            return features;
        }),
        calendarEventEntities: computed<CalendarEventEntity[]>(() => {
            const entities: CalendarEventEntity[] = [];
            entities.push(...todoStore.calendarEventEntities());
            entities.push(...journalStore.calendarEventEntities());
            return entities;
        }),
        entities: computed<{ withTime: EntitySetup[], withoutTime: EntitySetup[], calenderEvents: CalendarEntitySetup[] }>(() => {
            const date = store.filter.dateString();
            const hideCompleted = userStore.viewSettings().featureSettings.hideCompletedItems;
            const computedHabits = habitStore.getComputedEntities(date, hideCompleted);
            const computedJournals = journalStore.getComputedEntities(date, hideCompleted);
            const computedTodos = userStore.viewSignals()['showTodoFeature']() ? todoStore.getComputedEntities(date, hideCompleted) : { withTime: [], withoutTime: [], calenderEvents: [] };
            const computedMoneyTracker = moneyTrackerStore.getComputedEntities(date);
            const computedCalendarEvents = calendarStore.getComputedEntities(date);

            const entityList = {
                withTime: [...computedTodos.withTime, ...computedHabits.withTime, ...computedJournals.withTime, ...computedCalendarEvents.withTime].sort((a, b) => {
                    const [aHours, aMinutes] = a.startAt.timeString.split(":").map(Number);
                    const [bHours, bMinutes] = b.startAt.timeString.split(":").map(Number);

                    return aHours * 60 + aMinutes - (bHours * 60 + bMinutes);
                }),
                withoutTime: [...computedTodos.withoutTime, ...computedHabits.withoutTime, ...computedJournals.withoutTime, ...computedMoneyTracker.withoutTime],
                calenderEvents: [...computedTodos.calenderEvents, ...computedHabits.calenderEvents, ...computedJournals.calenderEvents, ...computedCalendarEvents.calenderEvents],
            };
            return entityList;
        }),
        activeEntities: computed<EntityNameType[]>(() => {
            const entities: EntityNameType[] = [];
            const isTodoEnabled = userStore.viewSignals()['showTodoFeature']();
            const isHabitEnabled = habitStore.enabledSetups().length > 0;
            const isJournalEnabled = journalStore.enabledSetups().length > 0;
            const isMoneyTrackerEnabled = moneyTrackerStore.enabledSetups().length > 0;
            const isCalendarIntegrationEnabled = calendarStore.enabledAccounts().length > 0;
            if (isTodoEnabled) {
                entities.push('todo');
            }
            if (isHabitEnabled) {
                entities.push('habit');
            }
            if (isJournalEnabled) {
                entities.push('journal');
            }
            if (isMoneyTrackerEnabled) {
                entities.push('moneyTracker');
            }
            if (isCalendarIntegrationEnabled) {
                entities.push('calendarIntegration');
            }
            return entities;
        }),
        completedEntities: computed<EntitySetup[]>(() => {
            const date = store.filter.dateString();
            const entityList: EntitySetup[] = [];
            // const completedTodos = todoStore.getCompletedTodos(date).entities;
            // const entityList = [...completedJournals, ...completedTodos]
            return entityList;
        }),
    })),
    withMethods((
        store,
        userStore = inject(UserStore),
        utilsService = inject(UtilsService),
        mapService = inject(MapService)
    ) => ({

        filterDate: (dateString: string) => {
            patchState(store, (state) => ({ filter: { ...state.filter, dateString } }));
        },

        groupByFeature: () => {
            const allEntities = [...store.entities().withTime, ...store.entities().withoutTime];

            const groupedMap = allEntities.reduce(
                (acc, entity) => {
                    const groupedMap = acc;
                    const id = entity.entityName;
                    if (!groupedMap.has(id)) {
                        groupedMap.set(id, {
                            id,
                            name: mapService.entityNameMap()[id],
                            data: []
                        });
                    }
                    groupedMap.get(id)!.data.push(entity);
                    return acc;
                },
                new Map<string, { id: string; name: string; data: EntitySetup[] }>()
            );

            const groupedData = [...groupedMap.values()];

            return groupedData;
        },

        findClosestTime(currentTime: string = '') {
            const timeStrings = store.entities().withTime.map(entity => entity.startAt.timeString);

            if (timeStrings.length === 0) {
                return "00:00";
            }

            // Get current time if not provided
            if (!currentTime) {
                const now = new Date();
                const hours = now.getHours().toString().padStart(2, '0');
                const minutes = now.getMinutes().toString().padStart(2, '0');
                currentTime = `${hours}:${minutes}`;
            }

            // Convert time string to minutes for easier comparison
            function timeToMinutes(timeStr: string) {
                const [hours, minutes] = timeStr.split(':').map(Number);
                return hours * 60 + minutes;
            }

            const currentMinutes = timeToMinutes(currentTime);

            // Convert all time strings to minutes and sort them
            const timeData = timeStrings
                .map(timeStr => ({
                    original: timeStr,
                    minutes: timeToMinutes(timeStr)
                }))
                .sort((a, b) => a.minutes - b.minutes);

            // If current time is less than all time strings, return "00:00"
            if (currentMinutes < timeData[0].minutes) {
                return "00:00";
            }

            // If current time is greater than all time strings, return the greatest
            if (currentMinutes >= timeData[timeData.length - 1].minutes) {
                return timeData[timeData.length - 1].original;
            }

            // Find the closest previous time string
            let closestPrevious = "00:00";

            for (let i = 0; i < timeData.length; i++) {
                if (timeData[i].minutes <= currentMinutes) {
                    closestPrevious = timeData[i].original;
                } else {
                    break;
                }
            }

            return closestPrevious;
        }
    })),
);

<div class="unscheduled-events flex">
  <div class="unscheduled-event" (click)="handleUnScheduledEvent(event)" *ngFor="let event of withoutTimeEvents" role="button">
    <div class="event-title">{{ event.title }}</div>
    <div class="event-checkmark {{ event.status }}"></div>
  </div>
</div>
<mwl-calendar-day-view class="today-cal-day-view" [viewDate]="viewDate" [events]="events" [eventTemplate]="customEventTemplate">

</mwl-calendar-day-view>
<ng-template #customEventTemplate let-weekEvent="weekEvent">
  <div class="cal-event" (click)="handleScheduledEvent(weekEvent.event)" role="button" [ngClass]="weekEvent.event.meta.entity">
    <div class="event-title">{{ weekEvent.event.title }}</div>
    <div class="event-checkmark {{ weekEvent.event.cssClass }}" [ngClass]="{'invalid': weekEvent.event.meta.invalid}"></div>
  </div>
</ng-template>
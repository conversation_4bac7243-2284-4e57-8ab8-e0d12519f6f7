import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-loader',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './loader.component.html',
  styleUrl: './loader.component.scss'
})

export class LoaderComponent {

  @Input() isSmall: boolean = false;
  @Input() isLoading: boolean = true;
  @Input() loaderText: string | null = null;

  hide() {
    this.isLoading = false;
  }
}

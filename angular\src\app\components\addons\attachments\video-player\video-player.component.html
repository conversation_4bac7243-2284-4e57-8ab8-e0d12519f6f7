<vg-player *ngIf="source; else noVideo" (click)="$event.stopPropagation()">
    <div class="video-wrapper" [style.transform]="containerTransform" #videoContainer>
        <vg-overlay-play></vg-overlay-play>
        <vg-buffering></vg-buffering>
        <video class="rotatable-video" id="attachmentVideoPlayer" #vgVideoPlayer [vgMedia]="$any(vgVideoPlayer)"  preload="auto" (timeupdate)="onTimeUpdate($event)" (click)="preventToggle($event)" [style.transform]="videoRotation">
            <source [src]="source" [type]="'video/' + attachmentType">
        </video>

        <vg-controls class="flex-col custom-controls" [vgAutohide]="true" [vgAutohideTime]="1.5">
            <div class="flex">
                <vg-time-display vgProperty="current" vgFormat="mm:ss"></vg-time-display>
                <div class="custom-progress-container">
                    <vg-scrub-bar #scrubBar vgFor="attachmentVideoPlayer" [vgSlider]="true" (click)="seekVideo($event)">
                        <vg-scrub-bar-current-time vgFor="attachmentVideoPlayer"></vg-scrub-bar-current-time>
                        <vg-scrub-bar-buffering-time vgFor="attachmentVideoPlayer"></vg-scrub-bar-buffering-time>
                    </vg-scrub-bar>
                </div>

                <vg-time-display vgProperty="total" vgFormat="mm:ss"></vg-time-display>
            </div>

            <div class="flex ps-1">
                <app-svg name="rotate" (click)="rotateVideo()" role="button"></app-svg>
                <app-svg name="download" class="ms-2" (click)="downloadEvent.emit()" role="button"></app-svg>
                <app-svg name="trash" class="ms-2" (click)="deleteEvent.emit()" role="button"></app-svg>
            </div>

        </vg-controls>
    </div>
</vg-player>

<ng-template #noVideo>
    <div class="flex h-full w-full items-center justify-center">
        <p class="mb-0 color-8">{{ cc.texts()['screen_common_recordsNotFound'] }}</p>
    </div>
</ng-template>
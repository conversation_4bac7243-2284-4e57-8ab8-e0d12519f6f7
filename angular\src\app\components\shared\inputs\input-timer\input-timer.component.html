<div class="flex items-start w-full" [ngClass]="{'readonly': readonly}" (click)="openTimerDialog()" role="button">
    <app-svg name="target" [color]="cc.theme.color35" class="ri-pe-4" *ngIf="!hideIcon"></app-svg>
    <div>
        <p class="text-14-400 mb-0" [ngClass]="{ 'color-8': duration, 'color-7': !duration }">
            <span *ngIf="duration" class="flex items-center">
                <span *ngIf="duration.hour" class="ri-pe-1 block">{{duration.hour}}h</span>
                <span *ngIf="duration.minute" class="ri-pe-1 block">{{duration.minute}}m</span>
                <span *ngIf="duration.second" class="ri-pe-1 block">{{duration.second}}s</span>
                <span>x {{ durationRepeatCount.value }}</span>
            </span>
            <span *ngIf="!duration">{{placeholder}}</span>
        </p>
        <p class="text-12-400 color-7 mb-0 ri-pt-2" *ngIf="duration">{{ getRepeatText() }}</p>
    </div>
</div>
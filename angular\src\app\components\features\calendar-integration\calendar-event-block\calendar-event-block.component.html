<div class="flex justify-between items-center" [ngClass]="blockClass" (click)="openCalendarEventForm()" role="button" *ngIf="event">
    <div class="">
        <p class="ri-pb-2 mb-0" *ngIf="show.includes('label') && isLabel"><span class="entity-badge text-12-400 color-7" >{{ cc.texts()['screen_today_labelCalendar'] }} - {{ event().calendarName }}</span></p>
        <p class="text-16-400 color-8 mb-0">{{ event().title }}</p>
        <p class="text-12-400 color-7 mb-0 ri-pt-2" *ngIf="descriptionType !== 'none' && event().description">{{ event().description | parseText: descriptionType === 'short' ? 'short' : 'full' }}</p>
        <div class="mb-0 ri-pt-2"
            *ngIf="(show.includes('time') || show.includes('reminder') || show.includes('duration')) && event().startAt.timeString !== '00:00' || (show.includes('attachments') && event().attachments.length > 0)">
            <div class="inline-flex items-center ri-pe-3" *ngIf="show.includes('time') && event().startAt.timeString !== '00:00'">
                <app-svg name="clock" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7">{{ event().startAt.timeString | parseTime }}</span>
            </div>
            <div class="inline-flex items-center ri-pe-3"
                *ngIf="show.includes('reminder') && event().reminderAt.length > 0 && event().startAt.timeString !== '00:00'">
                <app-svg name="bell" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7"><span *ngFor="let reminder of event().reminderAt;let i = index;">{{ ds.getReminderText(reminder) }}<span *ngIf="i < event().reminderAt.length - 1">, </span></span></span>
            </div>
            <div class="inline-flex items-center ri-pe-3" *ngIf="show.includes('duration') && event().startAt.timeString !== '00:00'">
                <app-svg name="timer" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7">{{ event().duration | parseMinutes }}</span>
            </div>
            <div class="inline-flex items-center ri-pe-3"
                *ngIf="show.includes('attachments') && event().attachments.length > 0">
                <app-svg name="attachment" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7">{{ event().attachments.length }}</span>
            </div>
        </div>
    </div>
    <div>
        <app-svg name="calendarIntegration" [color]="cc.theme.color7"></app-svg>
    </div>
</div>
<div class="me-date-period-picker">
    <mat-calendar [selected]="selectedDate" [minDate]="minDate" [maxDate]="maxDate"
        (selectedChange)="startDateSelected($event)"></mat-calendar>

    <div class="flex justify-between ri-pt-5">
        <p class="text-16-400 color-8 mb-0">{{ cc.texts()['screen_common_repeat'] }}</p>
        <app-input-dropdown [selectedMark]="'TICK'" [config]="repeatTypeConfig" [signal]="repeatType"
            (select)="repeatTypeChange()" [hiddenValues]="data.hiddenValues"></app-input-dropdown>
    </div>
    <div class="flex justify-between ri-pt-4" *ngIf="repeatType() === 'MONTHLY_NTH_DAY'">
        <p class="text-16-500 color-8 mb-0">{{ cc.texts()['overlay_repeatSelect_week'] }}</p>
        <app-input-dropdown [config]="ordinalConfig" [signal]="weekSignal" [multiple]="true"
            (select)="updateWeek()"></app-input-dropdown>
    </div>
    <div class="flex justify-between ri-pt-4" *ngIf="repeatType() === 'WEEKLY' || repeatType() === 'MONTHLY_NTH_DAY'">
        <p class="text-16-500 color-8 mb-0">{{ cc.texts()['overlay_repeatSelect_yearlyDaySelectTitle'] }}</p>
        <app-input-dropdown [config]="dayConfig" [signal]="daySignal" [multiple]="true"
            (select)="updateDay()"></app-input-dropdown>
    </div>
    <div class="flex justify-between ri-pt-4"
        *ngIf="repeatType() === 'MONTHLY' || repeatType() === 'MONTHLY_LAST_DAY' || repeatType() === 'YEARLY'">
        <p class="text-16-500 color-8 mb-0">{{ cc.texts()['overlay_repeatSelect_yearlyMonthSelectTitle'] }}</p>
        <p class="text-16-500 color-35 mb-0" (click)="selectMonth()" role="button">May</p>
    </div>
    <div class="flex justify-between ri-pt-4" *ngIf="repeatType() === 'MONTHLY' || repeatType() === 'YEARLY'">
        <p class="text-16-500 color-8 mb-0">{{ cc.texts()['overlay_repeatSelect_yearlyDaySelectTitle'] }}</p>
        <p class="text-16-500 color-35 mb-0" role="button" (click)="selectDate()">1,2,3</p>
    </div>
    <div class="flex justify-between ri-pt-4" *ngIf="repeatType() !== 'OFF'">
        <app-input-dropdown [config]="endDateRepeatModeConfig()" [signal]="endDateRepeatMode"
            (select)="updateEndMode()"></app-input-dropdown>
        <input *ngIf="ruleRecord['COUNT']" class="occurance-count-input" type="text" id="occuranceCount"
            name="occuranceCountControl" [formControl]="occuranceCount" (input)="updateOccuranceCount()" [onlyNumbers]="true" maxlength="5" minlength="1">
        <p class="text-16-400 color-35 mb-0" role="button" *ngIf="!ruleRecord['COUNT']" (click)="openInputCalendar()">{{
            endDate ? (endDate.dateTime | date:'d MMM y') : cc.texts()['overlay_datePicker_neverEnds'] }}</p>
    </div>
</div>
<div class="bottom-section flex align-items-end justify-end ri-p-4 ri-bt-2">
    <button class="btn-text text-16-500 color-7 me-4" (click)="closeDialog()">{{ cc.texts()['screen_common_buttonCancel'] }}</button>
    <button type="submit" class="btn-text text-16-500 check-disabled color-35" [disabled]="!hasChanges()" (click)="save()">{{ cc.texts()['screen_common_save'] }}</button>
</div>
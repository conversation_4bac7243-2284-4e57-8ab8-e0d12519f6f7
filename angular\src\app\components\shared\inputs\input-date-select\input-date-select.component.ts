import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AlertService } from '@app/_services/alert.service';
import { Subject, takeUntil } from 'rxjs';
import * as _ from 'lodash';
import { CacheService } from '@app/_services/cache.service';

@Component({
  selector: 'app-input-date-select',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './input-date-select.component.html',
  styleUrl: './input-date-select.component.scss'
})

export class InputDateSelectComponent {

  unSubscribe = new Subject<void>();
  initialDates: string[] = [];
  selectedDates: string[] = [];
  min: number = 0;
  dates: string[] = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31"];

  constructor(
    public dialogRef: MatDialogRef<InputDateSelectComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { value: string[], min: number },
    private alertService: AlertService,
    public cc: CacheService
  ) {

    this.initialDates = this.data.value;
    this.selectedDates = this.data.value;
    this.min = this.data.min ? this.data.min : this.min;

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  selectDate(selectedDate: string) {
    const selectedValues = this.selectedDates;
    const updatedValues = selectedValues.includes(selectedDate) ? (selectedValues.length === this.data.min ? selectedValues : selectedValues.filter((date: string) => date !== selectedDate)) : [...selectedValues, selectedDate];
    this.selectedDates = updatedValues;
  }

  hasChanges(): boolean {
    const initial = _.cloneDeep(this.initialDates);
    const current = _.cloneDeep(this.selectedDates);
    if (initial?.length !== current?.length) return true;
    initial.sort((a: string, b: string) => (a > b ? 1 : -1));
    current.sort((a: string, b: string) => (a > b ? 1 : -1));
    return !_.isEqual(initial, current);
  }

  save() {
    this.dialogRef.close([this.selectedDates]);
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
        this.dialogRef.close();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }
}

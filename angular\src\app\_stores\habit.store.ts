import { signalStore, withComputed, withState, withMethods, withHooks, patchState } from "@ngrx/signals";
import { computed, inject } from "@angular/core";
import { UserStore } from "./user.store";
import { DependencyService } from "@app/_services/dependency.service";
import { UtilsService } from "@app/_services/utils.service";
import { IndexDbService } from "@app/_services/index-db.service";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { Habit, HabitSetup } from "@app/_interfaces/habit.interface";
import { CalendarEntitySetup, CalendarEventEntity, EntitySetup, FeatureSetup } from "@app/_interfaces/feature.interface";
import { dbVersion } from "@app/_configs/db.config";
import { FirestoreCollection } from "@app/_enums/firestore-collection.enum";
import { CryptographyService } from "@app/_services/cryptography.service";
import { FirebaseFunctionService } from "@app/_services/firebase-function.service";
import { firstValueFrom } from "rxjs";
import { getCustomDate, getDateString, getTodayDateWithoutTime } from "@app/_utils/utils";
import { DayCode } from "@app/_types/generic.type";

type HabitState = {
    habits: Habit[];
    setups: HabitSetup[];
    isLoading: boolean;
    filter: { date: Date };
    selectedHabitSetupIds: string[];
};

const initialState: HabitState = {
    habits: [],
    setups: [],
    isLoading: false,
    filter: { date: new Date() },
    selectedHabitSetupIds: []
};

export const HabitStore = signalStore(
    { providedIn: 'root' },
    withState(initialState),

    withComputed((
        store,
        userStore = inject(UserStore),
        ds = inject(DependencyService)
    ) => ({
        activeSetups: computed<HabitSetup[]>(() => {
            return store.setups().filter(setup => !setup.deletedAt);
        }),
        enabledSetups: computed<HabitSetup[]>(() => {
            return store.setups().filter(setup => setup.isPaused === false && !setup.deletedAt);
        }),
        activeHabits: computed<Habit[]>(() => {
            return store.habits().filter(habit => !habit.deletedAt);
        }),
        firstDateString: computed<string>(() => {
            let todayDateString = getDateString();
            const setups = store.setups();
            if (!setups || setups.length === 0) {
                return todayDateString;
            }

            return setups.reduce(
                (acc, setup) => {
                    if (setup.deletedAt || !setup.startAt || setup.isPaused === true) return acc;
                    const dateStr = setup.startAt.dateString;

                    if (!acc || (acc && dateStr < acc)) {
                        acc = dateStr;
                    }

                    return acc;
                },
                todayDateString
            );
        }),
        lastDateString: computed<string | null>(() => {
            const setups = store.setups();
            const todayDateString = getDateString();

            if (!setups || setups.length === 0) {
                return null;
            }

            let lastDate: string | null = null;

            for (const setup of setups) {
                const date = setup.endAt?.dateString ?? null;

                if (setup.deletedAt || !setup.startAt || setup.isPaused === true || (date && date > todayDateString)) continue;

                if (date === null) return null;

                if (!lastDate || date > lastDate) {
                    lastDate = date;
                }
            }

            return lastDate;
        }),
        idToSetup: computed<Record<string, HabitSetup>>(() => {
            const idToHabitSetup: Record<string, HabitSetup> = {};
            store.setups().forEach(setup => {
                idToHabitSetup[setup.id] = setup;
            });
            return idToHabitSetup;
        }),
        selectedSetups: computed(() => {
            const ids = store.selectedHabitSetupIds();
            return new Map(
                [...ids].map(id => [id, store.setups().find(setup => setup.id === id) || null])
            );
        }),
        idToHabit: computed<Record<string, Habit>>(() => {
            const idToHabit: Record<string, Habit> = {};
            store.habits().forEach(habit => {
                idToHabit[habit.id] = habit;
            });
            return idToHabit;
        }),
        habitMap: computed<{ setups: Record<string, Record<string, Habit>> }>(() => {
            const setups: Record<string, Record<string, Habit>> = {};

            store.habits().forEach(item => {
                const setupId = item.setupId;
                const dateString = item.dueAt?.dateString;

                if (!setupId || !dateString || item.deletedAt) return;

                if (!setups[setupId]) {
                    setups[setupId] = {};
                }

                setups[setupId][dateString] = item;
            });

            return { setups };
        }),
        calendarEventEntities: computed<CalendarEventEntity[]>(() => {
            const entities: CalendarEventEntity[] = [];
            store.habits().forEach(entity => {
                entities.push({
                    id: entity.id,
                    data: entity,
                    localUpdatedAt: entity.localUpdatedAt,
                    updateType: 'raw',
                    isCreate: true
                });
            });
            return entities;
        }),
        ruleMap: computed<{
            oneDay: { [key in string]: EntitySetup[] },
            daily: EntitySetup[],
            weekly: { [key in DayCode]: EntitySetup[] },
            monthly: { [key in string]: { [key in string]: EntitySetup[] } },
            ordinally: { [key in string]: { [key in DayCode]: EntitySetup[] } },
        }>(() => {
            const { oneDay, daily, weekly, monthly, ordinally } = ds.computeRule(store.setups(), 'habit');
            return { oneDay, daily, weekly, monthly, ordinally };
        }),
    })),
    withMethods((
        store,
        userStore = inject(UserStore),
        utilsService = inject(UtilsService),
        cryptoService = inject(CryptographyService),
        firebaseFunctionService = inject(FirebaseFunctionService),
        idbService = inject(IndexDbService),
        ds = inject(DependencyService)
    ) => ({

        addSetup: async (habitSetup: HabitSetup) => {
            const habit: HabitSetup = habitSetup;
            await idbService.add('habitSetups', habitSetup);

            // Upload data to the backend
            const syncRequest = cryptoService.prepareRawData({ ...habit });
            await firebaseFunctionService.uploadData(syncRequest);
        },

        updateSetups: async (habitSetups: HabitSetup[]) => {
            const oldHabits = [];
            const newHabits = [];
            for (const habit of habitSetups) {
                const oldHabit = await firstValueFrom(idbService.getEntityById('habitSetups', habit.id));
                const newHabit = await idbService.update('habitSetups', habit.id, habit);
                oldHabit!.localUpdatedAt = new Date();
                newHabit!.cloudUpdatedAt = null;
                newHabit!.lastUpdatedAt = new Date();
                oldHabits.push(oldHabit);
                newHabits.push(newHabit);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldHabits,
                newHabits,
                FirestoreCollection.HabitSetups
            );

            await firebaseFunctionService.uploadData(syncRequest);
            return true;
        },

        deleteSetups: async (habitSetups: HabitSetup[]) => {
            const oldHabits = [];
            const newHabits = [];
            for (const habit of habitSetups) {
                habit.deletedAt = new Date();
                const oldHabit = await firstValueFrom(idbService.getEntityById('habitSetups', habit.id));
                const newHabit = await idbService.update('habitSetups', habit.id, habit);
                oldHabit!.localUpdatedAt = new Date();
                newHabit!.cloudUpdatedAt = null;
                oldHabits.push(oldHabit);
                newHabits.push(newHabit);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldHabits,
                newHabits,
                FirestoreCollection.HabitSetups
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },

        addHabit: async (habit: Habit) => {
            const newHabit = await idbService.add('habitActions', habit);

            // Upload data to the backend
            const syncRequest = cryptoService.prepareRawData({ ...newHabit });
            await firebaseFunctionService.uploadData(syncRequest);
        },
        updateHabits: async (habits: Habit[]) => {
            const oldHabits = [];
            const newHabits = [];
            for (const habit of habits) {
                const oldHabit = await firstValueFrom(idbService.getEntityById('habitActions', habit.id));
                const newHabit = await idbService.update('habitActions', habit.id, habit);
                oldHabit!.localUpdatedAt = new Date();
                newHabit!.cloudUpdatedAt = null;
                newHabit!.lastUpdatedAt = new Date();
                oldHabits.push(oldHabit);
                newHabits.push(newHabit);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldHabits,
                newHabits,
                FirestoreCollection.HabitActions
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },
        deleteHabits: async (habits: Habit[]) => {
            const oldHabits = [];
            const newHabits = [];
            for (const habit of habits) {
                habit.deletedAt = new Date();
                const oldHabit = await firstValueFrom(idbService.getEntityById('habitActions', habit.id));
                const newHabit = await idbService.update('habitActions', habit.id, habit);
                oldHabit!.localUpdatedAt = new Date();
                newHabit!.cloudUpdatedAt = null;
                oldHabits.push(oldHabit);
                newHabits.push(newHabit);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldHabits,
                newHabits,
                FirestoreCollection.HabitActions
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },

        getComputedEntities: (dateString: string, filterCompleted: boolean = false, groupBy: 'date' | 'hashtag' = 'date'): { withTime: EntitySetup[], withoutTime: EntitySetup[], calenderEvents: CalendarEntitySetup[], groupedData: { [key in string]: EntitySetup[] } } => {
            const [day, weekday, nthWeek, month, isLastDay] = ds.parseDateInfo(dateString);
            let entityList: EntitySetup[] = [];
            const computedJournals = store.ruleMap();
            entityList = [...(computedJournals.oneDay[dateString] || []), ...computedJournals.daily, ...computedJournals.weekly[weekday], ...computedJournals.monthly[month][day], ...computedJournals.ordinally[nthWeek][weekday], ...(isLastDay ? computedJournals.monthly[month]['-1'] : [])];
            const entitiesWithTime: EntitySetup[] = [];
            const entitiesWithoutTime: EntitySetup[] = [];
            const calenderEvents: CalendarEntitySetup[] = [];
            const groupedData: { [key in string]: EntitySetup[] } = {};
            const today = getTodayDateWithoutTime();

            entityList.forEach(entity => {
                const startsBeforeOrOn = entity.startAt ? entity.startAt.dateString <= dateString : true;
                const endsAfterOrOn = entity.endAt ? entity.endAt.dateString >= dateString : true;
                const isInDateRange = startsBeforeOrOn && endsAfterOrOn;

                if (!isInDateRange) return;
                const clonedEntity = { ...entity };

                const habit = store.habitMap().setups[clonedEntity.id] && store.habitMap().setups[clonedEntity.id][dateString] || null;
                const setup = store.idToSetup()[clonedEntity.id] || null;
                if (!setup || setup.isPaused) return;
                let isCompleted = false;
                let isDraft = false;

                switch (setup.habitType) {
                    case 'boolean':
                        isCompleted = habit?.booleanAnswer === true || habit?.booleanAnswer === false;
                        break;
                    case 'numeric':
                        if (habit?.numericAnswer && habit.numericAnswer.length > 0 && setup.numericGoal) {
                            const total = habit.numericAnswer.reduce((acc, curr) => acc + curr.value, 0);
                            total >= setup.numericGoal ? isCompleted = true : isDraft = true;

                            if (isDraft && setup.numericGoal) {
                                const percentage = (total / setup.numericGoal) * 100;
                                clonedEntity.percentage = Math.floor(percentage);
                            }
                        } else {
                            isCompleted = false;
                        }
                        break;
                    case 'timer':
                        if (habit?.timerAnswer && habit.timerAnswer.length > 0 && setup.timerGoal) {
                            const goal = ds.parseMinutes(setup.timerGoal)!.totalSeconds * setup.durationRepeatCount;
                            const completedSeconds = ds.getTotalSecondsSpent(habit.timerAnswer);
                            completedSeconds >= goal ? isCompleted = true : isDraft = true;

                            if (isDraft && goal) {
                                const percentage = (completedSeconds / goal) * 100;
                                clonedEntity.percentage = Math.floor(percentage);
                            }
                        } else {
                            isCompleted = false;
                        }
                        break;
                    case 'single':
                        isCompleted = habit?.singleAnswer !== null;
                        break;
                    case 'multiple':
                        isCompleted = (habit?.multipleAnswer && habit.multipleAnswer.length > 0) || false;
                        break;
                    default:
                        break;
                }
                const isMissed = new Date(`${dateString}T${clonedEntity.startAt.timeString}:00`) < today && !isCompleted && !isDraft;
                clonedEntity.invalid = isMissed;
                clonedEntity.status = isCompleted ? 'completed' : isDraft ? 'draft' : isMissed ? 'missed' : 'none';

                if (filterCompleted && isCompleted) {
                    if (isCompleted) return;
                }

                const hasTime = clonedEntity.startAt && clonedEntity.startAt.timeString !== "00:00";
                if (hasTime) {
                    entitiesWithTime.push(clonedEntity);
                    const startDate = new Date(`${dateString}T${clonedEntity.startAt.timeString}:00`);
                    calenderEvents.push({
                        id: clonedEntity.id,
                        start: startDate,
                        end: new Date(startDate.getTime() + clonedEntity.duration * 60 * 1000),
                        title: clonedEntity.title,
                        cssClass: clonedEntity.status,
                        meta: {
                            entity: 'habit',
                            startAt: clonedEntity.startAt,
                            endAt: clonedEntity.endAt,
                            invalid: false
                        }
                    })
                } else {
                    entitiesWithoutTime.push(clonedEntity);
                }

                switch (groupBy) {
                    case 'date':
                        (groupedData[dateString] ??= []).push(clonedEntity);
                        break;
                    case 'hashtag':
                        const tags = [...clonedEntity.tags, ...(habit ? habit.tags : [])];
                        if (tags.length === 0) {
                            (groupedData[''] ??= []).push(clonedEntity);
                        } else {
                            tags.forEach(tag => {
                                const id = tag || '';
                                (groupedData[id] ??= []).push(clonedEntity);
                            });
                        }
                        break;
                }
            })
            return { withTime: entitiesWithTime, withoutTime: entitiesWithoutTime, calenderEvents, groupedData };
        },
        getNewHabit(): Habit {
            const user = userStore.user?.();
            return {
                id: utilsService.getNewId(),
                description: '',
                setupId: '',
                dueAt: getCustomDate(),
                booleanAnswer: null,
                numericAnswer: null,
                timerAnswer: null,
                singleAnswer: null,
                multipleAnswer: null,
                attachments: [],
                tags: [],
                members: { memberHashedEmails: [], membersConfig: {} },
                uid: user?.uid ?? '',
                ownerName: user?.name,
                ownerEmail: user?.email,
                docVer: dbVersion,
                docCollection: FirestoreCollection.HabitActions.toString(),
                encData: {
                    dek: cryptoService.createEncryptedDocKey(),
                    encFields: [
                        'description',
                        'ownerName',
                        'ownerEmail',
                        'lastUpdatedBy',
                        'members.membersConfig{}.eEmail',
                    ]
                },
                source: 'client',
                sessionId: utilsService.getNewId(),
                createdAt: new Date(),
                localUpdatedAt: new Date(),
                cloudUpdatedAt: new Date(),
                deletedAt: null,
                permaDeletedAt: null,
            }
        },

        getNewSetup(): HabitSetup {
            const user = userStore.user?.();
            return {
                id: utilsService.getNewId(),
                title: '',
                description: '',
                habitType: 'boolean',
                numericGoal: null,
                numericUnit: null,
                timerGoal: null,
                timerStopType: 'onRoundEnd',
                durationRepeatType: 0,
                durationRepeatCount: 1,
                habitOptions: [],
                isVibrationEnabled: true,
                alarmSoundType: 'mevolve_1',
                alarmSoundVolume: 100,
                tags: [],
                isPaused: false,
                startAt: getCustomDate(),
                endAt: null,
                isStartTimeSet: false,
                isTmzAffected: false,
                duration: 15,
                repeat: ["RRULE:FREQ=DAILY"],
                reminderAt: [],
                members: { memberHashedEmails: [], membersConfig: {} },
                uid: user?.uid ?? '',
                ownerName: user?.name,
                ownerEmail: user?.email,
                docVer: dbVersion,
                docCollection: FirestoreCollection.HabitSetups.toString(),
                encData: {
                    dek: cryptoService.createEncryptedDocKey(),
                    encFields: [
                        'title',
                        'habitOptions[].value',
                        'ownerName',
                        'ownerEmail',
                        'members.membersConfig{}.eEmail',
                    ]
                },
                source: 'client',
                sessionId: utilsService.getNewId(),
                createdAt: new Date(),
                localUpdatedAt: new Date(),
                cloudUpdatedAt: new Date(),
                deletedAt: null,
                permaDeletedAt: null,
            }
        },

        getHabitFeature(habitSetup: HabitSetup): FeatureSetup {
            const status = ds.getFeatureStatus(habitSetup.startAt.dateString, habitSetup.endAt?.dateString);
            return {
                id: habitSetup.id,
                name: habitSetup.habitType,
                icon: 'habit',
                entityName: 'habit',
                description: habitSetup.title,
                status: status,
                disabled: !habitSetup.isPaused,
                isDefault: false,
                startAt: habitSetup.startAt,
                endAt: habitSetup.endAt,
                entity: habitSetup
            };
        },
        selectSetup(id: string) {
            patchState(store, (state) => ({ selectedHabitSetupIds: [...state.selectedHabitSetupIds, id] }));
        }
    })),

    withHooks({
        async onInit(
            store,
            idbService = inject(IndexDbService)
        ) {
            idbService.habitSetups$.pipe(takeUntilDestroyed()).subscribe(data => {
                if (data) {
                    patchState(store, { setups: data });
                }
            });
            idbService.habits$.pipe(takeUntilDestroyed()).subscribe(data => {
                if (data) {
                    patchState(store, { habits: data });
                }
            });
        },
    }),
);

import { CommonModule } from '@angular/common';
import { Component, Inject, inject, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, FormsModule, NgForm, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { Todo, TodoAction } from '@app/_interfaces/todo.interface';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';
import { UtilsService } from '@app/_services/utils.service';
import { TodoStore } from '@app/_stores';
import { InputDatePeriodComponent } from '@app/components/shared/inputs/input-date-period/input-date-period.component';
import { InputHashtagComponent } from '@app/components/shared/inputs/input-hashtag/input-hashtag.component';
import { InputReminderComponent } from '@app/components/shared/inputs/input-reminder/input-reminder.component';
import { InputTextComponent } from '@app/components/shared/inputs/input-text/input-text.component';
import { InputTimeComponent } from '@app/components/shared/inputs/input-time/input-time.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { Subject, takeUntil } from 'rxjs';
import * as _ from 'lodash';
import { HashtagComponent } from '@app/components/addons/hashtags/hashtag/hashtag.component';
import { getCustomDate } from '@app/_utils/utils';
import { AttachmentListComponent } from '@app/components/addons/attachments/attachment-list/attachment-list.component';
import { Attachment } from '@app/_interfaces/generic.interface';
import { DependencyService } from '@app/_services/dependency.service';
import { FirebaseService } from '@app/_services/firebase.service';
import { InputFileComponent } from '@app/components/shared/inputs/input-file/input-file.component';
import { InputChecklistComponent } from '@app/components/shared/inputs/input-checklist/input-checklist.component';
import { CollaboratorComponent } from '@app/components/addons/collaborators/collaborator/collaborator.component';
import { InputCheckmarkAdvancedComponent } from '@app/components/shared/inputs/input-checkmark-advanced/input-checkmark-advanced.component';
import { notOnlyWhitespace } from '@app/_directives/form-validator.directive';

@Component({
  selector: 'app-todo-form',
  standalone: true,
  imports: [
    CommonModule,
    MatMenuModule,
    FormsModule,
    ReactiveFormsModule,
    InputTextComponent,
    InputHashtagComponent,
    InputDatePeriodComponent,
    SvgComponent,
    InputTimeComponent,
    InputReminderComponent,
    AttachmentListComponent,
    InputFileComponent,
    InputChecklistComponent,
    InputCheckmarkAdvancedComponent
  ],
  templateUrl: './todo-form.component.html',
  styleUrl: './todo-form.component.scss'
})

export class TodoFormComponent {

  @ViewChild('tdForm') tdForm!: NgForm;
  unSubscribe = new Subject<void>();
  todoForm: FormGroup;
  todoInitial: Todo;
  readonly todoStore = inject(TodoStore);
  isDescription: boolean = false;
  newAttachments: FormArray = this.fb.array([]);
  deletedAttachments: FormArray = this.fb.array([]);
  submitted: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<TodoFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'new' | 'edit', value: Todo, dateString: string },
    private fb: FormBuilder,
    public cc: CacheService,
    private alertService: AlertService,
    private utilsService: UtilsService,
    public dialog: MatDialog,
    private ds: DependencyService,
    private fbService: FirebaseService
  ) {
    this.todoInitial = data.mode == 'new' ? this.initiateForm() : this.initiateForm(data.value);
    console.log("todoInitial--->", this.todoInitial)

    if (this.todoInitial.description) {
      this.isDescription = true;
    }

    this.todoForm = this.fb.group({
      id: new FormControl(this.todoInitial.id, Validators.required),
      title: new FormControl(this.todoInitial.title, [Validators.required, Validators.maxLength(120), notOnlyWhitespace()]),
      description: new FormControl(this.todoInitial.description, [Validators.maxLength(500), notOnlyWhitespace()]),
      startAt: new FormControl(this.todoInitial.startAt),
      isStartTimeSet: new FormControl(this.todoInitial.isStartTimeSet),
      isTmzAffected: new FormControl(this.todoInitial.isTmzAffected),
      endAt: new FormControl(this.todoInitial.endAt),
      reminderAt: new FormControl(this.todoInitial.reminderAt),
      repeat: new FormControl(this.todoInitial.repeat),
      duration: new FormControl(this.todoInitial.duration),
      tags: new FormControl(this.todoInitial.tags),
      uid: new FormControl(this.todoInitial.uid, Validators.required),
      actions: new FormControl(this.todoInitial.actions),
      checkLists: this.fb.array([]),
      attachments: this.fb.array([]),
    });

    if (this.todoInitial.attachments && this.todoInitial.attachments.length !== 0) {
      this.clearAttachments();
      this.todoInitial.attachments.forEach((attach: Attachment) => {
        this.attachments.push(this.ds.addAttachmentForm(attach));
      });
    }

    if (this.todoInitial.checkLists && this.todoInitial.checkLists.length !== 0) {
      this.todoInitial.checkLists.forEach(checklist => {
        this.checkLists.push(this.ds.addChecklistForm(checklist));
      });
    }

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  initiateForm(todo?: Todo): Todo {
    return {
      id: todo ? todo.id : this.utilsService.getNewId(),
      title: todo ? todo.title : '',
      description: todo ? todo.description : '',
      startAt: todo ? todo.startAt : getCustomDate(),
      isStartTimeSet: todo ? todo.isStartTimeSet : false,
      isTmzAffected: todo ? todo.isTmzAffected : true,
      endAt: todo ? todo.endAt : null,
      reminderAt: todo ? todo.reminderAt : [],
      repeat: todo ? todo.repeat : [],
      duration: todo ? todo.duration : 15,
      tags: todo ? todo.tags : [],
      uid: this.cc.user.uid,
      actions: todo ? todo.actions : [],
      checkLists: todo ? todo.checkLists : [],
      attachments: todo ? todo.attachments : [],
    }
  }

  actionMap(): Record<string, TodoAction> {
    const actionMap: Record<string, TodoAction> = {};
    this.todoForm.value.actions.forEach((action: TodoAction) => {
      actionMap[action.dueAt] = action;
    });
    return actionMap;
  }

  actionInitial(): TodoAction | null {
    return this.todoInitial.actions.find((action: TodoAction) => action.dueAt === this.data.dateString) || null;
  }

  onActionChange() {
    const action: TodoAction | null = this.actionMap()[this.data.dateString] || null;
    const actionInitial: TodoAction | null = this.actionInitial();
    if ((action && action.isSkipped === false) || !action) {
      const newAction: TodoAction = {
        dueAt: this.data.dateString,
        isSkipped: action ? true : false,
        completedAt: actionInitial ? actionInitial.completedAt : new Date()
      };
      this.todoForm.get('actions')?.setValue([...this.todoForm.value.actions, newAction]);
    } else if (action && action.isSkipped === true) {
      this.todoForm.get('actions')?.setValue(this.todoForm.value.actions.filter((action: TodoAction) => action.dueAt !== this.data.dateString));
    }
  }

  get attachmentValues(): any[] {
    const attachments = [...this.attachments.value, ...this.newAttachments.value];
    return attachments;
  }

  get attachments(): FormArray {
    return this.todoForm.get('attachments') as FormArray || this.fb.array([]);
  }

  get checkLists(): FormArray {
    return this.todoForm.get('checkLists') as FormArray;
  }

  addChecklist() {
    this.checkLists.push(this.ds.addChecklistForm());
  }

  clearAttachments() {
    while (this.attachments.length !== 0) {
      this.attachments.removeAt(0)
    }
  }

  deleteAttachments(attachments: Attachment[]) {
    attachments?.forEach(attach => {
      const attachmentFormArray = attach.status === 'local' ? this.newAttachments : this.attachments;
      const index = attachmentFormArray.controls.findIndex(control => control.get('id')?.value === attach.id);
      if (index !== - 1) {
        attachmentFormArray.removeAt(index);
      }
      if (attach.status === 'cloud') {
        this.deletedAttachments.push(this.ds.addAttachmentForm(attach));
      }
    });
  }

  get basePath(): string {
    return `userData/attachments/${this.cc.user ? this.cc.user.uid : ''}/todos/${this.todoInitial.id}`;
  }

  getFc(fcName: string): FormControl {
    return this.todoForm.get(fcName) as FormControl;
  }


  addCollaborator() {
    const confirmDialog = this.dialog.open(CollaboratorComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      data: {
        collection: 'todos',
        entityData: this.data.value,
        isPublic: false,
      },
    });
    return confirmDialog.afterClosed();
  }

  openHashtagsDialog() {
    const dialog = this.dialog.open(HashtagComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        values: this.todoForm.value.tags,
        type: 'map'
      },
    })

    dialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.todoForm.get('tags')?.setValue(result);
      }
    });
  }

  hasChanges() {
    const initial = _.cloneDeep(this.todoInitial);
    const current = _.cloneDeep(this.todoForm.value);
    if (initial.attachments?.length !== current.attachments?.length || this.newAttachments.length > 0 || this.deletedAttachments.length > 0) return true;
    return !_.isEqual(initial, current);
  }

  async save() {
    this.submitted = true;
    if (!this.todoForm.valid) {
      this.alertService.alert(this.cc.texts()['overlay_noteActionMandatoryAlert_title'], this.cc.texts()['overlay_todoActionMandatoryAlert_title'], this.cc.texts()['screen_common_ok']);
      return;
    }
    this.removeInvalidChecklists();
    this.cc.isLoading = true;
    if (!this.todoForm.value.id) return;
    if (this.newAttachments.length > 0) {
      const attachmentsRecord: Record<number, { attachments: { path: string; file: Uint8Array }[]; dek?: string }> = {};
      this.newAttachments.value.forEach((attachment: Attachment) => {
        const originalPath = `${this.basePath}/originalFiles/${attachment.id}.${attachment.format}`;
        const files = [{ path: originalPath, file: attachment.originalFile as Uint8Array }];
        if (attachment.fileType === 'image') {
          const optimizedPath = `${this.basePath}/optimizedFiles/${attachment.id}.${attachment.format}`;
          const thumbnailPath = `${this.basePath}/thumbnails/${attachment.id}.${attachment.format}`;
          files.push({ path: optimizedPath, file: attachment.optimizedFile as Uint8Array });
          files.push({ path: thumbnailPath, file: attachment.thumbnail as Uint8Array });
        }
        attachmentsRecord[attachment.id] = { attachments: files, dek: this.data?.value?.encData?.dek || '' };
      });
      const uploadRecord = await this.fbService.uploadFilesAsRecord(attachmentsRecord);
      for (const attach of this.newAttachments.value) {
        if (attach.id in uploadRecord && uploadRecord[attach.id]) {
          const updatedAttach: Attachment = {
            ...attach,
            status: 'cloud',
          };
          this.attachments.push(this.ds.addAttachmentForm(updatedAttach));
        }
      }
    }

    if (this.deletedAttachments.length > 0) {
      const deletedAttachmentRecord: Record<number, string[]> = {};
      this.deletedAttachments.value.forEach((attachment: Attachment) => {
        const originalPath = `${this.basePath}/originalFiles/${attachment.id}.${attachment.format}`;
        const filePaths: string[] = [originalPath];
        if (attachment.fileType === 'image') {
          const optimizedPath = `${this.basePath}/optimizedFiles/${attachment.id}.${attachment.format}`;
          const thumbnailPath = `${this.basePath}/thumbnails/${attachment.id}.${attachment.format}`;
          filePaths.push(optimizedPath);
          filePaths.push(thumbnailPath);
        }
        deletedAttachmentRecord[attachment.id] = filePaths;
      });
      await this.fbService.deleteFilesAsRecord(deletedAttachmentRecord);
    }


    const todo: Todo = this.data.mode === 'new' ? this.todoStore.getNewTodo() : this.data.value;
    const updatedTodo = { ...todo, ...this.todoForm.value };

    if (this.data.mode === 'new') {
      this.todoStore.addTodo(updatedTodo);
    } else if (this.data.mode === 'edit') {
      this.todoStore.updateTodos([updatedTodo]);
    }
    this.cc.isLoading = false;
    this.dialogRef.close();
  }

  removeInvalidChecklists() {
     for (let i = this.checkLists.length - 1; i >= 0; i--) {
      const item = this.checkLists.at(i);
      const text = item.get('checklistText')?.value?.trim();

      if (!text) {
        this.checkLists.removeAt(i);
      }
    }
  }

  deleteTodo() {
    this.todoStore.deleteTodos([this.data.value]);
    this.dialogRef.close();
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }
}

<div class="top-section">
    <h6 class="heading mb-0">{{ cc.texts()['screen_common_setup'] }}</h6>
    <div class="flex">
        <app-svg name="more" class="more-icon ri-me-6" [matMenuTriggerFor]="infoMenu" role="button" *ngIf="data.mode === 'edit'" [color]="cc.theme.color12"></app-svg>
        <mat-menu #infoMenu="matMenu" class="me-menu info-menu ri-w-350px">
            <button mat-menu-item class="text-center text-14-400 color-8" (click)="addCollaborator()"><span class="w-full text-center">{{ cc.texts()['screen_common_dropdownCollaboration'] }}</span></button>
            <button mat-menu-item (click)="deleteJournalSetup()"><span class="text-center text-14-400 color-11 w-full">{{ cc.texts()['screen_common_delete'] }}</span></button>
        </mat-menu>

        <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
    </div>
</div>
<div class="journal-setup-form ri-p-4">
    <form [formGroup]="journalSetupForm" #jSetupForm="ngForm">
        <app-input-text class="ri-pb-3" [control]="getFc('title')" name="meJournalTitle" [placeholder]="cc.texts()['screen_common_titlePlaceholder']" maxLength="120" [submitted]="submitted"></app-input-text>
        <app-input-date-period class="ri-pb-3" name="meJournalDateRange" [startControl]="getFc('startAt')" [endControl]="getFc('endAt')" [repeatControl]="getFc('repeat')" [hiddenValues]="['OFF']"></app-input-date-period>
        <app-input-time class="ri-pb-3" [control]="getFc('startAt')" [isTmzAffected]="getFc('isTmzAffected')" [isStartTimeSet]="getFc('isStartTimeSet')" [duration]="getFc('duration')" [reminder]="getFc('reminderAt')"></app-input-time>
        <app-input-reminder [control]="getFc('reminderAt')" *ngIf="journalSetupForm.value.isStartTimeSet"></app-input-reminder>
    </form>

    <div class="journal-setup-addons">
        <app-input-hashtag class="ri-pt-3" *ngIf="journalSetupForm.value.tags && journalSetupForm.value.tags.length > 0" [control]="getFc('tags')"></app-input-hashtag>
    </div>
    <!-- <pre class="color-8">{{ journalSetupForm.value | json }}</pre> -->
</div>
<div class="bottom-section flex items-center justify-between ri-p-4 text-end ri-bt-2">
    <div class="flex items-center">
        <app-svg *ngIf="!journalSetupForm.value.tags || journalSetupForm.value.tags.length === 0" name="hashtag" [color]="cc.theme.color35" class="ri-pe-6" role="button" (click)="openHashtagsDialog()"></app-svg>
    </div>
    <button type="submit" class="btn-text text-16-500 color-1" (click)="hasChanges() ? save() : closeDialog()">{{ hasChanges() ? cc.texts()['screen_common_save'] : cc.texts()['screen_common_close'] }}</button>
</div>
<vg-player class="items-center justify-center w-full h-full" *ngIf="attachmentPath; else noImage">
    <div class="h-full w-full flex items-center justify-center" #attachmentImageContainer>
        <div class="rotatable-content h-full w-full flex items-center justify-center" [style.transform]="videoRotation">
            <img *ngIf="imageExists; else placeholder" #attachmentImage class="img attach-image w-auto" [src]="attachmentPath">
    
            <ng-template #placeholder>
                <img class="img attach-image w-auto" src="assets/icons/image.svg">
            </ng-template>
        </div>
    </div>
    <vg-controls class="flex-col custom-controls">
        <div class="flex ps-1 my-auto">
            <app-svg name="rotate" (click)="rotateImage()" role="button"></app-svg>
            <app-svg name="download" class="ms-2" (click)="downloadEvent.emit()" role="button"></app-svg>
            <app-svg name="trash" class="ms-2" (click)="deleteEvent.emit()" role="button"></app-svg>
        </div>
    </vg-controls>
</vg-player>

<ng-template #noImage>
    <div class="flex h-full w-full items-center justify-center">
        <p class="mb-0 color-8">{{ cc.texts()['screen_common_recordsNotFound'] }}</p>
    </div>
</ng-template>
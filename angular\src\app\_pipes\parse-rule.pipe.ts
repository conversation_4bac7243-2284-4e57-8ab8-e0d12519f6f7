import { Pipe, PipeTransform } from '@angular/core';
import { MapService } from '@app/_services/map.service';

@Pipe({
    name: 'parseRule',
    standalone: true,
})

export class ParseRulePipe implements PipeTransform {

    constructor(private mapService: MapService) { }

    transform(rrule: string): string {
        if (!rrule) return 'Off';
        if (!rrule.startsWith('RRULE:')) return 'Off';

        const ruleParts = rrule.replace('RRULE:', '').split(';');
        const ruleMap: { [key: string]: string } = {};

        ruleParts.forEach(part => {
            const [key, value] = part.split('=');
            ruleMap[key] = value;
        });

        switch (ruleMap['FREQ']) {
            case 'DAILY':
                return 'Daily'

            case 'WEEKLY':
                const wdays = ruleMap['BYDAY']
                    .split(',')
                    .map(day => this.mapService.dayMap()[day] || day)
                    .join(', ');
                if (wdays.includes('Mon') && wdays.includes('Tue') && wdays.includes('Wed') && wdays.includes('Thu') && wdays.includes('Fri')) {
                    return 'Mon - Fri';
                } else {
                    return `Every ${wdays}`;
                }

            case 'MONTHLY':
                if (ruleMap['BYMONTHDAY']) {
                    const isLastDay = ruleMap['BYMONTHDAY'] === '-1';
                    const mmonths = ruleMap['BYMONTH']
                        ?.split(',')
                        .map(m => this.mapService.monthMap()[m.trim()])
                        .filter(Boolean)
                        .join(', ');
                    if (isLastDay) {
                        return `Monthly last day of ${mmonths}`;
                    } else {
                        const mdates = ruleMap['BYMONTHDAY']
                            .split(',')
                            .map(day => this.mapService.dayMap()[day] || day)
                            .join(', ');
                        return `Every ${mmonths} on ${mdates}`;
                    }
                } else {
                    const byDayValues = ruleMap['BYDAY'].split(',');

                    const weekNumbersSet = new Set<string>();
                    const weekDaysSet = new Set<string>();

                    byDayValues.forEach(item => {
                        const match = item.match(/^(-?\d)?([A-Z]{2})$/);
                        if (match) {
                            const [, weekNum, dayCode] = match;
                            if (weekNum) weekNumbersSet.add(this.mapService.ordinalMap()[weekNum]);
                            weekDaysSet.add(this.mapService.dayMap()[dayCode]);
                        }
                    });

                    const weekNumbers = Array.from(weekNumbersSet).join(', ');
                    const weekDays = Array.from(weekDaysSet).join(', ');

                    return `Every ${weekNumbers} week on ${weekDays}`;
                };
            
            case 'YEARLY':
                return `Every ${this.mapService.monthMap()[ruleMap['BYMONTH']]} ${ruleMap['BYMONTHDAY']}`

            default:
                return 'Off';
        }
    }
}
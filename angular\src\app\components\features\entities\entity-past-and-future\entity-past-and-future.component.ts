import { CommonModule } from '@angular/common';
import { Component, computed, inject, Signal, signal, WritableSignal } from '@angular/core';
import { FeatureStore, UserStore } from '@app/_stores';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { EntityNameType, ShowTypeKey } from '@app/_types/generic.type';
import { InputDropdownComponent } from '@app/components/shared/inputs/input-dropdown/input-dropdown.component';
import { MatMenuModule } from '@angular/material/menu';
import { InputDropdownConfig } from '@app/_interfaces/generic.interface';
import { Router } from '@angular/router';
import { TodoTabComponent } from '../../todos/todo-tab/todo-tab.component';
import { JournalTabComponent } from '../../journals/journal-tab/journal-tab.component';
import { ViewSettingService } from '@app/_services/view-setting.service';
import { MatTabsModule } from '@angular/material/tabs';
import { CacheService } from '@app/_services/cache.service';
import { HabitTabComponent } from '../../habits/habit-tab/habit-tab.component';
import { CalendarTabComponent } from '../../calendar-integration/calendar-tab/calendar-tab.component';
import { MoneyTrackerTabComponent } from '../../money-tracker/money-tracker-tab/money-tracker-tab.component';
import { getDateString } from '@app/_utils/utils';

@Component({
  selector: 'app-entity-past-and-future',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    MatMenuModule,
    SvgComponent,
    InputDropdownComponent,
    TodoTabComponent,
    HabitTabComponent,
    JournalTabComponent,
    MoneyTrackerTabComponent,
    CalendarTabComponent
  ],
  templateUrl: './entity-past-and-future.component.html',
  styleUrl: './entity-past-and-future.component.scss'
})

export class EntityPastAndFutureComponent {

  mode = signal<'PAST' | 'FUTURE'>('PAST');
  readonly featureStore = inject(FeatureStore);
  readonly userStore = inject(UserStore);
  viewSignals: Signal<Record<string, WritableSignal<any>>> = this.userStore.viewSignals;
  selectedActiveEntity: WritableSignal<EntityNameType> = signal('todo');
  activeEntity: Signal<EntityNameType> = computed(() => this.featureStore.activeEntities().length === 1 ? this.featureStore.activeEntities()[0] : this.selectedActiveEntity());
  config: { [key: string]: { [key: string]: { name: string, groupBy: string, groupedViewType: string, showType: ShowTypeKey, show: string, descriptionType: string, hiddenGroupValues: string[], hidddenGroupedViewTypeValues: string[], hiddenShowValues: string[], filter: boolean } } } = {
    PAST: {
      todo: {
        name: this.cc.texts()['tab_pastTodo_title'],
        groupBy: 'pastTodoGroupBy',
        groupedViewType: 'pastTodoGroupedViewType',
        showType: 'pastTodoShowType',
        show: 'pastTodoShow',
        descriptionType: 'pastTodoDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'mood', 'transactionType', 'setup'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['mood', 'label', 'invalidEntry', 'date', 'habitResponse', 'setup', 'calendarName'],
        filter: false
      },
      habit: {
        name: this.cc.texts()['tab_pastHabit_title'],
        groupBy: 'pastHabitGroupBy',
        groupedViewType: 'pastHabitGroupedViewType',
        showType: 'pastHabitShowType',
        show: 'pastHabitShow',
        descriptionType: 'pastHabitDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'mood', 'transactionType', 'setup'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['mood', 'label', 'date', 'checklist', 'setup', 'calendarName'],
        filter: false
      },
      journal: {
        name: this.cc.texts()['tab_pastJournal_title'],
        groupBy: 'pastJournalGroupBy',
        groupedViewType: 'pastJournalGroupedViewType',
        showType: 'pastJournalShowType',
        show: 'pastJournalShow',
        descriptionType: 'pastJournalDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'transactionType', 'setup'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['label', 'checklist', 'emptyDays', 'habitResponse', 'setup', 'calendarName'],
        filter: false
      },
      moneyTracker: {
        name: this.cc.texts()['tab_pastMoneyTracker_title'],
        groupBy: 'pastMoneyTrackerGroupBy',
        groupedViewType: 'pastMoneyTrackerGroupedViewType',
        showType: 'pastMoneyTrackerShowType',
        show: 'pastMoneyTrackerShow',
        descriptionType: 'pastMoneyTrackerDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'mood'],
        hidddenGroupedViewTypeValues: ['showCounts'],
        hiddenShowValues: ['mood', 'label', 'invalidEntry', 'habitResponse', 'date', 'time', 'reminder', 'duration', 'repeat', 'checklist', 'calendarName'],
        filter: false
      },
      calendarIntegration: {
        name: this.cc.texts()['tab_pastCalendarEvents_title'],
        groupBy: 'pastCalendarEventGroupBy',
        groupedViewType: 'pastCalendarEventGroupedViewType',
        showType: 'pastCalendarEventShowType',
        show: 'pastCalendarEventShow',
        descriptionType: 'pastCalendarEventDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'mood', 'transactionType', 'setup', 'hashtag'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['mood', 'label', 'invalidEntry', 'date', 'habitResponse', 'setup', 'checklist', 'attachments', 'hashtag', 'emptyDays', 'description'],
        filter: false
      }
    },
    FUTURE: {
      todo: {
        name: this.cc.texts()['tab_futureTodo_title'],
        groupBy: 'futureTodoGroupBy',
        groupedViewType: 'futureTodoGroupedViewType',
        showType: 'futureTodoShowType',
        show: 'futureTodoShow',
        descriptionType: 'futureTodoDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'hashtag', 'mood', 'transactionType', 'setup'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['mood', 'label', 'invalidEntry', 'habitResponse', 'setup', 'calendarName'],
        filter: false
      },
      habit: {
        name: this.cc.texts()['tab_futureHabit_title'],
        groupBy: 'futureHabitGroupBy',
        groupedViewType: 'futureHabitGroupedViewType',
        showType: 'futureHabitShowType',
        show: 'futureHabitShow',
        descriptionType: 'futureHabitDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'hashtag', 'mood', 'transactionType', 'setup'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['mood', 'label', 'invalidEntry', 'date', 'checklist', 'setup', 'calendarName'],
        filter: false
      },
      journal: {
        name: this.cc.texts()['tab_futureJournal_title'],
        groupBy: 'futureJournalGroupBy',
        groupedViewType: 'futureJournalGroupedViewType',
        showType: 'futureJournalShowType',
        show: 'futureJournalShow',
        descriptionType: 'futureJournalDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'hashtag', 'mood', 'transactionType', 'setup'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['label', 'checklist', 'date', 'habitResponse', 'setup', 'calendarName'],
        filter: false
      },
      moneyTracker: {
        name: this.cc.texts()['tab_futureMoneyTracker_title'],
        groupBy: 'futureMoneyTrackerGroupBy',
        groupedViewType: 'futureMoneyTrackerGroupedViewType',
        showType: 'futureMoneyTrackerShowType',
        show: 'futureMoneyTrackerShow',
        descriptionType: 'futureMoneyTrackerDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'hashtag', 'mood'],
        hidddenGroupedViewTypeValues: ['showCounts'],
        hiddenShowValues: ['mood', 'label', 'invalidEntry', 'habitResponse', 'date', 'time', 'reminder', 'duration', 'repeat', 'checklist', 'calendarName'],
        filter: false
      },
      calendarIntegration: {
        name: this.cc.texts()['tab_futureCalendarEvents_title'],
        groupBy: 'futureCalendarEventGroupBy',
        groupedViewType: 'futureCalendarEventGroupedViewType',
        showType: 'futureCalendarEventShowType',
        show: 'futureCalendarEventShow',
        descriptionType: 'futureCalendarEventDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'hashtag', 'mood', 'transactionType', 'setup'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['mood', 'label', 'invalidEntry', 'date', 'habitResponse', 'setup', 'checklist', 'attachments', 'hashtag', 'emptyDays', 'description'],
        filter: false
      }
    }
  }
  entityShowConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
    const config = this.vs.entityShowConfig();
    config['description'].signalName = this.config[this.mode()][this.activeEntity()]['descriptionType'];
    return config;
  });

  constructor(private router: Router, public vs: ViewSettingService, public cc: CacheService) {
    const mode = this.router.url.includes('past') ? 'PAST' : 'FUTURE';
    this.mode.set(mode);
  }

  setTab(tabIndex: number) {
    this.selectedActiveEntity.set(this.featureStore.activeEntities()[tabIndex]);
  }

  updateEvent() {
    this.userStore.updateViewSettings();
  }

  isDataAvailable: Signal<boolean> = computed(() => {
    const store = this.featureStore.storeMap()[this.activeEntity()];
    const firstDateString = store?.firstDateString();
    const lastDateString = store?.lastDateString();
    const todayDateString = getDateString();
    console.log('entity===>', this.activeEntity(), 'firstDateString', firstDateString, 'lastDateString', lastDateString, 'todayDateString', todayDateString);
    const mode = this.mode();
    switch (mode) {
      case 'PAST':
        return firstDateString < todayDateString;
      case 'FUTURE':
        return lastDateString === null ? true : lastDateString > todayDateString;
    }
  });
}


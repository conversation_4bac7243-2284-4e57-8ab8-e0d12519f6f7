import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AuthService } from '@app/_services/auth.service';
import { MatDialog } from "@angular/material/dialog";
import { CommonModule } from '@angular/common';
import { LoaderComponent } from '@app/components/shared/loader/loader.component';
import { OtpComponent } from '../otp/otp.component';
import { Subject, takeUntil } from 'rxjs';
import { InputTextDialogComponent } from '@app/components/shared/inputs/input-text-dialog/input-text-dialog.component';
import { FirebaseFunctionService } from '@app/_services/firebase-function.service';
import { CacheService } from '@app/_services/cache.service';
import { SvgComponent } from '@app/components/shared/svg/svg.component';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    LoaderComponent,
    SvgComponent
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})

export class LoginComponent implements OnInit, OnDestroy {

  unSubscribe = new Subject<void>();

  constructor(
    public authService: AuthService,
    public dialog: MatDialog,
    private fbfs: FirebaseFunctionService,
    public cc: CacheService
  ) {

  }

  ngOnInit() {
    this.cc.updateTheme('dark');
    this.authService.handleAppleRedirectResult();
  }

  async loginWithGoogle() {
    await this.authService.loginWithGoogle();
  }

  async loginWithApple() {
    await this.authService.loginWithApple();
  }

  async loginWithEmail() {
    const emailDialog = this.dialog.open(InputTextDialogComponent, {
      width: '100%',
      maxWidth: '500px',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        title: this.cc.texts()['bottomSheet_enterEmail_title'],
        value: '',
        placeholder: this.cc.texts()['bottomSheet_enterEmail_placeholder']
      }
    });
    emailDialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(async email => {
      if (email) {
        try {
          this.cc.isLoading = true;
          const response = await this.fbfs.sendOTP(email);
          const otpDialog = this.dialog.open(OtpComponent, {
            width: '100%',
            maxWidth: '500px',
            maxHeight: '90vh',
            disableClose: true,
            data: {
              email: email,
              refId: (response.data as any)['refId']
            }
          });
          otpDialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(async userData => {
            if (userData) {
              await this.authService.init(userData);
            }
          });
        } catch (error) {
          console.log('error', error);
        } finally {
          this.cc.isLoading = false;
        }
      }
    });
  }

  ngOnDestroy() {
    this.unSubscribe.complete();
    this.unSubscribe.next();
  }
}

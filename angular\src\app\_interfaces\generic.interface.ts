import { WritableSignal } from "@angular/core";
import { FormControl } from "@angular/forms";
import { AttachmentType, UploadStatus } from "@app/_types/generic.type";

export interface Attachment {
    id: number;
    size: number;
    fileType: AttachmentType;
    originalFileName?: string;
    metadata?: Record<string, any>;
    format: string;
    containsThumbnail: boolean;
    originalFile?: Uint8Array | null;
    optimizedFile?: Uint8Array | null;
    thumbnail?: Uint8Array | null;
    status: UploadStatus;
    localPath?: string;
    createdAt?: Date;
    deletedAt?: Date | null;
}

export interface AttachmentFile {
    id: number;
    file: Blob | null;
    fileType: AttachmentType;
    format: string;
    size: number;
    path: string;
}

export interface Svg {
    color: string,
    svg: string
}

export interface EncryptionData {
    dek: string;
    encFields: string[];
}

export interface Collaborators {
    memberHashedEmails: string[];
    membersConfig: { [key: string]: any };
}

export interface InputDropdownConfig {
    name: string;
    icon?: string;
    signalName?: string;
    control?: FormControl;
    description?: string;
    multiple?: boolean;
    enumData?: { [key: string]: string };
    disabled?: boolean;
    defaultValue?: string;
    config?: { [key: string]: InputDropdownConfig };
    fcName?: string;
}

export interface InputNestedDropdownConfig {
    name: string;
    signal: WritableSignal<any>;
    icon?: string;
}

export interface CustomDate {
    dateString: string;
    timeString: string;
    dateTime: Date;
    timeWithOffset: string;
}

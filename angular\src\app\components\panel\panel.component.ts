import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { MatToolbar } from '@angular/material/toolbar';
import { MatIconButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { MatDrawer, MatDrawerContainer, MatDrawerContent } from '@angular/material/sidenav';
import { Subject } from 'rxjs';
import { AuthService } from '@app/_services/auth.service';
import { CommonModule } from '@angular/common';
import { CryptographyService } from '@app/_services/cryptography.service';
import { IndexDbService } from '@app/_services/index-db.service';
import { StorageService } from '@app/_services/storage.servive';
import { CacheService } from '@app/_services/cache.service';
import { SvgComponent } from '../shared/svg/svg.component';
import { ViewSettingService } from '@app/_services/view-setting.service';
import { AlertService } from '@app/_services/alert.service';
import { FeatureStore } from '@app/_stores/feature.store';

@Component({
  selector: 'app-panel',
  standalone: true,
  imports: [
    RouterOutlet,
    RouterLink,
    RouterLinkActive,
    CommonModule,
    MatToolbar,
    MatIconButton,
    MatIcon,
    MatDrawer,
    MatDrawerContainer,
    MatDrawerContent,
    SvgComponent
  ],
  templateUrl: './panel.component.html',
  styleUrl: './panel.component.scss'
})

export class PanelComponent implements OnInit {

  showSidenav = true;
  currentUser: any = null;
  unSubscribe = new Subject<void>();
  isResyncing: boolean = true;
  readonly featureStore = inject(FeatureStore);

  constructor(
    private authService: AuthService,
    private ss: StorageService,
    public cc: CacheService,
    private cryptoService: CryptographyService,
    private idb: IndexDbService,
    public vs: ViewSettingService,
    private router: Router,
    private route: ActivatedRoute,
    private alertService: AlertService
  ) {

  }

  async ngOnInit() {
    this.cc.init();
    await this.authService.init();
    if (!this.ss.isResynced()) {
      this.isResyncing = true;
      await this.idb.resync();
      this.ss.setResynced(true);
      this.isResyncing = false;
    } else {
      this.isResyncing = false;
    }
    this.idb.init();
  }

  toggleSidenav() {
    this.showSidenav = !this.showSidenav;
  }

  async logout() {
    const confirm = await this.alertService.confirm(this.cc.texts()['overlay_logoutThisDeviceConfirmation_title'], this.cc.texts()['overlay_logoutThisDeviceConfirmation_content'], this.cc.texts()['overlay_logoutThisDeviceConfirmation_logout'], this.cc.texts()['screen_common_buttonCancel']);
    if (confirm) {
      this.authService.logoutUser();
    } else {
      return;
    }
  }

  ngOnDestroy() {
    this.unSubscribe?.next();
    this.unSubscribe?.complete();
  }
}

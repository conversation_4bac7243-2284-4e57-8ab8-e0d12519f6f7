import { CommonModule } from '@angular/common';
import { Component, computed, Input, Signal } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatMenuModule } from '@angular/material/menu';
import { SvgComponent } from '../../svg/svg.component';
import { TimerStopType } from '@app/_types/generic.type';
import { CacheService } from '@app/_services/cache.service';

@Component({
  selector: 'app-input-timer-stop-type',
  standalone: true,
  imports: [
    CommonModule,
    MatMenuModule,
    SvgComponent
  ],
  templateUrl: './input-timer-stop-type.component.html',
  styleUrl: './input-timer-stop-type.component.scss'
})

export class InputTimerStopTypeComponent {

  @Input() title: string = '';
  @Input() name: string = '';
  @Input() hideIcon: boolean = false;
  @Input() control: FormControl = new FormControl('onRoundEnd');
  @Input() readonly: boolean = false;
  timerStopTypes: TimerStopType[] = ['onRoundEnd', 'onGoalReached', 'never'];
  timerStopType: Signal<Record<string, string>> = computed(() => {
    return {
      onRoundEnd: this.cc.texts()['bottomSheet_stopTimer_roundCompleteTitle'],
      onGoalReached: this.cc.texts()['bottomSheet_stopTimer_goalCompleteTitle'],
      never: this.cc.texts()['bottomSheet_stopTimer_neverStopTitle']
    };
  });
  timerStopTypeDescription: Signal<Record<string, string>> = computed(() => {
    return {
      onRoundEnd: this.cc.texts()['bottomSheet_stopTimer_roundCompleteContent'],
      onGoalReached: this.cc.texts()['bottomSheet_stopTimer_goalCompleteContent'],
      never: this.cc.texts()['bottomSheet_stopTimer_neverStopContent']
    };
  });

  constructor(public cc: CacheService) {

  }
}

import { Component, inject, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SvgComponent } from '../../svg/svg.component';
import { FormControl } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { InputReminderSelectComponent } from '../input-reminder-select/input-reminder-select.component';
import { MatDialog } from '@angular/material/dialog';
import { ParseMinutesPipe } from '@app/_pipes/parse-minutes.pipe';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';

@Component({
  selector: 'app-input-reminder',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    ParseMinutesPipe
  ],
  templateUrl: './input-reminder.component.html',
  styleUrl: './input-reminder.component.scss'
})

export class InputReminderComponent {

  @Input() title: string = '';
  @Input() placeholder: string = '';
  @Input() name: string = '';
  @Input() hideIcon: boolean = false;
  @Input() control: FormControl = new FormControl();
  @Input() readonly: boolean = false;
  unSubscribe = new Subject<void>();

  constructor(private dialog: MatDialog, private alertService: AlertService, public cc: CacheService) {

  }

  getReminderText(reminder: number) {
    if (reminder === 0) {
      return this.cc.texts()['dropdown_remindOption_onTime'];
    } else if (reminder < 0) {
      return this.cc.interpolateText('overlay_reminderSelect_previewBeforeTime', { time: Math.abs(reminder).toString() });
    } else {
      return this.cc.interpolateText('overlay_reminderSelect_previewAfterTime', { time: reminder.toString() });
    }
  }

  async openReminderDialog(type: 'add' | 'edit', reminder: number = -10) {
    if (type === 'add' && this.control.value.length === 3) {
      await this.alertService.alert(this.cc.texts()['overlay_starredMaximumLimitReached_title'], this.cc.texts()['overlay_starredMaximumLimitReached_content'], this.cc.texts()['screen_common_ok']);
    } else {
      const dialog = this.dialog.open(InputReminderSelectComponent, {
        width: '100%',
        maxWidth: '500px',
        maxHeight: '90vh',
        disableClose: true,
        data: {
          value: reminder,
          allRemainders: this.control.value,
          type: type
        },
      })

      dialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
        if (result) {
          this.control.setValue(result.value);
        }
      });
    }
  }

  ngOnDestroy() {
    this.unSubscribe.complete();
    this.unSubscribe.next();
  }
}

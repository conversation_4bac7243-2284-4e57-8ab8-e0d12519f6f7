<div class="flex items-start w-full" [ngClass]="{'readonly': readonly}" role="button"
    [matMenuTriggerFor]="inputTimerStopTypeMenu">
    <app-svg name="clock" [color]="cc.theme.color35" class="ri-pe-4" *ngIf="!hideIcon"></app-svg>
    <div>
        <p class="text-14-400 mb-0 color-8">{{ timerStopType()[control.value] }}</p>
        <p class="text-12-400 color-7 mb-0 ri-pt-2">{{ timerStopTypeDescription()[control.value] }}</p>
    </div>
</div>
<mat-menu #inputTimerStopTypeMenu="matMenu" class="me-menu ri-w-350px">
    <button mat-menu-item *ngFor="let type of timerStopTypes; let i = index;" (click)="control.setValue(type)">
        <div class="flex justify-between">
            <div class="ri-pe-2">
                <p class="text-14-400 color-8 mb-0">{{ timerStopType()[type] }}</p>
                <p class="text-12-400 color-7 mb-0 ri-pt-2">{{ timerStopTypeDescription()[type] }}</p>
            </div>
            <app-svg name="tick" *ngIf="type === control.value" [color]="cc.theme.color35"></app-svg>
        </div>
    </button>
</mat-menu>
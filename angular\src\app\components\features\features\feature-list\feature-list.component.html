<div class="top-section">
    <h6 class="heading mb-0">{{ cc.texts()['bottomSheet_features_titleTopBar'] }}</h6>
    <div class="">
        <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
    </div>
</div>

<div class="body-section feature-block bg-3 position-relative">
    <div class="feature-data flex items-start ri-px-4 ri-py-4" *ngFor="let feature of featureStore.premiumFeatures(); let i = index;" role="button" (click)="addFeature(feature)">
        <app-svg [name]="feature.icon" [color]="cc.theme.color35"></app-svg>
        <div class="content w-full flex justify-between ri-ps-3">
            <div class="">
                <p class="text-16-500 color-1 ri-pb-3 mb-0">{{ featureName()[feature.name] }}</p>
                <p class="text-16-400 color-8 mb-0 ri-pb-3 mb-0">{{ feature.description }}</p>
            </div>
            <app-svg name="plus" [color]="cc.theme.color35"></app-svg>
        </div>
    </div>
</div>
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, signal, SimpleChanges, ViewChild, WritableSignal } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSlideToggle, MatSlideToggleChange } from '@angular/material/slide-toggle';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';

@Component({
  selector: 'app-input-toggle',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatSlideToggle
  ],
  templateUrl: './input-toggle.component.html',
  styleUrl: './input-toggle.component.scss'
})

export class InputToggleComponent {

  @ViewChild('slideToggle') slideToggle!: MatSlideToggle;
  @Input() value: boolean = false;
  @Input() signal: WritableSignal<boolean> = signal<boolean>(false);
  @Input() name: string = '';
  @Input() disabled: boolean = false;
  @Input() readonly: boolean = false;
  @Input() confirm: { title: string, message: string, buttonText: string, buttonColor: string } | null = null;

  @Output() change = new EventEmitter<boolean>();

  constructor(
    private alertService: AlertService,
    public cc: CacheService
  ) {

  }

  ngOnInit() {

  }

  toggle() {
    this.value = !this.value;
    this.signal.set(!this.signal());
    this.change.emit(this.readonly ? this.value : this.signal());
  }

  async toggleOverlay(event: Event) {
    event.stopPropagation();
    if (this.confirm && this.value === true) {
      const res = await this.alertService.confirm(
        this.confirm.title,
        this.confirm.message,
        this.confirm.buttonText,
        this.cc.texts()['screen_common_buttonCancel'],
        this.confirm.buttonColor
      );
      if (!res) return;
      if (res.confirm) {
        this.toggle();
      }
      return;
    } else {
      this.toggle();
    }
  }

}

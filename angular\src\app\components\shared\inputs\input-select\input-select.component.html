<div class="flex items-start w-full">
    <app-svg name="widget" [color]="cc.theme.color35" class="ri-pe-4" *ngIf="!hideIcon"></app-svg>
    <p class="text-16-400 color-8 mb-0" role="button" [matMenuTriggerFor]="inputSelectMenu"> {{ map[control.value].title || placeholder }} </p>
</div>

<mat-menu #inputSelectMenu="matMenu" class="me-menu">
    <ng-container *ngFor="let item of options; let i = index;">
        <button mat-menu-item (click)="onSelect(item.id, $event)">
            <span class="text-14-400 color-8 flex items-center justify-between">
                <span>{{item.title}}</span>
                <app-svg class="ri-ms-6" name="tick" *ngIf="control.value === item.id" [color]="cc.theme.color35"></app-svg>
            </span>
        </button>
    </ng-container>
    <button mat-menu-item (click)="addNew.emit()" class="ri-bt-2">
        <span class="text-14-400 color-35 text-center flex items-center justify-center">{{ cc.texts()['bottomSheet_moneyTrackerSetupList_addNew'] }}</span>
    </button>
</mat-menu>
<div class="top-section">
    <h6 class="heading mb-0">{{ transactionTypeMap()[data.transactionType] }}</h6>
    <div class="flex">
        <app-svg name="more" class="more-icon ri-me-6" [matMenuTriggerFor]="infoMenu" role="button" [color]="cc.theme.color12"></app-svg>
        <mat-menu #infoMenu="matMenu" class="me-menu info-menu">
            <button mat-menu-item class="text-14-400 color-8" (click)="openSetup(setup())">
                <span class="flex items-center"><app-svg name="edit" class="ri-pe-3" [color]="cc.theme.color35" role="button">{{ cc.texts()['dropdown_journalActionKebabMenu_editSetup'] }}</app-svg></span>
            </button>
            <button mat-menu-item class="text-14-400 color-8" (click)="deleteTransaction()">
                <span class="flex items-center"><app-svg name="trash" class="ri-pe-3" [color]="cc.theme.color11" role="button"></app-svg>{{ cc.texts()['screen_common_delete'] }}</span>
            </button>
        </mat-menu>

        <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
    </div>
</div>
<div class="transaction-form ri-p-4" id="transactionContentBlock">
    <form [formGroup]="transactionForm" #mtForm="ngForm">
        <app-input-text class="ri-pb-3" [control]="getFc('title')" name="meTransactionTitle" [placeholder]="cc.texts()['screen_common_titlePlaceholder']" maxLength="120" [submitted]="submitted"></app-input-text>
        <app-input-currency class="ri-pb-3" [control]="getFc('amount')" name="meTransactionAmount" [placeholder]="cc.texts()['bottomSheet_moneyTrackerAction_amountPlaceHolder']" [setupCurrency]="setup()?.currency || ''" type="number" maxLength="10" [submitted]="submitted"></app-input-currency>
        <app-input-date class="ri-pb-3" [control]="getFc('transactionDate')" name="meTransactionDate" [customDate]="true"></app-input-date>
        <app-input-select class="ri-pb-3" [control]="getFc('setupId')" name="meTransactionSetup" [placeholder]="cc.texts()['screen_common_setup']" [options]="moneyStore.enabledSetups()" [map]="moneyStore.idToSetup()" (selectEvent)="setupChange()" (addNew)="openSetup()"></app-input-select>
        <app-input-text *ngIf="isDescription" [control]="getFc('description')" name="meTransactionDescription" [placeholder]="cc.texts()['screen_common_description']" maxLength="500" icon="description"></app-input-text>
    </form>

    <div class="transaction-addons">
        <app-input-hashtag class="ri-pt-3" *ngIf="transactionForm.value.tags && transactionForm.value.tags.length > 0"
            [control]="getFc('tags')"></app-input-hashtag>
        <app-attachment-list [title]="transactionForm.value.title" class="ri-pt-3" *ngIf="attachmentValues && attachmentValues.length > 0"
            [attachments]="attachmentValues" [basePath]="basePath" [entity]="data.value"
            (deleteEvent)="deleteAttachments($event)"></app-attachment-list>
    </div>
</div>
<div class="bottom-section flex items-center justify-between ri-p-4 text-end ri-bt-2">
    <div class="flex items-center">
        <app-svg *ngIf="!isDescription" name="description" [color]="cc.theme.color35" class="ri-pe-6" role="button" (click)="isDescription = true"></app-svg>
        <app-svg *ngIf="!transactionForm.value.tags || transactionForm.value.tags.length === 0" name="hashtag" [color]="cc.theme.color35" class="ri-pe-6" role="button" (click)="openHashtagsDialog()"></app-svg>
        <app-input-file [newAttachments]="newAttachments" [multiple]="true"></app-input-file>
    </div>
    <button type="submit" class="btn-text text-16-500 color-35" (click)="hasChanges() ? save() : closeDialog()">{{ hasChanges() ? cc.texts()['screen_common_save'] : cc.texts()['screen_common_close'] }}</button>
</div>
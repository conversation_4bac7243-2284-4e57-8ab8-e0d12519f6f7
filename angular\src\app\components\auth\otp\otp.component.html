<div class="ri-p-6">
    <div class="flex justify-end">
        <p class="text-14-400 mb-0 text-secondary" role="button" (click)="closeDialog()">{{ cc.texts()['screen_common_close'] }}</p>
    </div>
    <div class="otp-block flex flex-col justify-center">
        <img src="assets/imgs/otp.svg" alt="OTP Image" style="height: 100px;">
        <p class="text-18-500 text-white mb-0 text-center ri-pt-6">{{ cc.texts()['screen_otpVerification_title'] }}</p>
        <p class="text-14-400 text-secondary mb-0 text-center ri-pt-4">{{ cc.interpolateText('screen_otpVerification_infoText', { emailId: data.email }) }}</p>
        <div class="otp-box flex align-center justify-center" [ngClass]="isOtpInvalid ? 'invalid' : ''" *ngIf="!otpVerifying">
            <span>{{otp[0] ? otp[0] : ''}}</span>
            <span>{{otp[1] ? otp[1] : ''}}</span>
            <span>{{otp[2] ? otp[2] : ''}}</span>
            <span>{{otp[3] ? otp[3] : ''}}</span>
        </div>
        <div class="four-dot-loader" *ngIf="otpVerifying">
            <span></span>
            <span></span>
            <span></span>
            <span></span>
        </div>
        <p class="text-14-400 mb-0 text-center text-danger ri-pb-4" *ngIf="isOtpInvalid">{{ cc.texts()['screen_otpVerification_errorText'] }}</p>
        <p class="text-14-400 mb-0 text-center ri-pb-4" [ngClass]="timeLeft < 11 ? 'text-danger' : 'text-secondary'">{{
            timeLeft > 0 ? timeLeft + 's' : cc.texts()['screen_otpVerification_timeout'] }}</p>
        <button class="btn btn-text text-14-400 mb-0 text-center" role="button" (click)="reSendOTP()"
            [ngClass]="timeLeft <= 150 ? 'text-success' : 'text-secondary'" [disabled]="timeLeft > 150" *ngIf="!otpResending">{{ cc.texts()['screen_otpVerification_resend'] }}</button>
        <div class="flex justify-center" *ngIf="otpResending">
            <div class="three-dot-loader">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="number-block flex align-center justify-center">
            <span (click)="addOtp('1')" role="button">1</span>
            <span (click)="addOtp('2')" role="button">2</span>
            <span (click)="addOtp('3')" role="button">3</span>
            <span (click)="addOtp('4')" role="button">4</span>
            <span (click)="addOtp('5')" role="button">5</span>
            <span (click)="addOtp('6')" role="button">6</span>
            <span (click)="addOtp('7')" role="button">7</span>
            <span (click)="addOtp('8')" role="button">8</span>
            <span (click)="addOtp('9')" role="button">9</span>
            <span></span>
            <span (click)="addOtp('0')" role="button">0</span>
            <span><app-svg name="backspace" color="#949C9E" (click)="removeOtp()" role="button"></app-svg></span>
        </div>
    </div>
</div>
import { CommonModule } from '@angular/common';
import { Component, Inject, inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { UserStore } from '@app/_stores/user.store';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { HashtagFormComponent } from '../hashtag-form/hashtag-form.component';
import { Hashtag } from '@app/_interfaces/user.interface';
import { MatMenuModule } from '@angular/material/menu';
import { Subject, takeUntil } from 'rxjs';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';

@Component({
  selector: 'app-hashtag',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    MatMenuModule
  ],
  templateUrl: './hashtag.component.html',
  styleUrl: './hashtag.component.scss'
})

export class HashtagComponent {

  readonly userStore = inject(UserStore);
  unSubscribe = new Subject<void>();
  selectedHashtags: string[] = [];

  constructor(
    public dialogRef: MatDialogRef<HashtagComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { values: string[], type: 'map' | 'filter' },
    public dialog: MatDialog,
    private alertService: AlertService,
    public cc: CacheService
  ) {
    this.selectedHashtags = [...this.data.values];

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  clickHashtag(hashtagId: string) {
    if (this.selectedHashtags.includes(hashtagId)) {
      this.selectedHashtags = this.selectedHashtags.filter(id => id !== hashtagId);
    } else {
      this.selectedHashtags.push(hashtagId);
    }
  }

  hasChanges(): boolean {
    const set1 = new Set(this.selectedHashtags);
    const set2 = new Set(this.data.values);

    if (set1.size !== set2.size) return true; // Different sizes → changed

    for (let item of set1) {
      if (!set2.has(item)) return true; // Any missing item → changed
    }

    return false; // All elements match → no change
  }

  openHashtagForm(mode: 'new' | 'edit', hashtag?: Hashtag) {
    const confirmDialog = this.dialog.open(HashtagFormComponent, {
      width: '100%',
      maxWidth: '500px',
      disableClose: true,
      data: {
        mode: mode,
        hashtag: hashtag,
      },
    });

    return confirmDialog.afterClosed();
  }

  save() {
    this.dialogRef.close(this.selectedHashtags);
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }
}

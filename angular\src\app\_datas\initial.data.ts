import { dbVersion } from "@app/_configs/db.config";
import { FirestoreCollection } from "@app/_enums/firestore-collection.enum";
import { UserViewSettings } from "@app/_interfaces/user.interface";
import { getCustomDate } from "@app/_utils/utils";

export function getNewViewSettings(): UserViewSettings {
    return {
        id: `vs_`,
        uid: '',
        docVer: dbVersion,
        docCollection: FirestoreCollection.ViewSettings.toString(),
        encData: {
            dek: '',
            encFields: ['featureSettings.userGoal']
        },
        source: 'client',
        appSettings: {
            themeColor: "theme1",
            language: "english",
            supportLanguage: "english",
            isSpeechToTextEnabled: true,
            appTheme: "dark",
            isVibrationEnabled: false
        },
        featureSettings: {
            showFeatureLabels: true,
            userGoal: null,
            viewType: 'none',
            showListFeature: true,
            showCalendarView: false,
            showTodoFeature: true,
            showNoteFeature: true,
            showUserGoal: false,
            hideCompletedItems: false,
            showTimebox: true
        },
        futureSettings: {
            futureHabitSettings: {
                showCounts: false,
                showCalendarName: false,
                showHabitResponse: false,
                showTime: true,
                viewSettingType: "compact",
                showRepeat: false,
                collapsedView: false,
                showReminder: false,
                showEmptyDays: false,
                descriptionType: "none",
                showDuration: false,
                showDate: false,
                showInvalidEntries: false,
                groupBy: "date",
                showTags: false,
                showImage: false
            },
            futureJournalSettings: {
                showMood: false,
                showDuration: false,
                collapsedView: false,
                showCounts: false,
                showTags: false,
                viewSettingType: "compact",
                showRepeat: false,
                descriptionType: "none",
                showInvalidEntries: false,
                showTime: true,
                showEmptyDays: false,
                showImage: false,
                showReminder: false,
                groupBy: "date",
                showDate: false
            },
            futureMoneyTrackerSettings: {
                showHashtag: false,
                groupBySettings: "date",
                viewSettingType: "compact",
                showDate: false,
                showEmptyDays: false,
                showImage: false,
                descriptionType: "none",
                showNetAmount: false,
                collapsedView: false,
                showSetupTitle: true
            },
            futureCalendarEventSettings: {
                showTime: true,
                showCounts: false,
                collapsedView: false,
                showCalendarName: false,
                showRepeat: false,
                viewSettingType: "compact",
                showDuration: false,
                showReminder: false,
                groupBy: "date"
            },
            futureTodoSettings: {
                showReminder: false,
                showEmptyDays: false,
                showImage: false,
                viewSettingType: "compact",
                groupBy: "date",
                showDuration: false,
                showCounts: false,
                collapsedView: false,
                showChecklist: false,
                showTime: true,
                descriptionType: "none",
                showTags: false,
                showRepeat: false
            }
        },
        insightSettings: {
            insightHabitSettings: {
                resultInPercentage: false,
            },
            insightJournalSettings: {
                resultInPercentage: false,
            },
            insightTodoSettings: {
                resultInPercentage: false,
            },
        },
        listSettings: {
            publicListItemSheetSettings: {
                showLastUpdatedBy: false,
                viewType: "compact",
                showDescription: true,
                showLastUpdatedAt: false
            },
            sharedListSettings: {
                collapsedView: false,
                groupByType: "none",
                viewSettingType: "compact",
                showHashtags: false,
                itemCount: true,
                showAccess: false,
                showCounts: false,
                showDescription: false
            },
            myListSettings: {
                collapsedView: true,
                itemCount: true,
                showDescription: false,
                showCollaboratorsCount: true,
                showAwaitingUserCount: false,
                viewSettingType: "custom",
                groupByType: "none",
                showInvitedUsersCount: false,
                showCounts: false,
                showHashtags: true
            },
            sharedListItemSettings: {
                showDescription: true,
                showLastUpdatedBy: false,
                viewType: "compact",
                showLastUpdatedAt: false
            },
            myListItemSheetSettings: {
                showDescription: true,
                showLastUpdatedBy: true,
                viewType: "custom",
                showLastUpdatedAt: true
            },
            publicListSettings: {
                itemCount: true,
                showDescription: false,
                viewSettingType: "compact"
            }
        },
        moneyTrackerSettings: {
            config: {
                title: null,
                currency: '₹',
                hasSetCurrency: false
            },
            listViewSettings: {
                showDate: true,
                showHashtag: false,
                descriptionType: 'none',
                showImage: false,
                groupBySettings: 'date',
                showEmptyDays: false,
                collapsedView: false,
                showNetAmount: false,
                viewSettingType: "compact"
            }
        },
        noteSettings: {
            savedNoteSettings: {
                showFilterRow: false,
                noteViewImage: false,
                noteViewTime: false,
                noteDescriptionType: "none",
                notesGroupBy: "none",
                collapsedView: false,
                noteViewDate: true,
                showCounts: false,
                viewType: "compact",
                noteViewMood: false
            },
            myNoteSettings: {
                showCounts: false,
                noteViewTime: true,
                showMemberCount: true,
                viewType: "custom",
                showFilterRow: false,
                noteViewTags: false,
                noteViewMood: true,
                notesGroupBy: "none",
                noteViewDate: true,
                noteDescriptionType: "short",
                noteViewImage: true,
                collapsedView: false
            },
            sharedNoteSettings: {
                noteDescriptionType: "none",
                noteViewDate: true,
                showAccess: false,
                noteViewImage: false,
                viewType: "compact",
                showFilterRow: false,
                collapsedView: false,
                notesGroupBy: "none",
                noteViewMood: false,
                showCounts: false,
                noteViewTime: false
            }
        },
        notificationSettings: {
            isDailyAgendaMobileNotificationEnabled: false,
            isDailyAgendaEmailNotificationEnabled: false,
            isOverdueEmailNotificationEnabled: false,
            emailNotificationTime: getCustomDate(),
            muteAllDevice: null,
            emailNotificationTimezone: "+05:30",
            isMuteAllDeviceTmzDependent: true,
            muteAllDeviceStartAt: null,
            remindMeType: "sameDay",
            muteAllCustomPresetSelected: false,
            mobileDailyAgendaNotificationTime: getCustomDate(),
            pinReminder: false,
            mobileSoundType: "mevolve1",
            mobileSnoozeType: "five",
            isDailyAgendaTmzDependent: false
        },
        pastSettings: {
            pastJournalSettings: {
                showTime: true,
                collapsedView: false,
                showTags: false,
                showDuration: false,
                descriptionType: "none",
                showMood: false,
                groupBy: "date",
                showDate: false,
                showRepeat: false,
                showEmptyDays: false,
                showInvalidEntries: false,
                showCounts: false,
                showReminder: false,
                showImage: false,
                viewSettingType: "compact"
            },
            pastHabitSettings: {
                showDate: false,
                showReminder: false,
                showDuration: false,
                showImage: false,
                showTime: true,
                descriptionType: "none",
                showTags: false,
                showEmptyDays: false,
                groupBy: "date",
                showHabitResponse: false,
                showInvalidEntries: false,
                collapsedView: false,
                showCounts: false,
                viewSettingType: "compact",
                showRepeat: false
            },
            pastCalendarEventSettings: {
                showTime: true,
                groupBy: "date",
                collapsedView: false,
                showCalendarName: false,
                showReminder: false,
                viewSettingType: "compact",
                showRepeat: false,
                showDuration: false,
                showCounts: false
            },
            pastTodoSettings: {
                showCounts: false,
                showChecklist: false,
                showEmptyDays: false,
                showImage: false,
                showReminder: false,
                showTime: true,
                descriptionType: "none",
                viewSettingType: "compact",
                showDate: false,
                groupBy: "date",
                collapsedView: false,
                showDuration: false,
                showRepeat: false,
                showTags: false
            },
            pastMoneyTrackerSettings: {
                groupBySettings: "date",
                descriptionType: "none",
                showNetAmount: false,
                showSetupTitle: true,
                showImage: false,
                showHashtag: false,
                collapsedView: false,
                showEmptyDays: false,
                showDate: false,
                viewSettingType: "compact"
            }
        },
        todaySettings: {
            unscheduleSettings: {
                showChecklist: false,
                descriptionType: "none",
                viewSettingType: "compact",
                showImage: false,
                showTags: false,
                showCompletedAt: true
            },
            todayTabSettings: {
                showTime: true,
                showDuration: true,
                showReminder: true,
                showCalendarName: true,
                showImage: true,
                viewSettingType: "custom",
                showRepeat: true,
                showHabitResponse: true,
                showTags: false,
                showMood: true,
                showInvalidEntries: false,
                showChecklist: true,
                descriptionType: "none"
            },
            overdueSettings: {
                showDuration: false,
                showChecklist: false,
                showTags: false,
                showReminder: false,
                showRepeat: false,
                viewSettingType: "compact",
                descriptionType: "none",
                showTime: true,
                showImage: false
            }
        },
        sessionId: '',
        createdAt: new Date(),
        cloudUpdatedAt: new Date(),
        deletedAt: null,
        permaDeletedAt: null,
    }
}
<div class="top-section">
    <h4 class="heading mb-0">{{ cc.texts()['screen_features_title'] }}</h4>
</div>

<div class="body-section bg-3 position-relative">
    <div class="filter-block h-auto filtered">
        <button class="btn btn-filter btn-toggle me-2" [ngClass]="{'active': featureStore.filter().status() === 'active' }" (click)="featureStore.filter().status.set('active')">{{ cc.texts()['screen_common_active'] }}</button>
        <button class="btn btn-filter btn-toggle me-2" [ngClass]="{'active': featureStore.filter().status() === 'upcoming' }" (click)="featureStore.filter().status.set('upcoming')">{{ cc.texts()['screen_common_upcoming'] }}</button>
        <button class="btn btn-filter btn-toggle me-2" [ngClass]="{'active': featureStore.filter().status() === 'completed' }" (click)="featureStore.filter().status.set('completed')">{{ cc.texts()['screen_common_completed'] }}</button>
    </div>

    <div class="feature-block">
        <div class="feature-data flex items-start ri-px-4 ri-py-4" *ngFor="let feature of featureStore.features(); let i = index;" role="button" (click)="openFeatureSetup(feature)">
            <app-svg [name]="feature.icon" [color]="cc.theme.color35"></app-svg>
            <div class="content w-full flex justify-between ri-ps-3">
                <div class="">
                    <p class="text-16-500 color-1 ri-pb-3 mb-0">{{ featureName()[feature.name] }}</p>
                    <p class="text-16-400 color-8 mb-0 ri-pb-3 mb-0">{{ feature.description }}</p>
                    <div class="mb-0">
                        <div class="inline-flex items-center">
                            <app-svg name="circleCheck" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                            <span class="text-12-400 color-7">{{ featureStatus()[feature.status] }}</span>
                        </div>
                    </div>
                </div>
                <app-input-toggle [value]="feature.disabled" [readonly]="true" (change)="toggleChange(feature, $event)" (click)="$event.stopPropagation()" [confirm]="confirmConfig[feature.entityName] || null"></app-input-toggle>
            </div>
        </div>

        <div class="no-result-block h-full flex items-center justify-center flex-col"
            *ngIf="featureStore.features().length === 0">
            <app-svg name="searchPlaceholder" [color]="cc.theme.color35"></app-svg>
            <p class="text-16-400 color-8 mb-0 ri-pt-4">{{ cc.texts()['screen_common_noData'] }}</p>
        </div>
    </div>
</div>

<div class="add-button">
    <button class="btn" (click)="addFeatures()"><app-svg name="plus"></app-svg></button>
</div>
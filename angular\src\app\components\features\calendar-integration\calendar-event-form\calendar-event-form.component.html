<div class="top-section">
     <h6 class="heading mb-0">{{ cc.texts()['bottomSheet_calendarEvent_titleTopBar'] }} <span class="text-12-400 ri-ps-2">{{ cc.getFormattedDate(data.dateString, 'd MMM y, EEE') }}</span></h6>
    <div class="flex">
        <app-svg name="more" class="more-icon ri-me-6" [matMenuTriggerFor]="infoMenu" role="button" *ngIf="data.mode === 'edit'" [color]="cc.theme.color12"></app-svg>
        <mat-menu #infoMenu="matMenu" class="me-menu info-menu ri-w-350px">
            <button mat-menu-item class="text-center text-14-400 color-8" (click)="openCalendarAccountForm()"><span class="w-full text-center">{{ cc.texts()['dropdown_calendarEventKebabMenu_editCalendar'] }}</span></button>
        </mat-menu>

        <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
    </div>
</div>
<div class="calendar-event-form ri-p-4">
    <form [formGroup]="calendarEventForm" #cEventForm="ngForm">
        <app-input-text class="ri-pb-3" [control]="getFc('title')" name="meCalendarEventTitle" [placeholder]="cc.texts()['screen_common_titlePlaceholder']" [readonly]="true" (click)="openAlert()"></app-input-text>
        <app-input-textarea class="ri-pb-3" [control]="getFc('description')" name="meCalendarEventDescription" [placeholder]="cc.texts()['screen_common_description']" [readonly]="true" (click)="openAlert()"></app-input-textarea>
        <app-input-date-period class="ri-pb-3" name="meCalendarEventDateRange" [startControl]="getFc('startAt')" [endControl]="getFc('endAt')" [repeatControl]="getFc('repeat')" [readonly]="true" (click)="openAlert()"></app-input-date-period>
        <app-input-time class="ri-pb-3" [control]="getFc('startAt')" [isTmzAffected]="getFc('isTmzAffected')" [isStartTimeSet]="getFc('isStartTimeSet')" [duration]="getFc('duration')" [reminder]="getFc('reminderAt')" [readonly]="true" (click)="openAlert()"></app-input-time>
        <app-input-reminder class="ri-pb-3" [control]="getFc('reminderAt')" *ngIf="calendarEventForm.value.isStartTimeSet" [readonly]="true" (click)="openAlert()"></app-input-reminder>

        <a class="flex items-start ri-pb-4" *ngIf="rawData.hangoutLink || rawData.onlineMeetingUrl" [href]="rawData.hangoutLink || rawData.onlineMeetingUrl" target="_blank">
            <app-svg [name]="calendarType === 'google' ? 'googleMeet' : 'teamsMeet'" [color]="cc.theme.color35"></app-svg>
            <div class="ri-ps-3 w-full">
                <p class="text-16-400 block color-1 mb-0 ri-pb-2">{{ calendarType === 'google' ? cc.texts()['bottomSheet_calendarEvent_googleMeetText'] : cc.texts()['bottomSheet_calendarEvent_teamsMeetText'] }}</p>
            </div>
        </a>
        
        <div class="flex items-start ri-pb-4" (click)="openAlert()">
            <app-svg name="calendar" [color]="cc.theme.color35"></app-svg>
            <div class="ri-ps-3 w-full">
                <p class="text-16-400 color-8 mb-0 ri-pb-2">{{ calendarEventInitial.calendarName }}</p>
                <p class="text-12-400 color-7 mb-0">{{ calendarAccount.email }}</p>
            </div>
        </div>
        <div class="flex items-start ri-pb-4" (click)="openAlert()" *ngIf="rawData.showAs">
            <app-svg name="briefCase" [color]="cc.theme.color35"></app-svg>
            <div class="ri-ps-3 w-full">
                <p class="text-16-400 color-8 mb-0 ri-pb-2 capitalize">{{ rawData.showAs }}</p>
            </div>
        </div>
        <div class="flex items-start ri-pb-4" (click)="openAlert()" *ngIf="rawData.sensitivity">
            <app-svg name="lock" [color]="cc.theme.color35"></app-svg>
            <div class="ri-ps-3 w-full">
                <p class="text-16-400 color-8 mb-0 ri-pb-2 capitalize">{{ rawData.sensitivity }}</p>
            </div>
        </div>
    </form>
</div>
<div class="bottom-section flex items-center justify-end ri-p-4 ri-bt-2">
    <button type="submit" class="btn-text text-16-500 color-1" (click)="closeDialog()">{{ cc.texts()['screen_common_close'] }}</button>
</div>
<div class="btn btn-filter btn-input h-full filter-search" [ngClass]="{'active': control.value}">
    <app-svg name="search" class="search-icon me-1" [color]="control.value ? cc.theme.color12 : cc.theme.color7" height="15px" width="15px"></app-svg>
    <input class="search-input w-full p-0" type="text" [placeholder]="cc.texts()['screen_common_search']" [name]="name+'Search'" [formControl]="control" (input)="signal.set(control.value)" autocomplete="off">
    <div class="clear-icon ms-2" *ngIf="control.value" role="button" (click)="control.setValue('');signal.set('')">
        <app-svg class="d-flex align-items-center justify-content-center" name="close" [color]="cc.theme.color1"></app-svg>
    </div>
</div>
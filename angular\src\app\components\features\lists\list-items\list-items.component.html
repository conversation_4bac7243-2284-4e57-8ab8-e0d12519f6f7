<div class="top-section">
    <h6 class="heading mb-0 truncate">{{ listData.title }}</h6>
    <div class="flex items-center">
        <app-svg [name]="isFilter ? 'filterOutline' : 'filter'" *ngIf="getListItems().length > 0" class="filter-icon ri-ms-6"
            role="button" (click)="toggleFilter()" width="24px" height="24px"></app-svg>
        <app-svg name="multiUser" class="ri-ms-6" width="20px" height="20px" (click)="addCollaborator()"
            *ngIf="listData.isPublic"></app-svg>
        <app-svg name="more" class="more-icon ri-ms-6" [matMenuTriggerFor]="infoMenu" role="button" width="24px"
            height="24px" [color]="cc.theme.color12"></app-svg>
        <app-svg name="close" class="ri-ms-6" role="button" (click)="dialogRef.close()" width="24px"
            height="24px"></app-svg>
        <mat-menu #infoMenu="matMenu" class="me-menu info-menu">
            <button mat-menu-item (click)="toggleFavourite()">
                <span class="text-14-400 color-8 flex items-center"><app-svg
                        [name]="listData.isFav ? 'star' : 'starOutline'" class="ri-pe-3"
                        [color]="cc.theme.color35"></app-svg>Favourite</span>
            </button>
            <button mat-menu-item (click)="$event.stopPropagation();" [matMenuTriggerFor]="actionMenu"
                #meActionTrigger="matMenuTrigger">
                <span class="text-14-400 color-8 flex items-center"><app-svg name="action" class="ri-pe-3"
                        [color]="cc.theme.color35"></app-svg>Actions</span>
            </button>
            <button mat-menu-item (click)="$event.stopPropagation();" [matMenuTriggerFor]="manageMenu"
                #meManageTrigger="matMenuTrigger">
                <span class="text-14-400 color-8 flex items-center"><app-svg name="manage" class="ri-pe-3"
                        [color]="cc.theme.color35"></app-svg>Manage</span>
            </button>
            <button mat-menu-item (click)="$event.stopPropagation();" [matMenuTriggerFor]="shareMenu"
                #meShareTrigger="matMenuTrigger">
                <span class="text-14-400 color-8 flex items-center"><app-svg name="share" class="ri-pe-3"
                        [color]="cc.theme.color35"></app-svg>Share</span>
            </button>
            <button mat-menu-item (click)="addCollaborator()">
                <span class="text-14-400 color-8 flex items-center"><app-svg name="users" class="ri-pe-3"
                        [color]="cc.theme.color35"></app-svg>Collaborate</span>
                <span class="menu-collaborator-add color-35 ri-ms-6"
                    [ngClass]="listData.members?.memberHashedEmails?.length ? 'text-10-400' : 'text-14-400'">{{
                    (listData.members?.memberHashedEmails?.length || 0) > 0 ?
                    listData.members?.memberHashedEmails?.length : '+' }}</span>
            </button>
            <button mat-menu-item (click)="$event.stopPropagation();" [matMenuTriggerFor]="publicMenu"
                #mePublicTrigger="matMenuTrigger">
                <span class="text-14-400 color-8 flex items-center"><app-svg name="multiUser" class="ri-pe-3"
                        [color]="cc.theme.color35"></app-svg>Public</span>
                <span class="menu-public-sign ri-ms-6" [ngClass]="listData.isPublic ? 'public' : 'private'"></span>
            </button>
        </mat-menu>

        <mat-menu #shareMenu="matMenu" class="me-menu info-menu">
            <button mat-menu-item (click)="copyToClipboard()">
                <span class="text-14-400 color-8 flex items-center">Copy Text</span>
                <app-svg name="copy" class="ri-ms-4" [color]="cc.theme.color35"></app-svg>
            </button>
            <button mat-menu-item (click)="downloadImage()">
                <span class="text-14-400 color-8 flex items-center">Download as Image</span>
                <app-svg name="download" class="ri-ms-4" [color]="cc.theme.color35"></app-svg>
            </button>
        </mat-menu>

        <mat-menu #actionMenu="matMenu" class="me-menu info-menu">
            <button mat-menu-item>
                <span class="text-14-400 color-8 flex items-center">Move Items</span>
                <div class="flex items-center">
                    <app-svg name="copyTo" class="ri-ms-4" [color]="cc.theme.color35"
                        (click)="transferListItems('Copy')" role="button"></app-svg>
                    <app-svg name="moveTo" class="ri-ms-4" [color]="cc.theme.color35"
                        (click)="transferListItems('Move')" role="button"></app-svg>
                </div>
            </button>
            <button mat-menu-item (click)="openListForm()">
                <span class="text-14-400 color-8 flex items-center">Clone List</span>
            </button>
        </mat-menu>

        <mat-menu #manageMenu="matMenu" class="me-menu info-menu">
            <button mat-menu-item (click)="openListForm()">
                <span class="text-14-400 color-8 flex items-center">List Info</span>
            </button>
            <button mat-menu-item class="" (click)="$event.stopPropagation()">
                <div class="ri-me-6">
                    <span class="text-14-400 color-8">Add-on</span>
                    <span class="block text-12-400 color-7 ri-pt-2">Shown next to each list item</span>
                </div>
                <app-input-dropdown [signal]="listItemAddonType" [config]="vs.listItemAddonConfig"
                    (select)="updateAddon()" textClass="text-12-400 whitespace-nowrap"
                    type="LISTADDON"></app-input-dropdown>
            </button>
            <button mat-menu-item class="" (click)="$event.stopPropagation()">
                <div class="ri-me-6">
                    <span class="text-14-400 color-8">Show</span>
                    <app-input-dropdown class="ri-pt-6px" [signal]="vs.type()['listItemShow']"
                        [config]="vs.listItemShowConfig" [multiple]="true"
                        [disabled]="vs.type()['listItemShowType']() === 'compact'"
                        textClass="text-12-400" (select)="vs.updateView()"></app-input-dropdown>
                </div>
                <app-input-dropdown [signal]="vs.type()['listItemShowType']" [config]="vs.listItemShowTypeConfig"
                    textClass="text-12-400 whitespace-nowrap" (select)="vs.updateShowType('listItemShowType')"></app-input-dropdown>
            </button>
            <button mat-menu-item (click)="deleteList()">
                <span class="text-14-400 flex items-center color-11">Delete List</span>
            </button>
        </mat-menu>

        <mat-menu #publicMenu="matMenu" class="me-menu info-menu">
            <button mat-menu-item (click)="togglePublic(); $event.stopPropagation();">
                <span class="text-14-400 color-8 flex items-center" class="ri-me-6">Visible to Public</span>
                <app-input-toggle [value]="(listData.isPublic || false)" [readonly]="true"></app-input-toggle>
            </button>
            <button mat-menu-item (click)="cc.copyLink(listData.inviteLink || ''); $event.stopPropagation();"
                [disabled]="!listData.isPublic">
                <span class="text-14-400 flex items-center" [ngClass]="listData.isPublic ? 'color-8' : 'color-10'">Copy
                    Link</span>
                <app-svg name="copy" [color]="listData.isPublic ? cc.theme.color35 : cc.theme.color10"></app-svg>
            </button>
            <button mat-menu-item (click)="viewQrCode(); $event.stopPropagation();" [disabled]="!listData.isPublic">
                <span class="text-14-400 flex items-center"
                    [ngClass]="listData.isPublic ? 'color-8' : 'color-10'">Download QR</span>
                <app-svg name="qrCode" [color]=" listData.isPublic ? cc.theme.color35 : cc.theme.color10"></app-svg>
            </button>
        </mat-menu>
    </div>
</div>

<div class="body-section bg-3 position-relative">
    <div class="filter-block" [ngClass]="{'h-auto filtered': isFilter}">
        <app-filter-search [signal]="searchQuery" [name]="''" class="me-2"></app-filter-search>

        <button class="btn btn-filter btn-toggle me-2" (click)="isCompleted.set(isCompleted() === false ? null : false)"
            [ngClass]="{'active': isCompleted() === false }">Unchecked</button>

        <button class="btn btn-filter btn-toggle me-2" (click)="isCompleted.set(isCompleted() === true ? null : true)"
            [ngClass]="{'active': isCompleted() === true }">Checked</button>
    </div>

    <div class="list-items-block" [ngClass]="isFilter ? 'filter-applied' : 'no-filter'">
        <div class="list-item ri-px-4 ri-py-4" *ngFor="let item of filteredLists(); let i = index;">
            <div class="flex items-center justify-between">
                <div class="">
                    <p class="text-16-400 color-8 mb-0">{{ item.item }}</p>
                    <p class="text-12-400 color-7 mb-0 ri-pt-2"
                        *ngIf="vs.type()['listItemShow']().includes('description') && item.description">{{
                        item.description }}</p>
                    <p class="text-12-400 color-7 mb-0 ri-pt-2"
                        *ngIf="vs.type()['listItemShow']().includes('lastUpdatedBy') || vs.type()['listItemShow']().includes('lastUpdatedAt')">
                        <span *ngIf="vs.type()['listItemShow']().includes('lastUpdatedBy')">{{ getItemLastUpdatedBy(item) }}</span>
                        <span
                            *ngIf="vs.type()['listItemShow']().includes('lastUpdatedBy') && vs.type()['listItemShow']().includes('lastUpdatedAt')">,
                        </span>
                        <span *ngIf="vs.type()['listItemShow']().includes('lastUpdatedAt')">{{ item.lastUpdatedAt |
                            timeShort }}</span>
                    </p>
                </div>
                <div class="flex justify-between items-center ms-3">
                    <app-input-checkmark [id]="'meListItemCheck'+i" [control]="getControl(item.done || false)"
                        (toggle)="updateItemDone(item, $event)"
                        *ngIf="listData.addOnType && listData.addOnType === 'checkbox'"></app-input-checkmark>
                    <span class="custom-text text-14-400 color-7 ri-ms-6 whitespace-nowrap"
                        *ngIf="listData.addOnType && listData.addOnType === 'customText'">
                        {{ item.customText ? item.customText : '---' }}
                    </span>
                    <app-svg name="more" class="more-icon ri-ms-6" [matMenuTriggerFor]="infoMenu" role="button"
                        width="24px" height="24px" [color]="cc.theme.color12"></app-svg>
                    <mat-menu #infoMenu="matMenu" class="me-menu info-menu">
                        <button mat-menu-item class="text-14-400 color-8" (click)="openListItemForm('edit', item)">
                            <span class="flex"><app-svg name="edit" class="ri-pe-3"
                                    [color]="cc.theme.color35"></app-svg>Edit</span>
                        </button>
                        <button mat-menu-item class="text-14-400 color-8" (click)="transferListItem(item, 'Copy')">
                            <span class="flex"><app-svg name="copyTo" class="ri-pe-3"
                                    [color]="cc.theme.color35"></app-svg>Copy</span>
                        </button>
                        <button mat-menu-item class="text-14-400 color-8" (click)="transferListItem(item, 'Move')">
                            <span class="flex"><app-svg name="moveTo" class="ri-pe-3"
                                    [color]="cc.theme.color35"></app-svg>Move</span>
                        </button>
                        <button mat-menu-item class="" (click)="deleteListItem(item)"><span
                                class="text-14-500 color-11 text-center w-full">DELETE ITEM</span></button>
                    </mat-menu>
                </div>
            </div>
        </div>
        <div class="no-result-block h-full flex items-center justify-center flex-col" *ngIf="getListItems().length === 0 || (getListItems().length > 0 && filteredLists().length === 0)">
            <app-svg [name]="getListItems().length === 0 ? 'listItemPlaceholder' : 'searchPlaceholder'"
                [color]="cc.theme.color1"></app-svg>
            <p class="text-16-400 color-8 mb-0 ri-pt-4">No List Items</p>
        </div>
        <div class="extra-block" *ngIf="filteredLists().length > 0"></div>
    </div>
    <!-- <div class="no-result-block flex items-center justify-center flex-col"
      [ngClass]="{'filter-applied': isFilter}" *ngIf="listData.length === 0">
    </div> -->
</div>
<div class="add-button">
    <button class="btn" (click)="openListItemForm('new')"><app-svg name="plus"></app-svg></button>
</div>
<div id="konva-container" class="hidden" style="background-color: #231a1a; border-radius: 16px; padding: 32px;"></div>
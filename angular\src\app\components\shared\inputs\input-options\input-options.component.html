<div class="" [ngClass]="{'readonly': readonly}">
    <div class="input-options-block w-full">
        <div class="option-item flex items-center justify-between" role="button" (click)="openAddOptionDialog(item)" *ngFor="let item of control.value; let i = index;" tabindex="0" #optionItem>
            <p class="text-14-400 color-8 mb-0 ri-pe-4">{{ item.value }}</p>
            <app-svg class="option-delete" name="trash" role="button" [color]="cc.theme.color7" (click)="removeOption(i);$event.stopPropagation()"></app-svg>
        </div>
        <button class="add-option w-full" (click)="openAddOptionDialog()" [disabled]="isAddDisabled()">
            <p class="text-14-400 mb-0 flex items-center justify-center" [ngClass]="isAddDisabled() ? 'color-7' : 'color-2'"><app-svg name="plus" [color]="isAddDisabled() ? cc.theme.color7 : cc.theme.color2" class="ri-me-2" height="14px"></app-svg> {{ cc.texts()['screen_habitSetupAdd_optionAdd'] }}</p>
        </button>
    </div>
</div>
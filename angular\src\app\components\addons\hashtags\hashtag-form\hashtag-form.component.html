<div class="top-section">
    <h6 class="heading mb-0">{{mode === 'new' ? cc.texts()['bottomSheet_publicProfile_hashtagPlaceholder'] : cc.texts()['bottomSheet_hashtagEdit_titleTopBar'] }}</h6>
    <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
</div>
<div class="hashtag-form ri-p-4">
    <form [formGroup]="hashtagForm" #hTagForm="ngForm">
        <app-input-text [control]="getFc('tag')" name="meHashtagName"  [placeholder]="cc.texts()['bottomSheet_hashtagAdd_placeholder']" maxLength="20" icon="hashtag" [onlyCharacterAndNumbers]="true" [toLowercase]="true"></app-input-text>
    </form>
</div>
<!-- <pre class="text-white">{{hashtagForm.value | json}}</pre> -->
<div class="ri-dialog-footer ri-p-4 text-end ri-bt-2">
    <button type="submit" class="btn border-0 p-0" (click)="save()" [disabled]="!hasChanges() || hashtagForm.invalid">
        <app-svg name="send" [color]="!hasChanges() || hashtagForm.invalid ? cc.theme.color10 : cc.theme.color35"></app-svg>
    </button>
</div>
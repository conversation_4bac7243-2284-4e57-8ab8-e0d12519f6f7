<div class="top-section">
    <h6 class="heading mb-0">{{ cc.texts()['bottomSheet_featureSharing_titleTopBar'] }}</h6>
    <div class="">
        <app-svg name="addUser" class="ri-pe-4" role="button" (click)="openCollabFormForm()"></app-svg>
        <app-svg name="close" role="button" (click)="dialogRef.close()"></app-svg>
    </div>
</div>

<div class="body-section bg-3 collab-block">
    <div class="collab-item flex justify-between ri-p-4">
        <p class="text-16-400 color-8 mb-0">{{ entityData.ownerEmail }}</p>
        <p class="text-16-400 color-1 mb-0">{{ cc.texts()['bottomSheet_featureSharing_owner'] }}</p>
    </div>
    <ng-container *ngIf="entityData.members && entityData.members.memberHashedEmails">
        <div class="collab-item flex justify-between ri-p-4" *ngFor="let hashedEmail of entityData.members.memberHashedEmails">
            <p class="text-16-400 color-8 mb-0">{{ entityData.members.membersConfig[hashedEmail].eEmail }}</p>
            <p class="text-16-400 color-1 mb-0">{{ entityData.members.membersConfig[hashedEmail].role }}</p>
        </div>
    </ng-container>
</div>
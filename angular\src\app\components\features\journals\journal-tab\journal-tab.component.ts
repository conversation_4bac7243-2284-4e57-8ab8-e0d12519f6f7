import { CommonModule } from '@angular/common';
import { Component, computed, inject, Input, OnChanges, OnInit, signal, Signal, SimpleChanges, ViewChild, WritableSignal } from '@angular/core';
import { CdkVirtualScrollViewport, ScrollingModule } from '@angular/cdk/scrolling';
import { EntityWithDateGroup, EntitySetup } from '@app/_interfaces/feature.interface';
import { JournalStore, UserStore } from '@app/_stores';
import { JournalBlockComponent } from '../journal-block/journal-block.component';
import { CacheService } from '@app/_services/cache.service';
import { FilterAttachmentComponent } from '@app/components/shared/filters/filter-attachment/filter-attachment.component';
import { FilterDateRangeComponent } from '@app/components/shared/filters/filter-date-range/filter-date-range.component';
import { FilterHashtagComponent } from '@app/components/shared/filters/filter-hashtag/filter-hashtag.component';
import { FilterSearchComponent } from '@app/components/shared/filters/filter-search/filter-search.component';
import { FilterStatusComponent } from '@app/components/shared/filters/filter-status/filter-status.component';
import { FilterTypeComponent } from '@app/components/shared/filters/filter-type/filter-type.component';
import { FilterMoodComponent } from '@app/components/shared/filters/filter-mood/filter-mood.component';
import { AttachmentType, EntityStatus, RepeatFilterType } from '@app/_types/generic.type';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { DragScrollDirective } from '@app/_directives/drag-scroll.directive';
import { MapService } from '@app/_services/map.service';

@Component({
  selector: 'app-journal-tab',
  standalone: true,
  imports: [
    CommonModule,
    ScrollingModule,
    JournalBlockComponent,
    FilterSearchComponent,
    FilterHashtagComponent,
    FilterDateRangeComponent,
    FilterAttachmentComponent,
    FilterTypeComponent,
    FilterStatusComponent,
    FilterMoodComponent,
    SvgComponent,
    DragScrollDirective
  ],
  templateUrl: './journal-tab.component.html',
  styleUrl: './journal-tab.component.scss'
})

export class JournalTabComponent implements OnInit, OnChanges {

  @ViewChild(CdkVirtualScrollViewport) viewport!: CdkVirtualScrollViewport;

  readonly journalStore = inject(JournalStore);
  readonly userStore = inject(UserStore);
  @Input() mode: Signal<'PAST' | 'FUTURE'> = computed(() => 'PAST');
  @Input() show: string[] = [];
  @Input() groupedViewType: string[] = [];
  @Input() filter: boolean = false;
  @Input() activeTab: string = 'journal';
  @Input() groupType: string = 'date';
  @Input() isDataAvailable: boolean = false;
  @Input() descriptionType: string = 'none';

  groupTypeSignal: WritableSignal<string> = signal<string>('date');
  hiddenStatus: EntityStatus[] = ['overdue', 'skipped', 'incomplete', 'none'];

  filterSearch = signal<string>('');
  filterType = signal<RepeatFilterType>('all');
  filterHashtag = signal<string[]>([]);
  filterAttachment = signal<AttachmentType[]>([]);
  filterStatus = signal<EntityStatus>('all');
  filterMood = signal<number[]>([]);
  startDate = signal<Date | null>(null);
  endDate = signal<Date | null>(null);

  scrollOffset: number = 0;

  journals: Signal<EntityWithDateGroup[]> = computed(() => {
    const firstDateString = this.journalStore.firstDateString();
    const firstDate = new Date(firstDateString || '');
    const groupType = this.groupTypeSignal();
    const groupedData = new Map<string, EntityWithDateGroup>();

    let dateStrings: string[] = [];
    switch (this.mode()) {
      case 'PAST':
        dateStrings = this.cc.getPastDates(this.startDate() || firstDate, this.endDate() ? this.endDate() : null);
        break;
      case 'FUTURE':
        dateStrings = this.endDate() === null && this.startDate() === null ? this.cc.futureDates() : this.cc.getFutureDates(this.endDate(), this.startDate());
        break;
    }

    dateStrings.forEach(dateString => {
      const entities = this.journalStore.getComputedEntities(dateString, false, groupType as 'date' | 'hashtag');
      let allEntities = [...entities.withoutTime, ...entities.withTime];
      const entityGroupData = entities.groupedData;

      switch (groupType) {
        case 'date':
          allEntities = this.applyFilter(allEntities, dateString);
          if (!groupedData.has(dateString)) {
            groupedData.set(dateString, {
              id: dateString,
              name: dateString,
              data: [],
              dateMap: {}
            });
          }
          groupedData.get(dateString)!.data.push(...allEntities);
          if (!groupedData.get(dateString)!.dateMap[dateString]) {
            groupedData.get(dateString)!.dateMap[dateString] = [];
          }
          groupedData.get(dateString)!.dateMap[dateString].push(...allEntities);
          break;
        case 'hashtag':
          const tagId = Object.keys(entityGroupData);
          tagId.forEach(key => {
            const entities = this.applyFilter(entityGroupData[key], dateString);
            if (!groupedData.has(key)) {
              groupedData.set(key, {
                id: key,
                name: key,
                data: [],
                dateMap: {}
              });
            }
            groupedData.get(key)!.data.push(...entities);
            if (!groupedData.get(key)!.dateMap[dateString]) {
              groupedData.get(key)!.dateMap[dateString] = [];
            }
            groupedData.get(key)!.dateMap[dateString].push(...entities);
          });
          break;
        case 'mood':
          const moodId = Object.keys(entityGroupData);
          moodId.forEach(key => {
            const entities = this.applyFilter(entityGroupData[key], dateString);
            if (!groupedData.has(key)) {
              groupedData.set(key, {
                id: key,
                name: key,
                data: [],
                dateMap: {}
              });
            }
            groupedData.get(key)!.data.push(...entities);
            if (!groupedData.get(key)!.dateMap[dateString]) {
              groupedData.get(key)!.dateMap[dateString] = [];
            }
            groupedData.get(key)!.dateMap[dateString].push(...entities);
          });
          break;
      }
    });
    const orderedKeys = [...groupedData.keys()];
    const result = orderedKeys
      .filter(key => key !== '')                     // all non-empty keys, in original order
      .map(key => groupedData.get(key)!)
      .concat(
        groupedData.has('')
          ? [groupedData.get('')!]                  // add the empty key group last, if it exists
          : []
      );
    return result;
  });

  constructor(public cc: CacheService, private mapService: MapService) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['activeTab']) {
      this.viewport?.checkViewportSize();
      this.viewport?.scrollToOffset(this.scrollOffset);
    }

    if (changes['groupType']) {
      this.groupTypeSignal.set(this.groupType);
    }
  }

  ngOnInit() {

  }

  getGroupName(group: EntityWithDateGroup): string {
    switch (this.groupTypeSignal()) {
      case 'date':
        return this.cc.getFormattedDate(group.name, 'd MMM y, EEE');
      case 'hashtag':
        return this.userStore.tagMap()[group.id] ? this.userStore.tagMap()[group.id] : this.cc.texts()['screen_common_groupByNoHashtags'];
      case 'mood':
        return group.name ? this.mapService.moodMap()[+group.name] : this.cc.texts()['tab_pastJournal_groupByNoMoods'];
      default:
        return group.name;
    }
  }

  getIsOpen() {
    return this.groupedViewType.includes('collapsedView') ? 'false' : 'true';
  }

  onScrolledIndexChange(index: number) {
    this.scrollOffset = this.viewport?.measureScrollOffset() ?? 0;
  }

  applyFilter(entities: EntitySetup[], dateString: string): EntitySetup[] {
    // Filters
    const search = this.filterSearch().toLowerCase();
    const type = this.filterType();
    const hashtags = this.filterHashtag();
    const attachments = this.filterAttachment();
    const status = this.filterStatus();
    const moods = this.filterMood();
    return entities.filter(entity => {
      // const journalSetup = this.journalStore.journalMap().setups[entity.id];
      const journalData = this.journalStore.journalMap().setups[entity.id] && this.journalStore.journalMap().setups[entity.id][dateString] || null;
      const journal = journalData ? { ...journalData } : null;
      const tags = [...entity.tags, ...(journal ? journal.tags : [])];
      const matchesSearch = !search || entity.title.toLowerCase().includes(search);
      const matchesType = type === 'all' || entity.repeatType === type;
      const matchesHashtag = hashtags.length === 0 || (entity.tags || []).some(tag => hashtags.includes(tag));
      const matchesStatus = status === 'all' || entity.status === status;
      const matchesAttachment = attachments.length === 0 || (journal ? (journal?.attachments || []).some(att => attachments.includes(att.fileType)) : false);
      const matchesMood = moods.length === 0 || (journal && journal?.emotion !== null ? moods.includes(journal?.emotion || 0) : false);

      return matchesSearch && matchesType && matchesHashtag && matchesStatus && matchesAttachment && matchesMood;
    });
  }

  isFiltered() {
    return this.filterSearch() || this.filterType() !== 'all' || this.filterHashtag().length || this.filterAttachment().length || this.filterStatus() !== 'all' || this.filterMood().length || this.startDate() || this.endDate();
  }

  clearFilter() {
    this.filterSearch.set('');
    this.filterType.set('all');
    this.filterHashtag.set([]);
    this.filterAttachment.set([]);
    this.filterStatus.set('all');
    this.filterMood.set([]);
    this.startDate.set(null);
    this.endDate.set(null);
  }

  objectKeys(obj: { [key: string]: EntitySetup[] }) {
    return Object.keys(obj);
  }

  trackByForEntity(index: number, entity: EntitySetup) {
    return entity.id;
  }

  trackByForGroup(index: number, group: EntityWithDateGroup) {
    return group.id;
  }

  trackByForDateString(index: number, dateString: string) {
    return dateString;
  }

  toggleChildGroup(element: HTMLElement, groupId: string) {
    const isOpened = element.getAttribute('isOpen');
    const groupedElement = document.getElementById(groupId);
    if (!groupedElement) {
      return
    }
    if (isOpened === 'false') {
      groupedElement.setAttribute('isGroupOpened', 'true');
      element.setAttribute('isOpen', 'true');
    } else {
      groupedElement.setAttribute('isGroupOpened', 'false');
      element.setAttribute('isOpen', 'false');
    }
  }
}

<div class="flex items-start w-full" [ngClass]="{'readonly': readonly}" role="button"
    [matMenuTriggerFor]="inputAlertToneMenu">
    <app-svg name="sound" [color]="cc.theme.color35" class="ri-pe-4" *ngIf="!hideIcon"></app-svg>
    <div>
        <p class="text-14-400 mb-0" [ngClass]="{ 'color-8': control.value !== 'none', 'color-7': control.value === 'none' }">{{ control.value === 'none' ? placeholder() : alertTone[control.value] }}</p>
        <p class="text-12-400 color-7 mb-0 ri-pt-2" *ngIf="control.value !== 'none'">{{ cc.interpolateText('screen_habitSetupAdd_mevolveSound', { VolumeRange: volume.value + '%' }) }}</p>
    </div>
</div>
<mat-menu #inputAlertToneMenu="matMenu" class="me-menu ri-w-350px">
    <button mat-menu-item *ngFor="let tone of alertTones; let i = index;" (click)="selectTone(tone, $event)">
        <div class="flex justify-between">
            <p class="text-14-400 color-8 mb-0 ri-pe-2">{{ alertTone[tone] }}</p>
            <app-svg name="tick" *ngIf="tone === control.value" [color]="cc.theme.color35" style="height: 16px; width: 16px;"></app-svg>
        </div>
    </button>
</mat-menu>
import { CommonModule } from '@angular/common';
import { Component, Inject, inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { dbVersion } from '@app/_configs/db.config';
import { CalendarGroup, CalendarIntegration } from '@app/_interfaces/calendar-integration.interface';
import { CacheService } from '@app/_services/cache.service';
import { CryptographyService } from '@app/_services/cryptography.service';
import { FirebaseFunctionService } from '@app/_services/firebase-function.service';
import { CalendarIntegrationStore, UserStore } from '@app/_stores';
import { getNewId, getNormalizedCurrentTimezone } from '@app/_utils/utils';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import {
  getAuth,
} from 'firebase/auth';
import { CalendarAccountFormComponent } from '../calendar-account-form/calendar-account-form.component';
import { Subject, takeUntil } from 'rxjs';
import { environment } from '@environments/environment';
import { AlertService } from '@app/_services/alert.service';

@Component({
  selector: 'app-calendar-connect',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent
  ],
  templateUrl: './calendar-connect.component.html',
  styleUrl: './calendar-connect.component.scss'
})

export class CalendarConnectComponent {

  readonly userStore = inject(UserStore);
  readonly calendarStore = inject(CalendarIntegrationStore);
  unSubscribe = new Subject<void>();
  googleCalendarClientId = environment.googleCalendarClientId;
  microsoftCalendarClientId = environment.microsoftCalendarClientId;

  constructor(
    private dialogRef: MatDialogRef<CalendarConnectComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { type: 'google' | 'microsoft' },
    private fbfs: FirebaseFunctionService,
    private cryptoService: CryptographyService,
    public dialog: MatDialog,
    public cc: CacheService,
    public alertService: AlertService
  ) {

  }

  connectGoogle() {
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      console.error('User not authenticated with Firebase.');
      return;
    }

    // Load the Google Identity Services script if not already loaded
    if (!document.getElementById('google-identity-script')) {
      const script = document.createElement('script');
      script.id = 'google-identity-script';
      script.src = 'https://accounts.google.com/gsi/client';
      script.onload = () => this.initGoogleAuth(user);
      document.head.appendChild(script);
    } else {
      this.initGoogleAuth(user);
    }
  }

  connectMicrosoft() {
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      console.error('User not authenticated with Firebase.');
      return;
    }

    const domain = window.location.origin;
    const state = crypto.randomUUID();
    const redirectUri = `${domain}/me-calendar-auth`;

    const authUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/authorize?` + new URLSearchParams({
      client_id: this.microsoftCalendarClientId,
      response_type: 'code',
      redirect_uri: redirectUri,
      scope: 'openid email User.Read offline_access Calendars.ReadWrite',
      response_mode: 'query',
      state: state,
      prompt: 'select_account'
    }).toString();

    // Open Microsoft login in a popup
    const width = 500;
    const height = 600;

    const left = window.screenX + (window.innerWidth - width) / 2;
    const top = window.screenY + (window.innerHeight - height) / 2;
    const popup = window.open(authUrl, '_blank', `width=${width},height=${height},left=${left},top=${top}`);

    // Listen for the postMessage from the popup
    window.addEventListener('message', (event) => {
      if (event.origin !== window.location.origin) return;
      const { code, receivedState } = event.data || {};
      if (code && receivedState === state) {
        const serverAuthCode = code;

        const payload = {
          serverAuthCode,
          type: 'microsoft',
          uid: user.uid,
          email: user.email,
          name: user.displayName,
          id: getNewId(),
          redirectUri: redirectUri
        };

        this.getCalendarDetails(payload);
      }
    });
  }

  initGoogleAuth(user: any) {
    // @ts-ignore - Google Identity Services
    const client = google.accounts.oauth2.initCodeClient({
      client_id: this.googleCalendarClientId,
      scope: 'https://www.googleapis.com/auth/calendar https://www.googleapis.com/auth/calendar.events email profile',
      ux_mode: 'popup',
      redirect_uri: 'postmessage',
      callback: async (response: { code: string | null }) => {
        if (response.code) {
          const serverAuthCode = response.code;
          const payload = {
            serverAuthCode,
            type: 'google',
            uid: this.userStore.user?.() ? this.userStore.user()?.uid : '',
            email: user.email,
            name: user.displayName,
            id: getNewId(),
            redirectUri: 'postmessage'
          };
          this.getCalendarDetails(payload);
        }
      }
    });

    client.requestCode();
  }

  async getCalendarDetails(credentials: any) {
    const activeAccounts = this.calendarStore.activeAccounts();
    const existingCalendar = activeAccounts.find((account) => account.email === credentials.email);
    if (existingCalendar) {
      this.alertService.alert(this.cc.texts()['overlay_duplicateCalendarAccount_title'], this.cc.texts()['overlay_duplicateCalendarAccount_content'], this.cc.texts()['screen_common_ok']);
      return;
    } else {
      try {
        this.cc.isLoading = true;
        const callable = this.fbfs.getCallable('calendar-getCalendarDetails');
        const response: any = await callable(credentials);
        const calendarDetails = JSON.parse(response.data.data);
        this.openCalendarAccountForm(calendarDetails);
      } catch (error) {
        console.error('❌ Calendar connection failed:', error);
      } finally {
        this.cc.isLoading = false;
      }
    }
  }

  openCalendarAccountForm(calendarAccountData: CalendarIntegration) {
    const setupCalendarDialog = this.dialog.open(CalendarAccountFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      minHeight: '90vh',
      disableClose: true,
      data: {
        mode: 'new',
        value: calendarAccountData,
      },
    })
    return setupCalendarDialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.dialogRef.close();
      }
    });;
  }

  async createNewCalendarPayload(calendarDetails: CalendarIntegration) {
    calendarDetails.uid = this.userStore.user!()?.uid || '';
    calendarDetails.docVer = dbVersion;
    calendarDetails.docCollection = 'calendarIntegrations';
    calendarDetails.source = 'client';
    calendarDetails.deletedAt = null;
    calendarDetails.createdAt = new Date();
    calendarDetails.sessionId = getNewId();

    const calendarConfig: { [key: string]: string } = {
      syncTodos: 'syncNotifications',
      syncHabits: 'syncNotifications',
      syncJournals: 'syncNotifications',
    }
    calendarDetails.calendarGroups = calendarDetails.calendarGroups.map((group: CalendarGroup) => {
      if (group.title !== 'Mevolve') {
        group.syncStatus = 'syncNotifications';
        calendarConfig[group.id] = 'syncNotifications';
      }
      return group;
    })
    const syncRequest = this.cryptoService.decrypt(calendarDetails, true)
    const payload = {
      uid: this.userStore.user?.() ? this.userStore.user()?.uid : '',
      calendarDetails: JSON.stringify(syncRequest),
      calAccId: calendarDetails.id,
      mevolveCalId: this.getMevolveCalId(calendarDetails.calendarGroups),
      refreshKey: calendarDetails.refreshToken,
      timeZone: getNormalizedCurrentTimezone(),
      eventsList: null,
      tasksList: null,
      type: calendarDetails.calendarType,
      calendarConfig: JSON.stringify(calendarConfig),
      redirectUri: 'postmessage'
    };

    try {
      const callable = this.fbfs.getCallable('calendar-saveNewCalendar');
      const response: any = await callable(payload);
      console.log('✅ Calendar Connect Response:', response);
    } catch (error) {
      console.error('❌ Calendar connection failed:', error);
    }
  }

  getMevolveGroup(calendarGroup: CalendarGroup[]): CalendarGroup | null {
    if (!calendarGroup.length) {
      return null;
    }

    return calendarGroup.find(group =>
      group.title === 'Mevolve'
    ) || null;
  }

  getMevolveCalId(calendarGroup: CalendarGroup[]): string | null {
    const mevolveGroup = this.getMevolveGroup(calendarGroup);
    if (!mevolveGroup) {
      return null;
    }
    return mevolveGroup.id;
  }

  closeDialog() {
    this.dialogRef.close();
  }
}

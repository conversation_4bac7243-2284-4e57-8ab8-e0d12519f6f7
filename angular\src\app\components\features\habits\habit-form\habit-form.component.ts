import { CommonModule } from '@angular/common';
import { Component, computed, inject, Inject, signal, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, FormsModule, NgForm, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { Attachment } from '@app/_interfaces/generic.interface';
import { Habit, HabitNumericAnswer, HabitSetup, HabitTimerAnswer } from '@app/_interfaces/habit.interface';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';
import { DependencyService } from '@app/_services/dependency.service';
import { FirebaseService } from '@app/_services/firebase.service';
import { UtilsService } from '@app/_services/utils.service';
import { AttachmentListComponent } from '@app/components/addons/attachments/attachment-list/attachment-list.component';
import { HashtagComponent } from '@app/components/addons/hashtags/hashtag/hashtag.component';
import { InputCheckmarkAdvancedComponent } from '@app/components/shared/inputs/input-checkmark-advanced/input-checkmark-advanced.component';
import { InputFileComponent } from '@app/components/shared/inputs/input-file/input-file.component';
import { InputHashtagComponent } from '@app/components/shared/inputs/input-hashtag/input-hashtag.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import _ from 'lodash';
import { Subject, takeUntil, timer } from 'rxjs';
import { HabitStore } from '@app/_stores';
import { HabitSetupFormComponent } from '../habit-setup-form/habit-setup-form.component';
import { HabitType } from '@app/_types/generic.type';
import { SmartTimeDiffPipe } from '@app/_pipes/smart-time-diff.pipe';
import { InputTextEditorComponent } from '@app/components/shared/inputs/input-text-editor/input-text-editor.component';
import { ParseSecondsPipe } from '@app/_pipes/parse-seconds.pipe';
import { getNumStrId } from '@app/_utils/utils';
import { HabitNumericValueComponent } from '../habit-numeric-value/habit-numeric-value.component';

@Component({
  selector: 'app-habit-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SvgComponent,
    InputTextEditorComponent,
    InputHashtagComponent,
    AttachmentListComponent,
    InputFileComponent,
    MatMenuModule,
    InputCheckmarkAdvancedComponent,
    SmartTimeDiffPipe,
    ParseSecondsPipe
  ],
  templateUrl: './habit-form.component.html',
  styleUrl: './habit-form.component.scss'
})

export class HabitFormComponent {
  @ViewChild('haForm') haForm!: NgForm;
  unSubscribe = new Subject<void>();
  habitForm: FormGroup;
  habitInitial: Habit;
  mode: 'new' | 'edit';
  readonly habitStore = inject(HabitStore);
  newAttachments: FormArray = this.fb.array([]);
  deletedAttachments: FormArray = this.fb.array([]);
  setup: HabitSetup;
  isDescription: boolean = false;
  habitType: HabitType;
  duration: { year: number, month: number, day: number, hour: number, minute: number, second: number, totalSeconds: number } | null;
  timerAnswers = signal<HabitTimerAnswer[]>([]);
  numericAnswers = signal<HabitNumericAnswer[]>([]);
  reversedTimerAnswers = computed(() =>
    this.timerAnswers().slice().reverse()
  );
  reversedNumericAnswers = computed(() =>
    this.numericAnswers().slice().reverse()
  );
  totalSecondsSpent = computed(() => {
    return this.ds.getTotalSecondsSpent(this.timerAnswers());
  });
  totalNumericValue = computed(() => {
    let total = 0;

    this.numericAnswers().forEach((answer: HabitNumericAnswer) => {
      total += answer.value;
    });

    return total;
  });

  durationRepeatCount = signal(5);
  divisionAngles = computed(() => {
    const angleStep = 360 / this.durationRepeatCount();
    return Array.from({ length: this.durationRepeatCount() }, (_, i) => i * angleStep - 180);
  });

  startTimerDate = signal<Date | null>(null);
  timerSeconds = signal<number>(0);

  totalTimePercentage = computed<number>(() => {
    if (!this.duration) return 0;

    const totalSeconds = this.duration.totalSeconds * this.setup?.durationRepeatCount;
    if (!totalSeconds) return 0;

    let spent = this.totalSecondsSpent();
    const startDate = this.startTimerDate();
    if (startDate) {
      const additionalSeconds = this.timerSeconds(); // your manually tracked seconds
      spent += Math.max(0, additionalSeconds); // ✅ add this instead of ongoing time
    }

    const percentage = (spent / totalSeconds) * 100;

    return Math.floor(percentage); // allow > 100%
  });

  totalNumericPercentage = computed<number>(() => {
    if (!this.setup.numericGoal) return 0;

    const percentage = (this.totalNumericValue() / this.setup.numericGoal) * 100;

    return Math.floor(percentage);
  });

  timerInterval: any;

  constructor(
    public dialogRef: MatDialogRef<HabitFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'new' | 'edit', setupId: string, value: Habit, dateString: string },
    private fb: FormBuilder,
    public cc: CacheService,
    private utilsService: UtilsService,
    public dialog: MatDialog,
    private alertService: AlertService,
    private ds: DependencyService,
    private fbService: FirebaseService,
  ) {
    this.mode = data.mode;
    this.setup = this.habitStore.idToSetup()[this.data.setupId];
    this.habitType = this.setup.habitType;
    this.habitInitial = data.mode == 'new' ? this.initiateForm() : this.initiateForm(data.value);
    this.duration = this.habitType === 'timer' && this.setup.timerGoal ? this.ds.parseMinutes(this.setup.timerGoal) : null;
    this.durationRepeatCount.set(this.setup.durationRepeatCount || 1);

    console.log("i am called data-->>, data", data.value);

    if (this.habitInitial.description) {
      this.isDescription = true;
    }

    this.habitForm = this.fb.group({
      id: new FormControl(this.habitInitial.id, Validators.required),
      description: new FormControl(this.habitInitial.description),
      dueAt: new FormControl(this.habitInitial.dueAt),
      booleanAnswer: new FormControl(this.habitInitial.booleanAnswer),
      numericAnswer: new FormControl(this.habitInitial.numericAnswer),
      timerAnswer: new FormControl(this.habitInitial.timerAnswer),
      singleAnswer: new FormControl(this.habitInitial.singleAnswer),
      multipleAnswer: new FormControl(this.habitInitial.multipleAnswer),
      tags: new FormControl(this.habitInitial.tags),
      attachments: this.fb.array([]),
      setupId: new FormControl(this.habitInitial.setupId),
      uid: new FormControl(this.habitInitial.uid, Validators.required)
    });

    if (this.habitInitial.timerAnswer) {
      this.timerAnswers.set(this.habitInitial.timerAnswer);
    }
    if (this.habitInitial.numericAnswer) {
      this.numericAnswers.set(this.habitInitial.numericAnswer);
    }

    if (this.habitInitial.attachments && this.habitInitial.attachments.length !== 0) {
      this.clearAttachments();
      this.habitInitial.attachments.forEach((attach: Attachment) => {
        this.attachments.push(this.ds.addAttachmentForm(attach));
      });
    }

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  startTimer() {
    const now = new Date();
    this.startTimerDate.set(now);
    this.timerInterval = setInterval(() => {
      this.timerSeconds.update((prev) => prev + 1);
      if (this.duration && (this.totalSecondsSpent() + this.timerSeconds()) >= (this.duration.totalSeconds * this.setup?.durationRepeatCount)) {
        this.stopTimer();
      }
    }, 1000);
  }

  stopTimer() {
    const startTimerDate = this.startTimerDate();
    this.startTimerDate.set(null);
    this.timerSeconds.set(0);
    clearInterval(this.timerInterval);
    this.habitForm.get('timerAnswer')?.setValue([...(this.habitForm.value.timerAnswer || []), {
      id: getNumStrId(),
      startTimestamp: startTimerDate,
      endTimestamp: new Date(),
      durationRepeatCount: 1
    }]);
    this.timerAnswers.set(this.habitForm.value.timerAnswer);
    this.updateTimeValue();
  }

  updateTimeValue() {
    const habitData: Habit = this.mode === 'new' ? this.habitStore.getNewHabit() : this.data.value;
    const updatedHabit = { ...habitData, timerAnswer: this.habitForm.value.timerAnswer };

    if (this.mode === 'new') {
      this.mode = 'edit';
      this.habitStore.addHabit(updatedHabit);
    } else if (this.mode === 'edit') {
      this.habitStore.updateHabits([updatedHabit]);
    }
    this.data.value = updatedHabit;
    this.habitInitial = this.initiateForm(updatedHabit);
  }

  addNumericValue(value?: HabitNumericAnswer) {
    const dialogRef = this.dialog.open(HabitNumericValueComponent, {
      width: '100%',
      maxWidth: '500px',
      disableClose: true,
      data: {
        mode: value ? 'edit' : 'new',
        value: value ? value : null,
        unit: this.setup.numericUnit
      }
    });

    dialogRef.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(async (result) => {
      if (result) {
        const numericAnswer = this.habitForm.get('numericAnswer')?.value;
        if (result.type === 'ADD') {
          this.habitForm.get('numericAnswer')?.setValue(numericAnswer ? [...numericAnswer, result.value] : [result.value]);
          this.numericAnswers.set(this.habitForm.value.numericAnswer);
        } else if (result.type === 'EDIT') {
          this.habitForm.get('numericAnswer')?.setValue(numericAnswer.map((v: HabitNumericAnswer) => v.id === result.value.id ? result.value : v));
          this.numericAnswers.set(this.habitForm.value.numericAnswer);
        }
      }
    });
  }

  deleteTimerValue(value: HabitTimerAnswer) {
    if (this.habitForm.value.timerAnswer.length === 1) {
      this.habitForm.get('timerAnswer')?.setValue(null);
      this.timerAnswers.set([]);
    } else {
      this.habitForm.get('timerAnswer')?.setValue(this.habitForm.value.timerAnswer.filter((v: HabitTimerAnswer) => v.id !== value.id));
      this.timerAnswers.set(this.habitForm.value.timerAnswer);
    }
  }

  deleteNumericValue(value: HabitNumericAnswer) {
    if (this.habitForm.value.numericAnswer.length === 1) {
      this.habitForm.get('numericAnswer')?.setValue(null);
      this.numericAnswers.set([]);
    } else {
      this.habitForm.get('numericAnswer')?.setValue(this.habitForm.value.numericAnswer.filter((v: HabitNumericAnswer) => v.id !== value.id));
      this.numericAnswers.set(this.habitForm.value.numericAnswer);
    }
  }

  get attachmentValues(): any[] {
    const attachments = [...this.attachments.value, ...this.newAttachments.value];
    return attachments;
  }

  get attachments(): FormArray {
    return this.habitForm.get('attachments') as FormArray;
  }

  clearAttachments() {
    while (this.attachments.length !== 0) {
      this.attachments.removeAt(0)
    }
  }

  get basePath(): string {
    return `userData/attachments/${this.cc.user ? this.cc.user.uid : ''}/habitActions/${this.habitInitial.id}`;
  }

  initiateForm(habit?: Habit): Habit {
    return {
      id: habit ? habit.id : this.utilsService.getNewId(),
      description: habit ? habit.description : '',
      dueAt: habit ? habit.dueAt : this.data.dateString ? this.utilsService.getCustomDate(this.data.dateString) : this.utilsService.getCustomDate(),
      booleanAnswer: habit ? habit.booleanAnswer : null,
      numericAnswer: habit ? habit.numericAnswer : null,
      timerAnswer: habit ? habit.timerAnswer : null,
      singleAnswer: habit ? habit.singleAnswer : null,
      multipleAnswer: habit ? habit.multipleAnswer : null,
      tags: habit ? habit.tags : [],
      attachments: habit ? habit.attachments : [],
      setupId: this.data.setupId,
      uid: this.cc.user.uid,
    }
  }

  trackByIndex = (index: number) => index;

  getFc(fcName: string): FormControl {
    return this.habitForm.get(fcName) as FormControl;
  }

  selectOption(option: number) {
    if (this.habitType === 'single') {
      this.habitForm.get('singleAnswer')?.setValue(option);
    } else if (this.habitType === 'multiple') {
      const current: number[] = this.getFc('multipleAnswer').value || [];
      if (current && current.includes(option)) {
        this.getFc('multipleAnswer').setValue(current.filter((id: number) => id !== option));
      } else {
        const values = [...current, option];
        this.getFc('multipleAnswer').setValue(values);
      }
    }
  }

  hasChanges(): boolean {
    const initial = _.cloneDeep(this.habitInitial);
    const current = _.cloneDeep(this.habitForm.value);
    if (initial.attachments?.length !== current.attachments?.length || this.newAttachments.length > 0 || this.deletedAttachments.length > 0) return true;
    // initial.attachments.sort((a: { key: string; }, b: { key: string; }) => (a.key > b.key ? 1 : -1));
    // current.attachments.sort((a: { key: string; }, b: { key: string; }) => (a.key > b.key ? 1 : -1));
    return !_.isEqual(initial, current);
  }

  hasValue(): boolean {
    return (this.habitForm.value.description.trim() !== '' || this.habitForm.value.emotion !== null || this.habitForm.value.attachments.length > 0);
  }

  deleteAttachments(attachments: Attachment[]) {
    attachments?.forEach(attach => {
      const attachmentFormArray = attach.status === 'local' ? this.newAttachments : this.attachments;
      const index = attachmentFormArray.controls.findIndex(control => control.get('id')?.value === attach.id);
      if (index !== - 1) {
        attachmentFormArray.removeAt(index);
      }
      if (attach.status === 'cloud') {
        this.deletedAttachments.push(this.ds.addAttachmentForm(attach));
      }
    });
  }

  editSetup() {
    const setupDialog = this.dialog.open(HabitSetupFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      minHeight: '90vh',
      disableClose: true,
      data: {
        mode: 'edit',
        value: this.setup,
        habitType: this.setup.habitType,
      },
    });
  }

  async save() {
    this.cc.isLoading = true;
    if (!this.habitForm.value.id) return;
    if (this.newAttachments.length > 0) {
      const attachmentsRecord: Record<number, { attachments: { path: string; file: Uint8Array }[]; dek?: string }> = {};
      this.newAttachments.value.forEach((attachment: Attachment) => {
        const originalPath = `${this.basePath}/originalFiles/${attachment.id}.${attachment.format}`;
        const files = [{ path: originalPath, file: attachment.originalFile as Uint8Array }];
        if (attachment.fileType === 'image') {
          const optimizedPath = `${this.basePath}/optimizedFiles/${attachment.id}.${attachment.format}`;
          const thumbnailPath = `${this.basePath}/thumbnails/${attachment.id}.${attachment.format}`;
          files.push({ path: optimizedPath, file: attachment.optimizedFile as Uint8Array });
          files.push({ path: thumbnailPath, file: attachment.thumbnail as Uint8Array });
        }
        attachmentsRecord[attachment.id] = { attachments: files, dek: this.data?.value?.encData?.dek || '' };
      });
      const uploadRecord = await this.fbService.uploadFilesAsRecord(attachmentsRecord);
      for (const attach of this.newAttachments.value) {
        if (attach.id in uploadRecord && uploadRecord[attach.id]) {
          const updatedAttach: Attachment = {
            ...attach,
            status: 'cloud',
          };
          this.attachments.push(this.ds.addAttachmentForm(updatedAttach));
        }
      }
    }

    if (this.deletedAttachments.length > 0) {
      const deletedAttachmentRecord: Record<number, string[]> = {};
      this.deletedAttachments.value.forEach((attachment: Attachment) => {
        const originalPath = `${this.basePath}/originalFiles/${attachment.id}.${attachment.format}`;
        const filePaths: string[] = [originalPath];
        if (attachment.fileType === 'image') {
          const optimizedPath = `${this.basePath}/optimizedFiles/${attachment.id}.${attachment.format}`;
          const thumbnailPath = `${this.basePath}/thumbnails/${attachment.id}.${attachment.format}`;
          filePaths.push(optimizedPath);
          filePaths.push(thumbnailPath);
        }
        deletedAttachmentRecord[attachment.id] = filePaths;
      });
      await this.fbService.deleteFilesAsRecord(deletedAttachmentRecord);
    }

    const habitData: Habit = this.mode === 'new' ? this.habitStore.getNewHabit() : this.data.value;
    const updatedHabit = { ...habitData, ...this.habitForm.value };

    if (this.mode === 'new') {
      this.habitStore.addHabit(updatedHabit);
    } else if (this.mode === 'edit') {
      this.habitStore.updateHabits([updatedHabit]);
    }

    this.cc.isLoading = false;
    this.dialogRef.close();
  }

  openHashtagsDialog() {
    const dialog = this.dialog.open(HashtagComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        values: this.habitForm.value.tags,
        type: 'map'
      },
    })

    dialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.habitForm.get('tags')?.setValue(result);
      }
    });
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }

  reset() {
    this.habitForm.reset();
    this.haForm.resetForm();
    this.habitForm.patchValue(this.habitInitial);
  }

  ngOnDestroy() {
    this.unSubscribe?.complete();
    this.unSubscribe?.next();
  }
}

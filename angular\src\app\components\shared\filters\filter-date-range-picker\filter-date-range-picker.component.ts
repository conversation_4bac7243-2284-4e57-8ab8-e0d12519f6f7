import { Component, computed, EventEmitter, Inject, Input, Output, Signal, signal, WritableSignal } from '@angular/core';
import { InputCalendarComponent, InputCalendarModel } from '../../inputs/input-calendar/input-calendar.component';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { CacheService } from '@app/_services/cache.service';
import { CommonModule } from '@angular/common';
import { SvgComponent } from '../../svg/svg.component';
import { AlertService } from '@app/_services/alert.service';

@Component({
  selector: 'app-filter-date-range-picker',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent
  ],
  templateUrl: './filter-date-range-picker.component.html',
  styleUrl: './filter-date-range-picker.component.scss'
})

export class FilterDateRangePickerComponent {

  @Input() startDate: WritableSignal<Date | null> = signal<Date | null>(null);
  @Input() endDate: WritableSignal<Date | null> = signal<Date | null>(null);

  infoText: Signal<string> = computed(() => {
    const start = this.startDate?.();
    const end = this.endDate?.();
    if (start && end) {
      const startOfDay = new Date(start);

      const endOfDay = new Date(end);

      const diff = endOfDay.getTime() - startOfDay.getTime();
      const days = Math.ceil(diff / (1000 * 60 * 60 * 24));

      // return `${days} day${days > 1 ? 's' : ''}`;
      return days > 1 ? this.cc.texts()['overlay_dateRangePicker_previewSameDateToDateSelect'] : this.cc.interpolateText('screen_common_daysCount', { daysCount: days.toString() });
    } else {
      if (start) {
        return this.cc.texts()['overlay_dateRangePicker_previewBeginningToNullDate'];
      } else if (end) {
        const formatted = end.toLocaleDateString('en-GB', {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        });
        return this.cc.interpolateText('overlay_dateRangePicker_previewBeginningToEndDateSelected', { date: formatted });
      } else {
        return this.cc.texts()['overlay_dateRangePicker_previewNoDate'];
      }
    }
  });

  constructor(
    private dialog: MatDialog,
    public dialogRef: MatDialogRef<FilterDateRangePickerComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { startDate: Date | null, endDate: Date | null },
    public cc: CacheService,
    private alertService: AlertService
  ) {
    this.startDate.set(this.data.startDate);
    this.endDate.set(this.data.endDate);

    dialogRef.backdropClick().subscribe(async () => {
      this.closeDialog();
    });
  }

  openInputCalendar(type: 'start' | 'end') {
    const dialogRef = this.dialog.open(InputCalendarComponent, {
      maxWidth: '350px',
      width: '100%',
      minWidth: "300px",
      data: new InputCalendarModel(type == 'start' ? this.startDate() : this.endDate(), type == 'start' ? null : this.startDate(), this.endDate() ? this.endDate() : new Date())
    });

    dialogRef.afterClosed().subscribe((date: Date | null) => {
      if (date) {
        if (type == 'start') {
          date.setHours(0, 0, 0, 0);
          this.startDate.set(date);
        } else {
          date.setHours(23, 59, 59, 999);
          this.endDate.set(date);
        }
      }
    });
  }

  applyFilter() {
    this.dialogRef.close({ startDate: this.startDate(), endDate: this.endDate() });
  }

  async closeDialog() {
    if (this.startDate() || this.endDate()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.applyFilter();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }
}

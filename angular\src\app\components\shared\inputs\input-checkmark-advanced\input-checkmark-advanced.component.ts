import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';

@Component({
  selector: 'app-input-checkmark-advanced',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './input-checkmark-advanced.component.html',
  styleUrl: './input-checkmark-advanced.component.scss'
})

export class InputCheckmarkAdvancedComponent {

  @Input() inputId: string = '';
  @Input() disabled: boolean = false;
  @Input() draft: boolean = false;
  @Input() canceled: boolean = false;
  @Input() readonly: boolean = false;
  @Input() checked: boolean = false;
  @Input() invalid: boolean = false;
  @Input() percentage: number = 0;

  @Output() change = new EventEmitter();

  constructor(private alertService: AlertService, public cc: CacheService) {

  }

  async onCheckmarkClick() {
    if (this.disabled) {
      this.alertService.alert(this.cc.texts()['overlay_journalActionMandatoryAlert_title'], this.cc.texts()['overlay_journalActionMandatoryAlert_content'], this.cc.texts()['screen_common_ok']);
    } else {
      this.change.emit();
    }
  }
}

import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SvgComponent } from '../../svg/svg.component';
import { InputTextComponent } from '../input-text/input-text.component';
import { FormControl, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { AlertService } from '@app/_services/alert.service';
import { notOnlyWhitespace } from '@app/_directives/form-validator.directive';
import { Subject } from 'rxjs';
import { getNumId } from '@app/_utils/utils';
import { CacheService } from '@app/_services/cache.service';

@Component({
  selector: 'app-input-options-add',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    InputTextComponent
  ],
  templateUrl: './input-options-add.component.html',
  styleUrl: './input-options-add.component.scss'
})

export class InputOptionsAddComponent {

  maxLength: string = '50';
  control: FormControl;
  mode: 'new' | 'edit' = 'new';
  dataChange$ = new Subject<{mode: 'new' | 'edit', option: { id: number, value: string } }>();

  constructor(public dialogRef: MatDialogRef<InputOptionsAddComponent>, @Inject(MAT_DIALOG_DATA) public data: { option: { id: number, value: string }, otherOptions: { id: number, value: string }[], maxLength: string, maxCount: number, mode: 'new' | 'edit' },
    private alertService: AlertService,
    public cc: CacheService
  ) {
    this.mode = data.mode;
    this.maxLength = data.maxLength;
    this.control = new FormControl(this.data.option ? this.data.option.value : '', [Validators.required, Validators.maxLength(Number(this.maxLength)), notOnlyWhitespace()]);

    dialogRef.backdropClick().subscribe(async () => {
      this.closeDialog();
    });
  }

  hasChanges(): boolean {
    return this.control.value !== (this.data.option ? this.data.option.value : '');
  }

  addOption(isClose: boolean = false) {
    const optionValue = this.control.value;
    const allOptionValues = this.data.otherOptions.map((option: { value: string }) => option.value.trim().toLowerCase());
    if (allOptionValues.includes(optionValue.trim().toLowerCase())) {
      this.alertService.alert(this.cc.texts()['overlay_habitSetupAddExistingOptionAlert_title'], this.cc.interpolateText('overlay_habitSetupAddExistingOptionAlert_content', { userOptions: optionValue }), this.cc.texts()['screen_common_ok']);
    } else {
      const option = { id: this.mode === 'new' ? getNumId() : this.data.option.id, value: optionValue.trim() };
      if (this.mode === 'edit') {
        const index = this.data.otherOptions.findIndex((option: { id: number }) => option.id === this.data.option.id);
        this.data.otherOptions.splice(index, 1);
      } else {
        this.data.otherOptions.push(option);
      }
      this.dataChange$.next({ mode: this.mode, option: option });
      const isMaxCountReached = this.data.otherOptions.length >= this.data.maxCount;
      if (isClose || isMaxCountReached || this.mode === 'edit') {
        this.dialogRef.close();
      } else {
        this.control.setValue('');
      }
    }
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.addOption(true);
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }

}

<vg-player class="items-center justify-center w-full">
    <vg-overlay-play></vg-overlay-play>
    <vg-buffering></vg-buffering>
    <audio class="hidden" id="attachmentAudioPlayer" #vgAudioPlayer [vgMedia]="$any(vgAudioPlayer)" preload="auto" controls>
        <source [src]="attachmentPath" [type]="'audio/' + (attachmentType === 'm4a' ? 'mp4' : attachmentType)">
    </audio>
    <div class="waveform-container" #waveformContainer></div>
    <vg-controls class="flex-col custom-controls" [vgAutohide]="true" [vgAutohideTime]="1.5">
        <div class="flex">
            <vg-time-display vgProperty="current" vgFormat="mm:ss"></vg-time-display>
            <div class="custom-progress-container">
                <vg-scrub-bar #scrubBar vgFor="attachmentAudioPlayer" [vgSlider]="true" (click)="seekVideo($event)">
                    <vg-scrub-bar-current-time vgFor="attachmentAudioPlayer" ></vg-scrub-bar-current-time>
                    <vg-scrub-bar-buffering-time vgFor="attachmentAudioPlayer" ></vg-scrub-bar-buffering-time>
                </vg-scrub-bar>
            </div>

            <vg-time-display vgProperty="total" vgFormat="mm:ss"></vg-time-display>
        </div>

        <div class="flex ps-1">
            <app-svg name="download" (click)="downloadEvent.emit()" role="button"></app-svg>
            <app-svg name="trash" class="ms-2" (click)="deleteEvent.emit()" role="button"></app-svg>
        </div>
    </vg-controls>
</vg-player>

<ng-template #noAudio>
    <div class="flex h-full w-full items-center justify-center">
        <p class="mb-0 color-8">{{ cc.texts()['screen_common_recordsNotFound'] }}</p>
    </div>
</ng-template>
import { CommonModule } from '@angular/common';
import { Component, computed, inject, Input, Signal } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Todo, TodoAction } from '@app/_interfaces/todo.interface';
import { ParseMinutesPipe } from '@app/_pipes/parse-minutes.pipe';
import { ParseTimePipe } from '@app/_pipes/parse-time.pipe';
import { CacheService } from '@app/_services/cache.service';
import { TodoStore, UserStore } from '@app/_stores';
import { getDateString } from '@app/_utils/utils';
import { InputCheckmarkAdvancedComponent } from '@app/components/shared/inputs/input-checkmark-advanced/input-checkmark-advanced.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { TodoFormComponent } from '../todo-form/todo-form.component';
import { EntitySetup } from '@app/_interfaces/feature.interface';
import { ParseRulePipe } from '@app/_pipes/parse-rule.pipe';
import { ParseTextPipe } from '@app/_pipes/parse-text.pipe';
import { DependencyService } from '@app/_services/dependency.service';

@Component({
  selector: 'app-todo-block',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    ParseTimePipe,
    ParseMinutesPipe,
    InputCheckmarkAdvancedComponent,
    ParseRulePipe,
    ParseTextPipe
  ],
  templateUrl: './todo-block.component.html',
  styleUrl: './todo-block.component.scss'
})

export class TodoBlockComponent {

  readonly todoStore = inject(TodoStore);
  readonly userStore = inject(UserStore);
  @Input() show: string[] = [];
  @Input() blockClass: string = '';
  @Input() descriptionType: string = 'none';
  @Input() dateString: string = getDateString();
  @Input() entity!: EntitySetup;
  @Input() isLabel: boolean = true;

  todo: Signal<Todo> = computed(() => {
    return this.todoStore.idToTodo()[this.entity.id];
  })
  actionMap: Signal<Record<string, TodoAction>> = computed(() => {
    const actionMap: Record<string, TodoAction> = {};
    this.todo().actions.forEach(action => {
      actionMap[action.dueAt] = action;
    });
    return actionMap;
  })

  constructor(public cc: CacheService, public dialog: MatDialog, public ds: DependencyService) {

  }

  ngOnInit() {

  }

  openTodo() {
    const confirmDialog = this.dialog.open(TodoFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      minHeight: '90vh',
      disableClose: true,
      data: {
        mode: 'edit',
        value: this.todo(),
        dateString: this.dateString
      },
    });
    return confirmDialog.afterClosed();
  }

  actionInitial(): TodoAction | null {
    return this.todo().actions.find((action: TodoAction) => action.dueAt === this.dateString) || null;
  }

  onActionChange() {
    const action: TodoAction | null = this.actionMap()[this.dateString] || null;
    const actionInitial: TodoAction | null = this.actionInitial();
    let updatedTodo: Todo | null = null;
    if ((action && action.isSkipped === false) || !action) {
      const newAction: TodoAction = {
        dueAt: this.dateString,
        isSkipped: action ? true : false,
        completedAt: actionInitial ? actionInitial.completedAt : new Date()
      };
      updatedTodo = { ...this.todo(), ...{ actions: [...this.todo().actions, newAction] } };
    } else if (action && action.isSkipped === true) {
      updatedTodo = { ...this.todo(), ...{ actions: this.todo().actions.filter((action: TodoAction) => action.dueAt !== this.dateString) } };
    }
    if (updatedTodo) {
      this.todoStore.updateTodos([updatedTodo]);
    }
  }
}

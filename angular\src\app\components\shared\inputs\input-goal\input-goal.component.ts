import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { InputTextComponent } from '../input-text/input-text.component';
import { FormControl, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { AlertService } from '@app/_services/alert.service';
import { SvgComponent } from '../../svg/svg.component';
import { notOnlyWhitespace } from '@app/_directives/form-validator.directive';
import { CacheService } from '@app/_services/cache.service';

@Component({
  selector: 'app-input-goal',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    InputTextComponent
  ],
  templateUrl: './input-goal.component.html',
  styleUrl: './input-goal.component.scss'
})

export class InputGoalComponent {

  control: FormControl = new FormControl('', [Validators.required, Validators.maxLength(80), notOnlyWhitespace()]);

  constructor(public dialogRef: MatDialogRef<InputGoalComponent>, @Inject(MAT_DIALOG_DATA) public data: { value: string }, private alertService: AlertService,
    private dialog: MatDialog,
    public cc: CacheService
  ) {
    this.control.setValue(data.value);

    dialogRef.backdropClick().subscribe(async () => {
      this.closeDialog();
    });
  }

  hasChanges(): boolean {
    return this.control.value !== this.data.value;
  }

  save() {
    this.dialogRef.close(this.control.value);
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }

}

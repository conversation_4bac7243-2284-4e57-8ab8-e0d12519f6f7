import { inject, Injectable } from '@angular/core';
import { NgxIndexedDBService } from 'ngx-indexed-db';
import { Performance, PerformanceTrace, trace } from '@angular/fire/performance';
import { FirebaseService } from './firebase.service';
import {
  BehaviorSubject,
  catchError,
  debounceTime,
  firstValueFrom,
  from,
  interval,
  lastValueFrom,
  mergeMap,
  Observable,
  startWith,
  Subject,
  switchMap,
  take,
  takeUntil,
  throwError
} from 'rxjs';
import * as _ from 'lodash';
import { Analytics, logEvent, setDefaultEventParameters, setUserId } from '@angular/fire/analytics';
import { dbConfig } from '@app/_configs/db.config';
import { environment } from '../../environments/environment';
import { Collection } from '@app/_types/collection.type';
import { List } from '@app/_interfaces/list.interface';
import { StorageService } from './storage.servive';
import { Note } from '@app/_interfaces/note.interface';
import { FbEntity, JsEntity } from '@app/_interfaces/entity.interface';
import { UserMetaData, UserResource, UserViewSettings } from '@app/_interfaces/user.interface';
import { Journal, JournalSetup } from '@app/_interfaces/journal.interface';
import { Todo } from '@app/_interfaces/todo.interface';
import { Habit, HabitSetup } from '@app/_interfaces/habit.interface';
import { MoneyTransaction, MoneyTrackerSetup } from '@app/_interfaces/money-tracker.interface';
import { CalendarEvent, CalendarEventSetup, CalendarIntegration } from '@app/_interfaces/calendar-integration.interface';

@Injectable({
  providedIn: 'root'
})

export class IndexDbService {

  stores: Collection[] = ['lists', 'notes'];
  todosSubject$ = new BehaviorSubject<Todo[] | null>(null);
  todos$: Observable<Todo[] | null> = this.todosSubject$.asObservable();
  
  listSubject$ = new BehaviorSubject<List[] | null>(null);
  lists$: Observable<List[] | null> = this.listSubject$.asObservable();

  noteSubject$ = new BehaviorSubject<Note[] | null>(null);
  notes$: Observable<Note[] | null> = this.noteSubject$.asObservable();

  journalSetupSubject$ = new BehaviorSubject<JournalSetup[] | null>(null);
  journalSetups$: Observable<JournalSetup[] | null> = this.journalSetupSubject$.asObservable();
  journalSubject$ = new BehaviorSubject<Journal[] | null>(null);
  journals$: Observable<Journal[] | null> = this.journalSubject$.asObservable();

  habitSetupSubject$ = new BehaviorSubject<HabitSetup[] | null>(null);
  habitSetups$: Observable<HabitSetup[] | null> = this.habitSetupSubject$.asObservable();
  habitSubject$ = new BehaviorSubject<Habit[] | null>(null);
  habits$: Observable<Habit[] | null> = this.habitSubject$.asObservable();

  moneyTrackerSetupSubject$ = new BehaviorSubject<MoneyTrackerSetup[] | null>(null);
  moneyTrackerSetups$: Observable<MoneyTrackerSetup[] | null> = this.moneyTrackerSetupSubject$.asObservable();
  moneyTransactionSubject$ = new BehaviorSubject<MoneyTransaction[] | null>(null);
  moneyTransaction$: Observable<MoneyTransaction[] | null> = this.moneyTransactionSubject$.asObservable();

  calendarIntegrationsSubject$ = new BehaviorSubject<CalendarIntegration[] | null>(null);
  calendarIntegrations$: Observable<CalendarIntegration[] | null> = this.calendarIntegrationsSubject$.asObservable();
  calendarEventSetupSubject$ = new BehaviorSubject<CalendarEventSetup[] | null>(null);
  calendarEventSetups$: Observable<CalendarEventSetup[] | null> = this.calendarEventSetupSubject$.asObservable();
  calendarEventSubject$ = new BehaviorSubject<CalendarEvent[] | null>(null);
  calendarEvents$: Observable<CalendarEvent[] | null> = this.calendarEventSubject$.asObservable();

  userResourceSubject$ = new BehaviorSubject<UserResource[] | null>(null);
  userResource$: Observable<UserResource[] | null> = this.userResourceSubject$.asObservable();
  userMetadataSubject$ = new BehaviorSubject<UserMetaData[] | null>(null);
  userMetadata$: Observable<UserMetaData[] | null> = this.userMetadataSubject$.asObservable();
  viewSettingsSubject$ = new BehaviorSubject<UserViewSettings[] | null>(null);
  viewSettings$: Observable<UserViewSettings[] | null> = this.viewSettingsSubject$.asObservable();

  private fetchTriggers: { [key: string]: Subject<void> } = {
    todos: new Subject<void>(),
    lists: new Subject<void>(),
    notes: new Subject<void>(),
    userResources: new Subject<void>(),
    usersMetadata: new Subject<void>(),
    viewSettings: new Subject<void>(),
    journalSetups: new Subject<void>(),
    journalActions: new Subject<void>(),
    habitSetups: new Subject<void>(),
    habitActions: new Subject<void>(),
    moneyTrackerSetups: new Subject<void>(),
    moneyTrackerTransactions: new Subject<void>(),
    calendarIntegrations: new Subject<void>(),
    calendarEventSetups: new Subject<void>(),
    calendarEventActions: new Subject<void>()
  };
  private latestUpdatedAts: { [key: string]: Date } = {
    todos: new Date(2000, 0, 1),
    lists: new Date(2000, 0, 1),
    notes: new Date(2000, 0, 1),
    userResources: new Date(2000, 0, 1),
    usersMetadata: new Date(2000, 0, 1),
    viewSettings: new Date(2000, 0, 1),
    journalSetups: new Date(2000, 0, 1),
    journalActions: new Date(2000, 0, 1),
    habitSetups: new Date(2000, 0, 1),
    habitActions: new Date(2000, 0, 1),
    moneyTrackerSetups: new Date(2000, 0, 1),
    moneyTrackerTransactions: new Date(2000, 0, 1),
    calendarIntegrations: new Date(2000, 0, 1),
    calendarEventSetups: new Date(2000, 0, 1),
    calendarEventActions: new Date(2000, 0, 1)
  };

  private collectionsWithCollab: Collection[] = [
    'todos',
    'habitSetups',
    'habitActions',
    'journalSetups',
    'journalActions',
    'moneyTrackerSetups',
    'moneyTrackerTransactions',
    'notes',
    'lists'
  ];

  private fetchCollabTriggers: { [key: string]: Subject<void> } = {
    todos: new Subject<void>(),
    lists: new Subject<void>(),
    notes: new Subject<void>(),
    journalSetups: new Subject<void>(),
    journalActions: new Subject<void>(),
    habitSetups: new Subject<void>(),
    habitActions: new Subject<void>(),
    moneyTrackerSetups: new Subject<void>(),
    moneyTrackerTransactions: new Subject<void>(),
  };

  private collabLatestUpdatedAts: { [key: string]: Date } = {
    todos: new Date(2000, 0, 1),
    lists: new Date(2000, 0, 1),
    notes: new Date(2000, 0, 1),
    journalSetups: new Date(2000, 0, 1),
    journalActions: new Date(2000, 0, 1),
    habitSetups: new Date(2000, 0, 1),
    habitActions: new Date(2000, 0, 1),
    moneyTrackerSetups: new Date(2000, 0, 1),
    moneyTrackerTransactions: new Date(2000, 0, 1),
  };

  private isInitialsed = false;
  private idbPerformanceTrace: PerformanceTrace;
  unSubscribe = new Subject<void>();
  private userCollections = ['userResources', 'viewSettings'];

  // RESYNC -> INITIALISE -> INITIALISE COLLECTION(reinit new fetch after every fetchtrigger.next()[on visibility changes and docs received]) -> FETCH LATEST DATA -> UPDATE IDB -> UPDATE OBSERVABLES FROM IDB
  constructor(
    private dbService: NgxIndexedDBService,
    private fbService: FirebaseService,
    private ss: StorageService,
    private performance: Performance = inject(Performance),
    private analytics: Analytics = inject(Analytics)
  ) {
    setUserId(this.analytics, this.fbService.getCurrentUserId());
    setDefaultEventParameters({
      user: this.fbService.getCurrentUserId(),
      version: environment.version
    });
    logEvent(this.analytics, 'idb_service', {
      user: this.fbService.getCurrentUserId(),
      version: environment.version
    });
    this.idbPerformanceTrace = trace(this.performance, 'idbServiceService');
    this.idbPerformanceTrace.putAttribute('user', this.fbService.getCurrentUserId() || 'UNKNOWN');
    this.idbPerformanceTrace.putAttribute('version', environment.version);
    this.setupDatabaseChangeListener();
  }

  // Only triggers when the app needs a clean start (e.g. when user gets newer version of the app))
  async resync() {
    console.log('RESYNCING');
    await this.resyncTodoStore();
    await this.resyncListsStore();
    await this.resyncNotesStore();
    await this.resyncJournalSetupStore();
    await this.resyncJournalStore();
    await this.resyncHabitSetupStore();
    await this.resyncHabitStore();
    await this.resyncMoneyTrackerSetupStore();
    await this.resyncMoneyTransactionStore();
    await this.resyncCalendarIntegrationStore();
    await this.resyncCalendarEventSetupStore();
    await this.resyncCalendarEventStore();
    await this.resyncUserResourceStore();
    await this.resyncUserMetadataStore();
    await this.resyncViewSettingsStore();
    await this.updateObservablesFromIdb();
  }

  // Always gets triggered on app start to get NEW ITEMS after resync from Firestore - basically a subscription for newer ones
  init() {
    const localUser = this.ss.getUser();
    const localUserId = localUser && localUser?.uid;
    if (this.isInitialsed || !localUserId) return;
    this.isInitialsed = true;
    console.log('INITIALISING');
    const collections: Collection[] = [
      'todos',
      'lists',
      'notes',
      'journalSetups',
      'journalActions',
      'habitSetups',
      'habitActions',
      'moneyTrackerSetups',
      'moneyTrackerTransactions',
      'calendarIntegrations',
      'calendarEventSetups',
      'calendarEventActions',
      'userResources',
      'usersMetadata',
      'viewSettings'
    ];

    const initCollections = collections.map(collection => {
      return this.getLatestCloudUpdatedAt(collection as Collection).then(timestamp => {
        console.log('latestUpdatedAts', collection, timestamp);
        this.latestUpdatedAts[collection] = timestamp;
        this.initialiseCollection(collection as Collection);
      });
    });

    const initCollabCollections = this.collectionsWithCollab.map(collection => {
      return this.getLatestCloudUpdatedAt(collection as Collection).then(timestamp => {
        console.log('collabLatestUpdatedAts', collection, timestamp);
        this.collabLatestUpdatedAts[collection] = timestamp;
        this.initialiseCollabCollection(collection as Collection);
      });
    });

    Promise.all(initCollections).then(async () => {
      console.log('INITIALISED');
      // To take care of offline situations
      let lastCheckTime = new Date();
      let initialised = false;
      interval(1000 * 60)
        .pipe(startWith(0), takeUntil(this.unSubscribe)) // This makes it start immediately
        .subscribe(() => {
          const currentTime = new Date();
          const timeDiff = currentTime.getTime() - lastCheckTime.getTime();
          // if diff greater than 2 mins then check for new items
          if (timeDiff > 1000 * 60 * 2 || !initialised) {
            console.log('CHECKING FOR NEW ITEMS');
            initialised = true;
            lastCheckTime = currentTime;
            collections.forEach(collection => {
              this.fetchTriggers[collection]?.next();
              this.fetchCollabTriggers[collection]?.next();
            });
          } else {
            lastCheckTime = currentTime;
          }
        });
      await this.updateObservablesFromIdb();
    });
  }

  initialiseCollection(collection: Collection) {
    this.fetchTriggers[collection]?.pipe(switchMap(() => this.fetchLatestData(collection)), takeUntil(this.unSubscribe)).subscribe();
  }

  initialiseCollabCollection(collection: Collection) {
    this.fetchCollabTriggers[collection]?.pipe(switchMap(() => this.fetchLatestCollabData(collection)), takeUntil(this.unSubscribe)).subscribe();
  }

  fetchLatestData(collection: Collection) {
    console.log('FETCHING LATEST DATA - ' + collection);
    let baseObservable: Observable<any>;
    baseObservable = collection === 'usersMetadata' ? this.fbService.getUserMetadata<FbEntity, UserResource>(collection, this.latestUpdatedAts[collection]) : this.fbService.getAllItems<FbEntity, JsEntity>(collection, this.latestUpdatedAts[collection]);

    return baseObservable.pipe(
      debounceTime(3000),
      mergeMap(async (items: JsEntity[]) => {
        console.log('FIRESTORE READ - ' + collection, items.length);
        this.idbPerformanceTrace.incrementMetric(`INIT - ${collection}`, items.length);
        logEvent(this.analytics, 'init_firestore_read_' + collection, {
          count: items.length
        });
        if (items.length > 0) {
          await this.updateBulk(collection, items);
        }
        const latestUpdatedItem = _.maxBy(items, 'cloudUpdatedAt');
        if (latestUpdatedItem && latestUpdatedItem.cloudUpdatedAt && latestUpdatedItem.cloudUpdatedAt > this.latestUpdatedAts[collection]) {
          this.latestUpdatedAts[collection] = latestUpdatedItem.cloudUpdatedAt as Date;
          this.fetchTriggers[collection].next();
        }
        return items;
      }),
      catchError(e => {
        console.error('ERROR in ' + collection + ' fetchLatestData');
        console.log('error', e);
        return throwError(() => new Error('ups something happened'));
      })
    );
  }

  fetchLatestCollabData(collection: Collection) {
    console.log('FETCHING LATEST COLLAB DATA - ' + collection);
    let baseObservable: Observable<any>;
    baseObservable = this.fbService.getAllCollaborationItems<FbEntity, JsEntity>(collection, this.collabLatestUpdatedAts[collection]);

    return baseObservable.pipe(
      debounceTime(3000),
      mergeMap(async (items: JsEntity[]) => {
        console.log('FIRESTORE READ COLLAB - ' + collection, items.length);
        this.idbPerformanceTrace.incrementMetric(`INIT - ${collection}`, items.length);
        logEvent(this.analytics, 'init_firestore_read_' + collection, {
          count: items.length
        });
        if (items.length > 0) {
          await this.updateBulk(collection, items);
        }
        const latestUpdatedItem = _.maxBy(items, 'cloudUpdatedAt');
        if (latestUpdatedItem && latestUpdatedItem.cloudUpdatedAt && latestUpdatedItem.cloudUpdatedAt > this.collabLatestUpdatedAts[collection]) {
          this.collabLatestUpdatedAts[collection] = latestUpdatedItem.cloudUpdatedAt as Date;
          this.fetchCollabTriggers[collection].next();
        }
        return items;
      }),
      catchError(e => {
        console.error('ERROR in ' + collection + ' fetchLatestCollabData');
        console.log('error', e);
        return throwError(() => new Error('ups something happened'));
      })
    );
  }

  async updateObservablesFromIdb<T extends JsEntity>(entity: Collection | null = null) {
    if (entity === null || entity === 'todos') {
      const todos = await this.getAll<Todo>('todos');
      console.log('Todos shown from IDB', todos.length);
      this.todosSubject$.next(todos || []);
    }
    if (entity === null || entity === 'lists') {
      const lists = await this.getAll<List>('lists');
      console.log('Lists shown from IDB', lists.length);
      this.listSubject$.next(lists || []);
    }
    if (entity === null || entity === 'notes') {
      const notes = await this.getAll<Note>('notes');
      console.log('Notes shown from IDB', notes.length);
      this.noteSubject$.next(notes || []);
    }
    if (entity === null || entity === 'journalSetups') {
      const journalSetups = await this.getAll<JournalSetup>('journalSetups');
      console.log('journalSetups shown from IDB', journalSetups.length);
      this.journalSetupSubject$.next(journalSetups || []);
    }
    if (entity === null || entity === 'journalActions') {
      const journalActions = await this.getAll<Journal>('journalActions');
      console.log('journalActions shown from IDB', journalActions.length);
      this.journalSubject$.next(journalActions || []);
    }
    if (entity === null || entity === 'habitSetups') {
      const habitSetups = await this.getAll<HabitSetup>('habitSetups');
      console.log('habitSetups shown from IDB', habitSetups.length);
      this.habitSetupSubject$.next(habitSetups || []);
    }
    if (entity === null || entity === 'habitActions') {
      const habitActions = await this.getAll<Habit>('habitActions');
      console.log('habitActions shown from IDB', habitActions.length);
      this.habitSubject$.next(habitActions || []);
    }
    if (entity === null || entity === 'moneyTrackerSetups') {
      const moneyTrackerSetups = await this.getAll<MoneyTrackerSetup>('moneyTrackerSetups');
      console.log('moneyTrackerSetups shown from IDB', moneyTrackerSetups.length);  
      this.moneyTrackerSetupSubject$.next(moneyTrackerSetups || []);
    }
    if (entity === null || entity === 'moneyTrackerTransactions') {
      const moneyTrackerTransactions = await this.getAll<MoneyTransaction>('moneyTrackerTransactions');
      console.log('moneyTrackerTransactions shown from IDB', moneyTrackerTransactions.length);
      this.moneyTransactionSubject$.next(moneyTrackerTransactions || []);
    }

    if (entity === null || entity === 'calendarIntegrations') {
      const calendarIntegrations = await this.getAll<CalendarIntegration>('calendarIntegrations');
      console.log('calendarIntegrations shown from IDB', calendarIntegrations.length);
      this.calendarIntegrationsSubject$.next(calendarIntegrations || []);
    }
    if (entity === null || entity === 'calendarEventSetups') {
      const calendarEventSetups = await this.getAll<CalendarEventSetup>('calendarEventSetups');
      console.log('calendarEventSetups shown from IDB', calendarEventSetups.length);
      this.calendarEventSetupSubject$.next(calendarEventSetups || []);
    }
    if (entity === null || entity === 'calendarEventActions') {
      const calendarEvents = await this.getAll<CalendarEvent>('calendarEventActions');
      console.log('calendarEvents shown from IDB', calendarEvents.length);
      this.calendarEventSubject$.next(calendarEvents || []);
    }

    if (entity === null || entity === 'userResources') {
      const userResources = await this.getAll<UserResource>('userResources');
      console.log('userResources shown from IDB', userResources.length);
      this.userResourceSubject$.next(userResources || []);
    }
    if (entity === null || entity === 'usersMetadata') {
      const usersMetadata = await this.getAll<UserMetaData>('usersMetadata');
      console.log('usersMetadata shown from IDB', usersMetadata.length);
      this.userMetadataSubject$.next(usersMetadata || []);
    }
    if (entity === null || entity === 'viewSettings') {
      const viewSettings = await this.getAll<UserViewSettings>('viewSettings');
      console.log('viewSettings shown from IDB', viewSettings.length);
      this.viewSettingsSubject$.next(viewSettings || []);
    }
    return true;
  }

  async resyncTodoStore() {
    await this.clearStoreObjects('todos');
    const items = await lastValueFrom(this.fbService.getAllItems<FbEntity, Todo>('todos').pipe(take(1)));
    console.log('FIRESTORE RESYNC todos - ', items);
    const collabItems = await lastValueFrom(this.fbService.getAllCollaborationItems<FbEntity, Todo>('todos').pipe(take(1)));
    console.log('FIRESTORE RESYNC todos collab - ', collabItems);
    items.push(...collabItems);
    await this.addBulk('todos', items || []);
    console.log('FIRESTORE RESYNC todos - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - Todos`, items.length);
    logEvent(this.analytics, 'resync_firestore_read_todos', {
      count: items.length
    });
  }


  async resyncListsStore() {
    await this.clearStoreObjects('lists');
    const items = await lastValueFrom(this.fbService.getAllItems<FbEntity, List>('lists').pipe(take(1)));
    const collabItems = await lastValueFrom(this.fbService.getAllCollaborationItems<FbEntity, List>('lists').pipe(take(1)));
    items.push(...collabItems);
    await this.addBulk('lists', items || []);
    console.log('FIRESTORE RESYNC lists - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - Lists`, items.length);
    logEvent(this.analytics, 'resync_firestore_read_lists', {
      count: items.length
    });
  }

  async resyncNotesStore() {
    await this.clearStoreObjects('notes');
    const items = await lastValueFrom(this.fbService.getAllItems<FbEntity, Note>('notes').pipe(take(1)));
    const collabItems = await lastValueFrom(this.fbService.getAllCollaborationItems<FbEntity, Note>('notes').pipe(take(1)));
    items.push(...collabItems);
    await this.addBulk('notes', items || []);
    console.log('FIRESTORE RESYNC notes - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - Notes`, items.length);
    logEvent(this.analytics, 'resync_firestore_read_notes', {
      count: items.length
    });
  }

  async resyncJournalSetupStore() {
    await this.clearStoreObjects('journalSetups');
    const items = await lastValueFrom(this.fbService.getAllItems<FbEntity, JournalSetup>('journalSetups').pipe(take(1)));
    const collabItems = await lastValueFrom(this.fbService.getAllCollaborationItems<FbEntity, JournalSetup>('journalSetups').pipe(take(1)));
    items.push(...collabItems);
    await this.addBulk('journalSetups', items || []);
    console.log('FIRESTORE RESYNC journalSetups - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - journalSetups`, items.length);
    logEvent(this.analytics, 'resync_firestore_read_journalSetups', {
      count: items.length
    });
  }

  async resyncJournalStore() {
    await this.clearStoreObjects('journalActions');
    const items = await lastValueFrom(this.fbService.getAllItems<FbEntity, Journal>('journalActions').pipe(take(1)));
    const collabItems = await lastValueFrom(this.fbService.getAllCollaborationItems<FbEntity, Journal>('journalActions').pipe(take(1)));
    items.push(...collabItems);
    await this.addBulk('journalActions', items || []);
    console.log('FIRESTORE RESYNC journalActions - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - journalActions`, items.length);
    logEvent(this.analytics, 'resync_firestore_read_journalActions', {
      count: items.length
    });
  }

  async resyncHabitSetupStore() {
    await this.clearStoreObjects('habitSetups');
    const items = await lastValueFrom(this.fbService.getAllItems<FbEntity, HabitSetup>('habitSetups').pipe(take(1)));
    const collabItems = await lastValueFrom(this.fbService.getAllCollaborationItems<FbEntity, HabitSetup>('habitSetups').pipe(take(1)));
    items.push(...collabItems);
    await this.addBulk('habitSetups', items || []);
    console.log('FIRESTORE RESYNC habitSetups - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - habitSetups`, items.length);
    logEvent(this.analytics, 'resync_firestore_read_habitSetups', {
      count: items.length
    });
  }

  async resyncHabitStore() {
    await this.clearStoreObjects('habitActions');
    const items = await lastValueFrom(this.fbService.getAllItems<FbEntity, Habit>('habitActions').pipe(take(1)));
    const collabItems = await lastValueFrom(this.fbService.getAllCollaborationItems<FbEntity, Habit>('habitActions').pipe(take(1)));
    items.push(...collabItems);
    await this.addBulk('habitActions', items || []);
    console.log('FIRESTORE RESYNC habitActions - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - habitActions`, items.length);
    logEvent(this.analytics, 'resync_firestore_read_habitActions', {
      count: items.length
    });
  }
  
  async resyncMoneyTrackerSetupStore() {
    await this.clearStoreObjects('moneyTrackerSetups');
    const items = await lastValueFrom(this.fbService.getAllItems<FbEntity, MoneyTrackerSetup>('moneyTrackerSetups').pipe(take(1)));
    const collabItems = await lastValueFrom(this.fbService.getAllCollaborationItems<FbEntity, MoneyTrackerSetup>('moneyTrackerSetups').pipe(take(1)));
    items.push(...collabItems);
    await this.addBulk('moneyTrackerSetups', items || []);
    console.log('FIRESTORE RESYNC moneyTrackerSetups - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - moneyTrackerSetups`, items.length);
    logEvent(this.analytics, 'resync_firestore_read_moneyTrackerSetups', {
      count: items.length
    });
  }

  async resyncMoneyTransactionStore() {
    await this.clearStoreObjects('moneyTrackerTransactions');
    const items = await lastValueFrom(this.fbService.getAllItems<FbEntity, MoneyTransaction>('moneyTrackerTransactions').pipe(take(1)));
    const collabItems = await lastValueFrom(this.fbService.getAllCollaborationItems<FbEntity, MoneyTransaction>('moneyTrackerTransactions').pipe(take(1)));
    items.push(...collabItems);
    await this.addBulk('moneyTrackerTransactions', items || []);
    console.log('FIRESTORE RESYNC moneyTrackerTransactions - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - moneyTrackerTransactions`, items.length);
    logEvent(this.analytics, 'resync_firestore_read_moneyTrackerTransactions', {
      count: items.length
    });
  }

  async resyncCalendarIntegrationStore() {
    await this.clearStoreObjects('calendarIntegrations');
    const items = await lastValueFrom(this.fbService.getAllItems<FbEntity, CalendarIntegration>('calendarIntegrations').pipe(take(1)));
    await this.addBulk('calendarIntegrations', items || []);
    console.log('FIRESTORE RESYNC calendarIntegrations - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - calendarIntegrations`, items.length);
  }

  async resyncCalendarEventSetupStore() {
    await this.clearStoreObjects('calendarEventSetups');
    const items = await lastValueFrom(this.fbService.getAllItems<FbEntity, CalendarEventSetup>('calendarEventSetups').pipe(take(1)));
    await this.addBulk('calendarEventSetups', items || []);
    console.log('FIRESTORE RESYNC calendarEventSetups - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - calendarEventSetups`, items.length);
  }

  async resyncCalendarEventStore() {
    await this.clearStoreObjects('calendarEventActions');
    const items = await lastValueFrom(this.fbService.getAllItems<FbEntity, CalendarEvent>('calendarEventActions').pipe(take(1)));
    await this.addBulk('calendarEventActions', items || []);
    console.log('FIRESTORE RESYNC calendarEvents - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - calendarEvents`, items.length);
  }

  async resyncUserResourceStore() {
    await this.clearStoreObjects('userResources');
    const items = await lastValueFrom(this.fbService.getUserItems<FbEntity, UserResource>('userResources').pipe(take(1)));
    await this.addBulk('userResources', items || []);
    console.log('FIRESTORE RESYNC userResources - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - userResources`, items.length);
  }

  async resyncUserMetadataStore() {
    await this.clearStoreObjects('usersMetadata');
    const items = await lastValueFrom(this.fbService.getUserMetadata<FbEntity, UserResource>('usersMetadata').pipe(take(1)));
    await this.addBulk('usersMetadata', items || []);
    console.log('FIRESTORE RESYNC usersMetadata - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - usersMetadata`, items.length);
  }

  async resyncViewSettingsStore() {
    await this.clearStoreObjects('viewSettings');
    const items = await lastValueFrom(this.fbService.getUserItems<FbEntity, UserResource>('viewSettings').pipe(take(1)));
    await this.addBulk('viewSettings', items || []);
    console.log('FIRESTORE RESYNC viewSettings - ', items?.length);
    this.idbPerformanceTrace.incrementMetric(`RESYNC - viewSettings`, items.length);
  }

  async getLatestCloudUpdatedAt(store: Collection): Promise<Date> {
    let latestDate = new Date(2000, 0, 1);
    await firstValueFrom(this.dbService.getAll(store)).then(items => {
      items.forEach(element => {
        // console.log(
        //   // @ts-ignore
        //   element.cloudUpdatedAt,
        //   latestDate,
        //   // @ts-ignore
        //   element.cloudUpdatedAt > latestDate
        // );
        // @ts-ignore
        if (element.cloudUpdatedAt > latestDate) {
          // @ts-ignore
          latestDate = element.cloudUpdatedAt;
        }
      });
    });
    // Reduce 1 minute to avoid missing items
    // return new Date(latestDate.getTime() - 1 * 60000);
    return latestDate;
  }

  async clearAll() {
    this.stores.forEach(async store => {
      await this.clearStoreObjects(store);
    });
    return true;
  }

  // async createDatabase() {
  //   return await this.dbService.createDatabase(1, (db) => {
  //     this.stores.forEach((store) => {
  //       db.createObjectStore(store, { keyPath: 'id', autoIncrement: false });
  //     });
  //   });
  // }

  initializeDatabase() {
    dbConfig.objectStoresMeta.forEach((storeConfig) => {
      this.dbService.createObjectStore(storeConfig);
    });
  }

  getEntityById(storeName: Collection, id: string | number): Observable<JsEntity> {
    return from(this.dbService.getByID(storeName, id) as Observable<JsEntity>);
  }

  async deleteDatabase() {
    return await lastValueFrom(this.dbService.deleteDatabase());
  }

  async clearStoreObjects(storeName: Collection) {
    return await lastValueFrom(this.dbService.clear(storeName));
  }

  async deleteStore(storeName: Collection) {
    return await lastValueFrom(this.dbService.deleteObjectStore(storeName));
  }

  async createObjectStore(storeName: Collection) {
    return await this.dbService.createObjectStore({
      store: storeName,
      storeConfig: { keyPath: 'id', autoIncrement: false },
      storeSchema: [{ name: 'id', keypath: 'id', options: { unique: true } }]
    });
  }

  async getAll<T extends JsEntity>(storeName: Collection | 'computed'): Promise<T[]> {
    return lastValueFrom<T[]>(this.dbService.getAll<T>(storeName));
  }

  async getByKey(storeName: Collection, key: any) {
    return await lastValueFrom(this.dbService.getByKey(storeName, key));
  }

  async getByIndex(storeName: Collection, index: string, key: any) {
    return await lastValueFrom(this.dbService.getByIndex(storeName, index, key));
  }

  async addBulk(storeName: Collection | 'computed', values: JsEntity[]) {
    return await lastValueFrom(this.dbService.bulkAdd(storeName, values));
  }

  async add(storeName: Collection, value: JsEntity): Promise<JsEntity> {
    const addedEntity = await lastValueFrom(this.dbService.add(storeName, value));
    await this.updateObservablesFromIdb(storeName);
    return addedEntity;
  }

  async update(storeName: Collection, id: string, value: JsEntity): Promise<JsEntity> {
    console.log('updatedEntity value value value', value);
    const updatedEntity = await firstValueFrom(this.dbService.update(storeName, { ...value, id }));
    await this.updateObservablesFromIdb(storeName);
    return updatedEntity;
  }

  async updateBulk(storeName: Collection, values: JsEntity[]): Promise<(string)> {
    const updatedKeys = await lastValueFrom(this.dbService.bulkPut(storeName, values));
    await this.updateObservablesFromIdb(storeName);
    return updatedKeys.toString();
  }

  async getBulk(storeName: Collection, keys: any[]) {
    return await lastValueFrom(this.dbService.bulkGet(storeName, keys));
  }

  async delete(storeName: Collection, key: any) {
    return await lastValueFrom(this.dbService.delete(storeName, key));
  }

  async bulkDelete(storeName: Collection, keys: any[]) {
    return await lastValueFrom(this.dbService.bulkDelete(storeName, keys));
  }

  async count(storeName: Collection) {
    return await lastValueFrom(this.dbService.count(storeName));
  }

  async openCursor(storeName: Collection) {
    return await lastValueFrom(this.dbService.openCursor(storeName));
  }

  deleteIndexDb() {
    this.dbService.deleteDatabase().pipe(takeUntil(this.unSubscribe))?.subscribe({
      next: (result) => {
        console.log('Database deleted successfully', result);
      },
      error: (error) => {
        console.error('Error deleting database:', error);
      }
    });
  }

  stopSubscription() {
    this.unSubscribe?.next();
    this.unSubscribe?.complete();
  }

  setupDatabaseChangeListener() {
    const dbRequest = indexedDB.open(dbConfig.name, dbConfig.version);

    dbRequest.onsuccess = (event: any) => {
      const db = event.target.result;

      db.onversionchange = () => {
        console.log('Database version changed.');
        // this.handleDatabaseChange();
      };
    };

    dbRequest.onerror = (event) => {
      console.error('Error opening database:', event);
    };
  }

  handleDatabaseChange() {
    // Pull data based on specific conditions
    this.dbService.getAll('lists').subscribe((items: any[]) => {
      // const filteredItems = items.filter(item => item.value > 10); // Example condition
      console.log('Filtered Items:', items);
    });
  }
}

import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { getNumId } from '@app/_utils/utils';
import { SvgComponent } from '../../svg/svg.component';
import { CacheService } from '@app/_services/cache.service';
import { AlertService } from '@app/_services/alert.service';
import { MatDialog } from '@angular/material/dialog';
import { InputOptionsAddComponent } from '../input-options-add/input-options-add.component';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-input-options',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SvgComponent
  ],
  templateUrl: './input-options.component.html',
  styleUrl: './input-options.component.scss'
})

export class InputOptionsComponent {

  @Input() name: string = '';
  @Input() control: FormArray = this.fb.array([]);
  @Input() minLength: string | null = null;
  @Input() maxLength: string = '50';
  @Input() maxCount: number = 10;
  @Input() disabled: boolean = false;
  @Input() readonly: boolean = false;
  @Input() submitted: boolean = false;
  unSubscribe = new Subject<void>();

  constructor(private fb: FormBuilder, public cc: CacheService, private alertService: AlertService, private dialog: MatDialog) {

  }

  getOptionFormGroup(index: number): FormGroup {
    return this.control.at(index) as FormGroup;
  }

  openAddOptionDialog(option: { id: number, value: string } | null = null) {
    const dialog = this.dialog.open(InputOptionsAddComponent, {
      width: '100%',
      maxWidth: '500px',
      disableClose: true,
      data: {
        option: option,
        mode: option ? 'edit' : 'new',
        otherOptions: this.control.value,
        maxLength: this.maxLength,
        maxCount: this.maxCount
      },
    });

    dialog.componentInstance.dataChange$.pipe(takeUntil(this.unSubscribe)).subscribe(value => {
      if (value) {
        if (value.mode === 'new') {
          this.control.push(this.addOptionForm(value.option));
        } else if (value.mode === 'edit') {
          const index = this.control.value.findIndex((option: { id: number }) => option.id === value.option.id);
          this.control.at(index).patchValue(value.option);
        }
      }
    });
  }

  isAddDisabled() {
    return this.control.length >= this.maxCount || (this.control.invalid && this.control.length > 1);
  }

  addOptionForm(option?: { id: number; value: string }) {
    return this.fb.group({
      id: new FormControl(option ? option.id : getNumId(), Validators.required),
      value: new FormControl(option ? option.value : '', [Validators.required, Validators.maxLength(Number(this.maxLength)), Validators.minLength(1)]),
    });
  }

  removeOption(index: number) {
    this.control.removeAt(index);
  }
}

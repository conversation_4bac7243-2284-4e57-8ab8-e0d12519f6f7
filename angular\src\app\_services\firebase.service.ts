import { Injectable } from '@angular/core';
import { BehaviorSubject, from, map, Observable, of, Subject } from 'rxjs';
import { serverTimestamp, Timestamp } from '@angular/fire/firestore';
import { FbUser } from '../_interfaces/user.interface';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AngularFireStorage } from '@angular/fire/compat/storage';
import { Config } from '../_interfaces/config.interface';
import { convertTimeStampsFb_Js, convertTimeStampsJs_Fb, getNewId } from '../_utils/utils';
import { Collection } from '../_types/collection.type';
import { CryptographyService } from "./cryptography.service";
import { HttpClient } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { CacheService } from './cache.service';
import { dbVersion } from '@app/_configs/db.config';
import { FbEntity, JsEntity } from '@app/_interfaces/entity.interface';

interface keyValueType {
  [key: string]: string | number | boolean | Date | null;
}

@Injectable({
  providedIn: 'root'
})

export class FirebaseService {

  currentUserSubject$ = new BehaviorSubject<any>({});
  public currentUser$: Observable<any> = this.currentUserSubject$.asObservable();
  configSubject$ = new BehaviorSubject<Config[]>([]);
  public config$: Observable<Config[]> = this.configSubject$.asObservable();

  public users: any[] = [];
  public orderedUserIds: string[] = [];
  public currentUser!: any;
  public uidUserMap: any = {};
  public tagNameToUidMap: { [key: string]: string } = {};
  public uidToName: { [key: string]: string } = {};
  unSubscribe = new Subject<void>();
  fbApiUrl: string = `${environment.fbApiBase}/v${dbVersion}`;

  constructor(
    private firestore: AngularFirestore,
    private storage: AngularFireStorage,
    private cryptoService: CryptographyService,
    private cc: CacheService,
    private http: HttpClient
  ) {

  }

  getMevolveIdFromUid(uid: string) {
    return this.http.get(`${this.fbApiUrl}-collaboration-getMevolveIdFromUid`, { params: { uid: uid } });
    // const functionName = `${dbVersion}-collaboration-getMevolveIdFromUid`;
    // const functions = getFunctions(undefined, 'europe-west1'); // Specify the region
    // const callable = httpsCallable(functions, functionName);

    // callable({ 'uid': uid })
    //   .then((response) => console.log('Response from Firebase:', response))
    //   .catch((error) => console.error('Error calling Firebase function:', error));
  }

  // const functionName = `${dbVersion}-collaboration-getMevolveIdFromUid`;
  // const callable = this.functions.httpsCallable('myFunction', {
  //   region: 'europe-west1', // Explicitly set the region
  // });
  // callable({ 'uid': uid }).subscribe({
  //   next: (response) => console.log('Response from Firebase:', response),
  //   error: (error) => console.error('Error calling Firebase function:', error),
  // });

  // getList() {
  //   this.encryptionService.initializeMeEncryption();

  //   const uid = this.getCurrentUserId();

  //   return this.firestore.collection<ListData>('lists',
  //     ref =>
  //       ref.where('uid', '==', uid),
  //   ).valueChanges();
  // }

  getFbUserData(): Observable<FbUser[]> {
    return this.firestore.collection<FbUser>('users').valueChanges();
  }

  getConfig<T extends FbEntity, K extends JsEntity>(): Observable<Config[]> {
    return this.firestore
      .collection<T>('config')
      .valueChanges()
      .pipe(
        map((items: T[]) => {
          const config = items.map(item => convertTimeStampsFb_Js(item) as K);
          this.configSubject$.next(config as unknown as Config[] || []);
          return config;
        })
      ) as unknown as Observable<Config[]>;
  }

  getUserNameFromId(id: string | undefined): string | undefined {
    if (!id) return;
    return this.users.find(user => user.value === id)?.label;
  }

  getDisplayName(id: string | null): string {
    if (!id) return '';
    const name = this.users.find(user => user.value === id)?.label;
    if (name && name.split(' ').length > 1) {
      return name.split(' ')[0];
    } else {
      return 'INVALID';
    }
  }

  getUserFirstAndLastLetter(id: string) {
    const nameParts = this.getUserNameFromId(id)?.trim().split(' ') || '';

    if (nameParts.length === 0) {
      return '';
    }
    const firstNameFirstLetter = nameParts[0].charAt(0);

    if (nameParts.length === 1) {
      return firstNameFirstLetter;
    }

    const lastNameFirstLetter = nameParts[nameParts.length - 1].charAt(0);
    return firstNameFirstLetter + lastNameFirstLetter;
  }

  getCurrentUserId() {
    return this.getLocalUser()?.uid;
  }

  getCurrentUser() {
    const uid = this.getCurrentUserId();
    if (uid) {
      return this.users.find(user => user.value === uid);
    }
  }

  async uploadFilesAsRecord(
    files: Record<number, { attachments: { path: string; file: Uint8Array }[]; dek?: string }>
  ): Promise<Record<number, string[]>> {
    const result: Record<number, string[]> = {};

    for (const keyStr of Object.keys(files)) {
      const key = Number(keyStr);
      const { attachments, dek } = files[key];
      result[key] = [];

      for (const attachment of attachments) {
        // Encrypt if a data encryption key is provided
        const fileData = dek
          ? this.cryptoService.encryptFile(attachment.file, dek)
          : attachment.file;

        // Create a reference and upload
        const fileRef = this.storage.ref(attachment.path);
        await this.storage.upload(attachment.path, fileData);

        // Get the download URL and store it
        const downloadURL = await fileRef.getDownloadURL().toPromise();
        result[key].push(downloadURL);
      }
    }

    return result;
  }

  async getFiles(filePaths: Record<number, string>, dek?: string): Promise<Record<number, string>> {
    const entries = Object.entries(filePaths)
    try {
      const results = await Promise.all(entries.map(([id, path]) => this.getFile(path, dek).then(file => ({ id: Number(id), file, path }))
      ));
      const resultRecord: Record<number, string> = {};
      results.forEach(({ id: id_1, file: file_1 }) => {
        resultRecord[id_1] = file_1;
      });
      return resultRecord;
    } catch (error) {
      console.error('Error downloading files:', error);
      throw error;
    }
  }

  async getFile(filePath: string, dek?: string): Promise<string> {
    return new Promise((resolve, reject) => {
      this.storage.ref(filePath).getDownloadURL().subscribe({
        next: (url: string) => {
          console.log('Download URL:', url);
          fetch(url)
            .then(response => {
              if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
              }
              return response.arrayBuffer();
            })
            .then(arrayBuffer => {
              console.log('Blob:', arrayBuffer);
              const byteArray = new Uint8Array(arrayBuffer);
              const decryptedByteArray = this.cryptoService.decryptFile(byteArray, dek || '');
              const mimeType = this.getMimeType(filePath); // Based on file extension
              if (decryptedByteArray) {
                const blob = new Blob([decryptedByteArray], { type: mimeType });
                const blobUrl = URL.createObjectURL(blob);
                console.log('blobUrl :', blobUrl, mimeType);
                resolve(blobUrl);
              } else {
                resolve('')
              }
            })
            .catch(error => {
              console.error('Error fetching blob:', error);
              reject(error);
            });
        },
        error: (error) => {
          console.error('Error getting download URL:', error);
          reject(error);
        }
      });
    });
  }

  async deleteFilesAsRecord(fileRecord: Record<number, string[]>): Promise<Record<number, string[]>> {
    const deleted: Record<number, string[]> = {};
    const deleteTasks: Promise<void>[] = [];
  
    for (const [idStr, filePaths] of Object.entries(fileRecord)) {
      const id = Number(idStr);
      deleted[id] = [];
  
      for (const path of filePaths) {
        const fileRef = this.storage.ref(path);
        const deleteTask = fileRef.delete().toPromise().then(() => {
          deleted[id].push(path);
        }).catch(error => {
          console.error(`Failed to delete ${path}:`, error);
        });
  
        deleteTasks.push(deleteTask);
      }
    }
  
    await Promise.all(deleteTasks);
    return deleted;
  }

  getMimeType(path: string): string {
    const ext = path.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'pdf': return 'application/pdf';
      case 'png': return 'image/png';
      case 'jpg':
      case 'jpeg': return 'image/jpeg';
      default: return 'application/octet-stream';
    }
  }

  downloadFileAsBlob(filePath: string): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const fileRef = this.storage.ref(filePath);
      fileRef.getDownloadURL().subscribe(
        url => {
          console.log('Download URL:', url);
          // Try fetching the file without the token
          fetch(url, { mode: 'cors' })
            .then(response => response.blob())
            .then(blob => {
              resolve(blob);
              console.log('Blob:', blob);
            })
            .catch(error => {
              reject(error)
              console.error('Error fetching file:', error);
            });
        },
        error => {
          reject(error)
          console.error('Error getting download URL:', error);
        }
      );
    });
  }

  getAllItems<T extends FbEntity, K extends JsEntity>(collection: Collection, from?: Date) {
    return this.firestore.collection<T>(collection, ref => {
      let condition = ref
        .where('cloudUpdatedAt', '>', Timestamp.fromDate(from || new Date(0)))
        .where('uid', '==', this.cc.user.uid)
        .where('docVer', '==', dbVersion);
      return condition;
    }).valueChanges().pipe(
      map((items: T[]) => {
        return items.map(item => this.cryptoService.decrypt(convertTimeStampsFb_Js(item), false) as K);
      })
    );
  }

  getAllCollaborationItems<T extends FbEntity, K extends JsEntity>(collection: Collection, from?: Date) {
    return this.firestore.collection<T>(collection, ref => {
      let condition = ref
        .where('cloudUpdatedAt', '>', Timestamp.fromDate(from || new Date(0)))
        .where('members.memberHashedEmails', 'array-contains', this.cc.user.hashedEmail);
      return condition;
    }).valueChanges().pipe(
      map((items: T[]) => {
        return items.map(item => this.cryptoService.decrypt(convertTimeStampsFb_Js(item), false, true) as K);
      })
    );
  }

  getUserMetadata<T extends FbEntity, K extends JsEntity>(collection: Collection, from?: Date) {
    return this.firestore.collection<T>(collection, ref => {
      let condition = ref
        .where('localUpdatedAt', '>', Timestamp.fromDate(from || new Date(0)))
        .where('uid', '==', this.cc.user.uid);
      return condition;
    }).valueChanges().pipe(
      map((items: T[]) => {
        return items.map(item => this.cryptoService.decrypt(convertTimeStampsFb_Js(item), false) as K);
      })
    );
  }

  getUserItems<T extends FbEntity, K extends JsEntity>(collection: Collection, from?: Date) {
    return this.firestore.collection<T>(collection, ref => {
      let condition = ref
        .where('cloudUpdatedAt', '>', Timestamp.fromDate(from || new Date(0)))
        .where('uid', '==', this.cc.user.uid)
        .where('docVer', '==', dbVersion);
      return condition;
    }).valueChanges().pipe(
      map((items: T[]) => {
        return items.map(item => this.cryptoService.decrypt(convertTimeStampsFb_Js(item), false) as K);
      })
    );
  }

  getItems<T extends FbEntity, K extends JsEntity>(criteria: keyValueType, collection: Collection, limit?: number) {
    return this.firestore
      .collection<T>(collection, ref => {
        let condition = ref.where('deletedAt', '==', null).orderBy('createdAt', 'desc');
        for (const [key, value] of Object.entries(criteria)) {
          condition = condition.where(key, '==', value);
        }
        if (limit) condition = condition.limit(limit);
        return condition;
      })
      .valueChanges()
      .pipe(
        map((items: T[]) => {
          return items.map(item => convertTimeStampsFb_Js(item) as K);
        })
      );
  }

  async createItems<T extends JsEntity>(items: T[], collection: Collection) {
    const uid = this.getLocalUser()?.uid;
    if (!items.length || !uid) return;
    // Flesh details
    items.forEach(item => {
      item.id = item.id || getNewId();
      item.uid = collection === 'users' ? item.uid : uid;
      // item.createdBy = uid;
      item.createdAt = item.createdAt || new Date();
      item.cloudUpdatedAt = serverTimestamp();
    });

    // Add in Firestore - Because of the max limit of 500 writes per batch
    if (items.length > 10 && items.length < 450) {
      const batch = this.firestore.firestore.batch();
      items.forEach(async item => {
        const docRef = this.firestore.firestore.collection(collection).doc(item.id);
        batch.set(docRef, convertTimeStampsJs_Fb(item)!, { merge: true });
      });
      await batch.commit();
    } else {
      items.forEach(async item => {
        await this.firestore.collection(collection).doc(item.id).set(convertTimeStampsJs_Fb(item));
      });
    }
  }

  async createItem<T extends JsEntity>(items: T, collection: Collection) {
    console.log(items);

    return this.firestore.firestore.collection(collection).doc(items.id).set(items).catch((e) => {
      console.log(e);
    });
  }

  async updateItems<T extends JsEntity>(items: T[], collection: Collection) {
    const uid = this.getLocalUser()?.uid;
    if (!items.length || !uid) return;
    // Flesh details
    items.forEach(item => {
      item.uid = collection === 'users' ? item.uid : uid;
      ;
      // item.updatedAt = new Date();
      item.cloudUpdatedAt = serverTimestamp();
    });
    // Add in Firestore - Because of the max limit of 500 writes per batch
    if (items.length > 10 && items.length < 450) {
      const batch = this.firestore.firestore.batch();
      items.forEach(item => {
        const docRef = this.firestore.firestore.collection(collection).doc(item.id);
        batch.update(docRef, convertTimeStampsJs_Fb(item)!);
      });
      await batch.commit();
    } else {
      items.forEach(async item => {
        await this.firestore.collection(collection).doc(item.id).update(convertTimeStampsJs_Fb(item)!);
      });
    }
  }

  async uploadFiles(files: File[], pathExceptFileName: string) {
    if (!files.length) return;
    const uid = this.getLocalUser()?.uid;
    if (!uid) return;
    const uploadTasks = files.map(file => {
      const filePath = `${pathExceptFileName}${file.name}`;
      const fileRef = this.storage.ref(filePath);
      return this.storage.upload(filePath, file);
    });
    return await Promise.all(uploadTasks);
  }

  async deleteFiles(fileNames: string[], pathExceptFileName: string) {
    if (!fileNames.length) return;
    const uid = this.getLocalUser()?.uid;
    if (!uid) return;
    const deleteTasks = fileNames.map(fileName => {
      const filePath = `${pathExceptFileName}${fileName}`;
      const fileRef = this.storage.ref(filePath);
      return fileRef.delete();
    });
    return await Promise.all(deleteTasks);
  }

  getLocalUser() {
    return JSON.parse(localStorage.getItem('user')!);
  }

  getUsername(uid: string) {
    return this.users.find(user => user.value === uid)?.label;
  }

  checkRoleForCurrentUser(role: string) {
    return this.getCurrentUser()?.roles.includes(role);
  }

  isAdmin(): boolean {
    return this.getCurrentUser()?.roles.includes('ADMIN');
  }

  stopSubscription() {
    this.unSubscribe?.next();
    this.unSubscribe?.complete();
  }
}

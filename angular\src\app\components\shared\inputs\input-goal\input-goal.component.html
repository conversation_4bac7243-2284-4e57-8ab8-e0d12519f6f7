<div class="top-section">
  <h6 class="heading mb-0">{{ cc.texts()['bottomSheet_yourGoal_titleTopBar'] }}</h6>
  <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
</div>
<div class="goal-form ri-p-4">
    <app-input-text [control]="control" name="meUserGoal" [placeholder]="cc.texts()['bottomSheet_yourGoal_placeholder']" [hideIcon]="true" maxLength="80"></app-input-text>
</div>
<div class="bottom-section flex items-center justify-between ri-px-4 ri-py-3 text-end ri-bt-2">
  <div class="flex items-center">
    
  </div>
  <button type="submit" class="btn btn-text text-16-500 color-35 check-disabled" (click)="save()" [disabled]="!hasChanges() || control.invalid">
    {{ cc.texts()['screen_common_save'] }}
  </button>
</div>
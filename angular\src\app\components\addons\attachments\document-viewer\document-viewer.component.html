<vg-player class="items-center justify-center w-full h-full" *ngIf="attachmentPath; else noDocument">
    <div class="h-full w-full flex items-center justify-center">
        <div class="text-center">
            <img class="img img-fluid w-auto" src="assets/icons/file.svg">
            <p class="text-18-500 ri-pt-4 color-1 mb-0 open-text" (click)="downloadEvent.emit()" role="button">{{ cc.texts()['screen_documentPreview_openFile'] }}</p>
        </div>
    </div>
    <vg-controls class="flex-col custom-controls">
        <div class="flex ps-1 my-auto">
            <app-svg name="download" (click)="downloadEvent.emit()" role="button"></app-svg>
            <app-svg name="trash" class="ms-2" (click)="deleteEvent.emit()" role="button"></app-svg>
        </div>
    </vg-controls>
</vg-player>

<ng-template #noDocument>
    <div class="flex h-full w-full items-center justify-center">
        <p class="mb-0 color-8">{{ cc.texts()['screen_common_recordsNotFound'] }}</p>
    </div>
</ng-template>
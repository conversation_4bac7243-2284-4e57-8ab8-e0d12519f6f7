<div class="top-section">
    <h6 class="heading mb-0">{{ cc.texts()['screen_common_habit'] }} <span class="text-12-400 ri-ps-2">{{ cc.getFormattedDate(data.dateString, 'd MMM y, EEE') }}</span></h6>
    <div class="flex">
        <app-svg name="more" class="more-icon ri-me-6" [matMenuTriggerFor]="infoMenu" role="button" [color]="cc.theme.color12"></app-svg>
        <mat-menu #infoMenu="matMenu" class="me-menu info-menu ri-w-300px">
            <button mat-menu-item class="text-14-400 color-8" (click)="editSetup()">
                <span class="flex items-center"><app-svg name="edit" class="ri-pe-3" [color]="cc.theme.color35"
                        role="button"></app-svg>{{ cc.texts()['dropdown_journalActionKebabMenu_editSetup'] }}</span>
            </button>
        </mat-menu>

        <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
    </div>
</div>
<div class="habit-form ri-p-4" id="habitContentBlock">
    <form [formGroup]="habitForm" #haForm="ngForm">
        <p class="color-8 text-18-500 ri-py-6 mb-0 w-full text-center">{{ setup.title }}</p>
        <div class="boolean-block ri-pb-4" *ngIf="habitType === 'boolean'">
            <button class="btn-boolean text-16-400" (click)="getFc('booleanAnswer').setValue(true)"
                [ngClass]="{'active' : getFc('booleanAnswer').value === true }">
                <span>{{ cc.texts()['screen_common_yes'] }}</span>
                <span class="check-circle"></span>
            </button>
            <button class="btn-boolean text-16-400" (click)="getFc('booleanAnswer').setValue(false)"
                [ngClass]="{'active' : getFc('booleanAnswer').value === false }">
                <span>{{ cc.texts()['bottomSheet_habitActionHabitTypeYesOrNo_no'] }}</span>
                <span class="check-circle"></span>
            </button>
        </div>
        <div class="options-block ri-pb-4" *ngIf="habitType === 'single' || habitType === 'multiple'">
            <button class="btn-option text-16-400" *ngFor="let option of setup.habitOptions"
                [ngClass]="{'active' : getFc('singleAnswer').value === option.id || (habitType === 'multiple' && getFc('multipleAnswer').value && getFc('multipleAnswer').value.includes(option.id)) }"
                (click)="selectOption(option.id)">
                <span>{{ option.value }}</span>
                <span class=""
                    [ngClass]="{'check-circle' : habitType === 'single', 'checkmark' : habitType === 'multiple'}"></span>
            </button>
        </div>
        <div class="progress-block ri-pb-4" *ngIf="habitType === 'timer'">
            <div class="flex justify-center ri-pb-4">
                <div class="circular-progress"
                    [style.background]="'conic-gradient('+ cc.theme.color35 +' '+(totalTimePercentage())*3.6+'deg, '+ cc.theme.color10 +' 0deg)'">
                    <ng-container *ngFor="let angle of divisionAngles()">
                        <div class="circular-division absolute left-1/2 top-1/2 w-[1.5px] h-[calc(50%+1px)] bg-black origin-top"
                            [ngStyle]="{ transform: 'rotate(' + angle + 'deg)' }" *ngIf="durationRepeatCount() > 1">
                        </div>
                    </ng-container>
                    <div class="progress-value">
                        <span class="text-14-400 color-8 block text-center">{{ totalTimePercentage() }}%</span>
                        <span class="text-24-500 color-8 block ri-py-4 text-center">{{ (totalSecondsSpent() +
                            timerSeconds()) | parseSeconds
                            }}</span>
                        <span *ngIf="duration" class="text-14-400 color-7 flex items-center justify-center">
                            <span *ngIf="duration.hour" class="ri-pe-1 block">{{duration.hour}}h</span>
                            <span *ngIf="duration.minute" class="ri-pe-1 block">{{duration.minute}}m</span>
                            <span *ngIf="duration.second" class="ri-pe-1 block">{{duration.second}}s</span>
                            <span>x {{ setup.durationRepeatCount }}</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="value-block flex items-center justify-between" *ngFor="let value of reversedTimerAnswers()">
                <span class="text-14-400 color-8">{{ value.startTimestamp | smartTimeDiff:value.endTimestamp }}</span>
                <div class="flex items-center">
                    <span class="text-14-400 color-7 ri-pe-4">{{ cc.getFormattedDate(value.startTimestamp, 'h:mm a') }}</span>
                    <app-svg name="trash" [color]="cc.theme.color7" role="button"
                        (click)="deleteTimerValue(value)"></app-svg>
                </div>
            </div>
        </div>
        <div class="progress-block ri-pb-4" *ngIf="habitType === 'numeric'">
            <div class="flex justify-center ri-pb-4">
                <div class="circular-progress" [style.background]="'conic-gradient('+ cc.theme.color35 +' '+(totalNumericPercentage())*3.6+'deg, '+ cc.theme.color10 +' 0deg)'">
                    <div class="progress-value">
                        <span class="text-24-500 color-8 block ri-py-4 text-center">{{ totalNumericValue() }}</span>
                        <span class="text-14-400 color-7 block text-center">{{ setup.numericGoal ? cc.interpolateText('bottomSheet_habitActionHabitTypeNumericalValue_withGoal', { target: setup.numericGoal + ' ' + setup.numericUnit }) : setup.numericUnit }}</span>
                    </div>
                </div>
            </div>
            <div class="value-block flex items-center justify-between" *ngFor="let value of reversedNumericAnswers()" (click)="addNumericValue(value)" role="button">
                <span class="text-14-400 color-8">{{ value.value }} {{ setup.numericUnit }}</span>
                <div class="flex items-center">
                    <span class="text-14-400 color-7 ri-pe-4">{{ cc.getFormattedDate(value.timestamp, 'h:mm a') }}</span>
                    <app-svg name="trash" [color]="cc.theme.color7" role="button" (click)="$event.stopPropagation();deleteNumericValue(value)"></app-svg>
                </div>
            </div>
        </div>
        <app-input-text-editor class="ri-pb-3" *ngIf="isDescription" [control]="getFc('description')" name="meTodoDescription" placeholder="Description"></app-input-text-editor>
        <div class="extra-block" *ngIf="habitType === 'timer' || habitType === 'numeric'"></div>
    </form>


    <div class="habit-addons">
        <app-input-hashtag class="ri-pt-3" *ngIf="habitForm.value.tags && habitForm.value.tags.length > 0"
            [control]="getFc('tags')"></app-input-hashtag>
        <app-attachment-list [title]="setup.title" class="ri-pt-3"
            *ngIf="attachmentValues && attachmentValues.length > 0" [attachments]="attachmentValues"
            [basePath]="basePath" [entity]="data.value" (deleteEvent)="deleteAttachments($event)"></app-attachment-list>
    </div>
    <!-- <pre class="color-8">{{ habitForm.value | json }}</pre>
    <p>Initial</p>
     <pre class="color-8">{{ habitInitial | json }}</pre> -->
</div>
<div class="bottom-section flex items-center justify-between ri-p-4 text-end ri-bt-2">
    <div class="flex items-center">
        <app-svg *ngIf="!isDescription" name="description" [color]="cc.theme.color35" class="ri-pe-6" role="button"
            (click)="isDescription = true"></app-svg>
        <app-svg *ngIf="!habitForm.value.tags || habitForm.value.tags.length === 0" name="hashtag" [color]="cc.theme.color35"
            class="ri-pe-6" role="button" (click)="openHashtagsDialog()"></app-svg>
        <app-input-file [newAttachments]="newAttachments" [multiple]="true"></app-input-file>
    </div>
    <button type="submit" class="btn-text text-16-500 color-1" (click)="hasChanges() ? save() : closeDialog()">{{
        hasChanges() ? cc.texts()['screen_common_save'] : cc.texts()['screen_common_close'] }}</button>
</div>

<div class="add-button flex items-center justify-between" [ngClass]="{'timer-started' : habitType === 'timer' && startTimerDate()}" *ngIf="habitType === 'timer' || habitType === 'numeric'">
    <div class="timer-second ri-ps-4 whitespace-nowrap" *ngIf="habitType === 'timer'">
        <span class="text-14-400 color-12 block text-center">{{ timerSeconds() | parseSeconds }} / {{ (duration!.totalSeconds * durationRepeatCount()) | parseSeconds }}</span>
    </div>
    <button class="btn ml-auto" (click)="habitType === 'numeric' ? addNumericValue() : startTimerDate() ? stopTimer() : startTimer()">
        <app-svg [name]="habitType === 'numeric' ? 'plus' : startTimerDate() ? 'pause' : 'play'"></app-svg>
    </button>
</div>
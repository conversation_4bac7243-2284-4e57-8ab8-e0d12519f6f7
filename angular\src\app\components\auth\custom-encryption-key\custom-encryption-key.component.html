<div class="logo-block flex justify-center items-center">
    <app-svg name="mevolve" [color]="cc.theme.color12" style="height: 40px;"></app-svg>
</div>
<div class="enc-key-block">
    <p class="text-18-500 text-white mb-0 text-center">{{cc.texts()['screen_loginEncryption_content']}}</p>
    <div class="custom-enc-input-block flex justify-between items-center ri-pt-8 ri-pb-4">
        <input class="custom-enc-input" [type]="type" placeholder="" [noSpace]="true" [formControl]="control" maxlength="20">
        <app-svg class="visibility-icon" *ngIf="control.value" [name]="type === 'password' ? 'visiblityHide' : 'visiblityShow'" [color]="type === 'password' ? cc.theme.color7 : cc.theme.color35" role="button" (click)="type = type === 'password' ? 'text' : 'password'"></app-svg>
    </div>
    <div class="ri-pt-8 ri-mt-4 flex justify-around items-center">
        <button class="btn btn-text color-11 ri-me-6" role="button" (click)="close()">BACK TO LOGIN</button>
        <button class="btn btn-text" (click)="verifyKey()" [disabled]="!control.value" [ngClass]="control.value ? 'color-35' : 'color-7'">{{ cc.texts()['overlay_loginEncryptionAttempts_proceed'] }}</button>
    </div>
</div>
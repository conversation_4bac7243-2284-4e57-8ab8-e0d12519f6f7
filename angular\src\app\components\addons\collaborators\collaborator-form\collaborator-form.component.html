<div class="top-section">
    <h6 class="heading mb-0">{{ cc.texts()['bottomSheet_inviteMember_titleTopBar'] }}</h6>
    <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
</div>
<div class="collaborator-form ri-p-4">
    <form [formGroup]="collaboratorForm" #collabForm="ngForm">
        <app-input-email [control]="getFc('email')" name="meCollabEmail"  [placeholder]="cc.texts()['bottomSheet_inviteMember_emailPlaceholder']" maxLength="320"></app-input-email>
    </form>
</div>
<!-- <pre class="text-white">{{collaboratorForm.value | json}}</pre> -->
<div class="ri-dialog-footer ri-p-4 flex justify-between ri-bt-2">
    <div class="">
        <app-input-dropdown [signal]="roleSignal" name="meCollabRole" [config]="vs.collaboratorRoleConfig()"></app-input-dropdown>
    </div>
    <button type="submit" class="btn border-0 p-0" (click)="save()" [disabled]="!hasChanges() || collaboratorForm.invalid">
        <app-svg name="send" [color]="!hasChanges() || collaboratorForm.invalid ? cc.theme.color10 : cc.theme.color35"></app-svg>
    </button>
</div>
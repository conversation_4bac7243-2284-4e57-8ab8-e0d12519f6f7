<div class="timer-round-picker">
    <div class="flex justify-between">
        <p class="text-16-500 color-8 mb-0">{{ cc.texts()['overlay_roundPicker_title'] }}</p>
    </div>
    <div class="flex justify-between ri-pt-4 round-type">
        <button class="btn" [ngClass]="{ 'active': durationRepeatType.value === 0 }" (click)="durationRepeatType.setValue(0)">{{ cc.texts()['overlay_roundPicker_optionMinimum'] }}</button>
        <button class="btn" [ngClass]="{ 'active': durationRepeatType.value === 1 }" (click)="durationRepeatType.setValue(1)">{{ cc.texts()['overlay_roundPicker_optionExact'] }}</button>
    </div>
    <div class="flex justify-between ri-py-4">
        <div class="swiper round-swiper">
            <div class="swiper-wrapper">
                <div class="swiper-slide" *ngFor="let r of rounds">{{ r }}</div>
            </div>
        </div>
    </div>
    <p class="text-16-500 color-8 mb-0 ri-pt-6 ri-py-4 text-center">{{ getRepeatText() }}</p>
    <div class="timer-picker-footer flex justify-end">
        <button class="btn-text text-16-500 color-7 ri-me-4" (click)="closeDialog()">{{ cc.texts()['screen_common_buttonCancel'] }}</button>
        <button class="btn-text text-16-500 color-1" [disabled]="!hasChanges()" (click)="save()">{{ cc.texts()['screen_common_save'] }}</button>
    </div>
</div>
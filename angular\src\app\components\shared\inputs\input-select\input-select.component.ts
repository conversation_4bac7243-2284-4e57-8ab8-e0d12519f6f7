import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatMenuModule } from '@angular/material/menu';
import { SvgComponent } from '../../svg/svg.component';
import { CacheService } from '@app/_services/cache.service';

@Component({
  selector: 'app-input-select',
  standalone: true,
  imports: [
    CommonModule,
    MatMenuModule,
    SvgComponent
  ],
  templateUrl: './input-select.component.html',
  styleUrl: './input-select.component.scss'
})

export class InputSelectComponent {

  @Input() name: string = '';
  @Input() placeholder: string = '';
  @Input() control: FormControl = new FormControl(null);
  @Input() options: any[] = [];
  @Input() multiple: boolean = false;
  @Input() disabled: boolean = false;
  @Input() map: { [key: string]: any } = {};
  @Input() hideIcon: boolean = false;

  // @Input() clear: boolean = false;
  // @Input() search: boolean = false;
  // @Input() noneValue: '' | null = '';
  // @Input() label: string = '';
  // @Input() value: string = '';
  // @Input() prefix: string = '';
  // @Input() prefixValue: string = '';
  // @Input() disabledOptions: any[] = [];
  // @Input() hiddenOptions: any[] = [];

  @Output() selectEvent = new EventEmitter<{ label: string; value: any }>();
  @Output() addNew = new EventEmitter<void>();

  constructor(public cc: CacheService) {

  }

  onSelect(value: any, event: MouseEvent) {
    this.control.setValue(value);
    this.selectEvent.emit(this.control.value);
    // event.stopPropagation();
  }
}

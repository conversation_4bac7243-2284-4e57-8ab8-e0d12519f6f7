<app-svg name="smiley" [color]="cc.theme.color35" role="button" [matMenuTriggerFor]="inputMoodMenu"></app-svg>
<mat-menu #inputMoodMenu="matMenu" class="me-menu ri-w-350px">
    <button mat-menu-item *ngFor="let mood of moods; trackBy: trackByMood;" (click)="onMoodSelected(mood, $event)">
        <div class="flex items-center justify-between">
            <span class="text-14-400 color-8 flex items-center">
                <app-svg [name]="moodEmojiMap()[mood]" [color]="cc.theme.color22" class="ri-pe-4"></app-svg>{{moodMap()[mood]}}
            </span>
            <app-svg *ngIf="multiple" name="tick" [color]="control.value?.includes(mood) ? cc.theme.color35 : cc.theme.color10"></app-svg>
        </div>
    </button>
</mat-menu>
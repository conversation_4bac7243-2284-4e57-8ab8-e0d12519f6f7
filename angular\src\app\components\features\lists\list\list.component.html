<div class="top-section">
  <h4 class="heading mb-0">My Lists</h4>
  <div class="">
    <app-svg [name]="isFilter ? 'filterOutline' : 'filter'" *ngIf="listStore.activeLists().length > 0" class="filter-icon ri-me-6" role="button" (click)="toggleFilter()"></app-svg>

    <app-svg name="more" class="more-icon" [matMenuTriggerFor]="infoMenu" role="button" [color]="cc.theme.color12"></app-svg>
    <mat-menu #infoMenu="matMenu" class="me-menu info-menu ri-w-350px">
      <button mat-menu-item class="" (click)="$event.stopPropagation();">
        <div class="ri-me-6">
          <span class="text-14-400 color-8">Group By</span>
          <app-input-dropdown *ngIf="vs.type()['listGroupBy']() !== 'none'" class="ri-pt-6px" [multiple]="true"  [signal]="vs.type()['listGroupedViewType']" [config]="vs.groupedViewTypeConfig()" (select)="vs.updateView()" textClass="text-12-400"></app-input-dropdown>
        </div>
        <app-input-dropdown [config]="vs.listGroupByConfig" [signal]="vs.type()['listGroupBy']" (select)="vs.updateView()" textClass="text-12-400 whitespace-nowrap"></app-input-dropdown>
      </button>
      <button mat-menu-item class="" (click)="$event.stopPropagation()">
        <div class="ri-me-6">
          <span class="text-14-400 color-8">Show</span>
          <app-input-dropdown class="ri-pt-6px" [config]="vs.listShowConfig" [signal]="vs.type()['listShow']" (select)="vs.updateView()" [disabled]="vs.type()['listShowType']() === 'compact'" [multiple]="true" textClass="text-12-400"></app-input-dropdown>
        </div>
        <app-input-dropdown [config]="vs.showTypeConfig()" [signal]="vs.type()['listShowType']" (select)="vs.updateShowType('listShowType')" textClass="text-12-400 whitespace-nowrap"></app-input-dropdown>
      </button>
    </mat-menu>
  </div>
</div>

<div class="body-section bg-3 position-relative">
  <div class="filter-block" [ngClass]="{'h-auto filtered': isFilter}" dragScroll>
    <app-filter-search [signal]="listStore.filter().query" [name]="''" class="me-2"></app-filter-search>
    
    <app-filter-favourite [signal]="listStore.filter().isFav" class="me-2"></app-filter-favourite>
    
    <button class="btn btn-filter btn-toggle me-2"
    (click)="listStore.filter().isCompleted.set(listStore.filter().isCompleted() === false ? null : false)"
    [ngClass]="{'active': listStore.filter().isCompleted() === false }">Incomplete</button>
    
    <button class="btn btn-filter btn-toggle me-2"
    (click)="listStore.filter().isCompleted.set(listStore.filter().isCompleted() === true ? null : true)"
    [ngClass]="{'active': listStore.filter().isCompleted() === true }">Completed</button>

    <app-filter-hashtag [signal]="listStore.filter().tags" class="me-2"></app-filter-hashtag>
  </div>

  <div class="list-block" [ngClass]="isFilter ? 'filter-applied' : 'no-filter'">

    <ng-container *ngIf="vs.type()['listGroupBy']() === 'none' && listStore.filteredLists().length !== 0">
      <ng-container
        *ngTemplateOutlet="listsBlock; context: { $implicit: listStore.filteredLists(), groupId: 'noGroup' }"></ng-container>
    </ng-container>
    <ng-container *ngIf="vs.type()['listGroupBy']() !== 'none' && listStore.filteredLists().length !== 0">
      <ng-container *ngFor="let group of listStore.hashtagGroup();let i = index;">
        <div class="text-16-400 color-35 ri-p-4 ri-bb-2 flex items-center justify-between" role="button" [attr.isOpen]="getIsOpen()" #tgroupHead (click)="toggleChildGroup(tgroupHead,'todoGroup' + i)">
          <span>{{ group.name }}</span>
          <span class="group-count" *ngIf="vs.type()['listGroupedViewType']().includes('showCounts')">{{ group.data.length }}</span>
        </div>
        <ng-container
          *ngTemplateOutlet="listsBlock; context: { $implicit: group.data, groupId: 'todoGroup' + i }"></ng-container>
      </ng-container>
    </ng-container>

    <ng-template #listsBlock let-element let-groupId="groupId">
      <div [ngClass]="vs.type()['listGroupBy']() === 'none' ? 'row-nogroup' : 'row-grouped'" [attr.id]="groupId" [attr.isGroupOpened]="getIsOpen()">
        <div class="list-data ri-px-4 ri-py-3" *ngFor="let list of element; let i = index;"
          (click)="openListItemsDialog(list)" role="button">
          <div class="flex justify-between items-start">
            <p class="text-16-400 color-8 mb-0">{{ list.title }}</p>
            <app-svg name="star" [color]="cc.theme.color35" *ngIf="list.isFav" width="24px" height="24px" class="ri-ms-4"></app-svg>
          </div>
          <p class="text-12-400 color-7 mb-0 ri-pt-2" *ngIf="vs.type()['listShow']().includes('description') && list.description">{{ list.description }}</p>
          <div class="mb-0 ri-pt-2" *ngIf="vs.type()['listShow']().includes('itemCount') || vs.type()['listShow']().includes('collaboratorsCount')">
            <div class="inline-flex items-center" *ngIf="vs.type()['listShow']().includes('itemCount')">
              <app-svg name="circleCheck" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
              <span class="text-12-400 color-7">{{ getCompletedRange(list.listItems || {}) }}</span>
            </div>
            <div class="inline-flex items-center ri-ps-3" *ngIf="vs.type()['listShow']().includes('collaboratorsCount') && ((list.members && list.members.memberHashedEmails?.length > 0) || list.isPublic)">
              <app-svg [name]="list.isPublic ? 'multiUser' : 'users'" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
              <span class="text-12-400 color-7">{{ list.members.memberHashedEmails?.length }}</span>
            </div>
          </div>
          <p class="text-12-400 color-7 mb-0 ri-pt-2 hashtags-block" *ngIf="vs.type()['listShow']().includes('hashtag') && list.hashtags.length > 0">
            <span class="hashtag" *ngFor="let tagId of list.hashtags">#{{userStore.tagMap()[tagId]}}</span>
          </p>
        </div>
      </div>
    </ng-template>

    <div class="no-result-block h-full flex items-center justify-center flex-col" *ngIf="listStore.activeLists().length === 0 || (listStore.activeLists().length > 0 && listStore.filteredLists().length === 0)">
      <app-svg [name]="listStore.activeLists().length === 0 ? 'listPlaceholder' : 'searchPlaceholder'" [color]="cc.theme.color1"></app-svg>
      <p class="text-16-400 color-8 mb-0 ri-pt-4">{{ listStore.activeLists().length === 0 ? 'No Lists' : 'No Data' }}</p>
    </div>

    <div class="extra-block" *ngIf="listStore.filteredLists().length > 0"></div>
  </div>
</div>
<div class="add-button">
  <button class="btn" (click)="openListForm()"><app-svg name="plus"></app-svg></button>
</div>
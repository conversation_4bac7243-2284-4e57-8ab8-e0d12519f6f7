import { computed, Injectable, Renderer2, RendererFactory2, Signal, signal, WritableSignal } from '@angular/core';
import { CloudFlareService } from './cloudflare.service';
import { BehaviorSubject, Observable, Subject, takeUntil } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { Config } from '../_interfaces/config.interface';
import { Clipboard } from '@angular/cdk/clipboard';
import { Router } from '@angular/router';
import { User, UserData } from '@app/_interfaces/user.interface';
import { themeData } from '@app/_datas/theme.data';
import { environment } from '@environments/environment';
import { Meta } from '@angular/platform-browser';
import { AnalyticsService } from './analytics.service';
import { StorageService } from './storage.servive';
import { getNewId } from '@app/_utils/utils';
import { countryToLanguageMap, languageMap, languages } from '@app/_datas/languages.data';
import { MatDialog } from '@angular/material/dialog';
import { DateFormatPipe } from '@app/_pipes/date-format.pipe';
import { AlertToneType, LanguageCode } from '@app/_types/generic.type';
import * as translatedTexts from '@app/_datas/translations.json';
import { LanguageDialogComponent } from '@app/components/shared/dialogs/language-dialog/language-dialog.component';
import { formatDate } from '@angular/common';

@Injectable({
  providedIn: 'root',
})

export class CacheService {

  public configSubject = new BehaviorSubject<Config | null>(null);
  public config$: Observable<Config | null> = this.configSubject.asObservable();
  public config: Config | any = {};
  // public moodMap: Record<number, string> = MoodMap;
  // public moodEmojiMap: Record<number, string> = MoodEmojiMap;
  user!: UserData;
  userData!: User;
  mevolveUserId: string = '';
  unSubscribe = new Subject<void>();
  languageCodes: WritableSignal<string[]> = signal([]);
  isLoading: boolean = false;
  mode: 'light' | 'dark' = 'dark';
  domainWithProtocol: string = window.location.origin;
  theme = themeData['Default'][this.mode];
  environment = environment;
  toneMap: { [key in AlertToneType]: string } = {
    mevolve_1: 'mevolve_1.mp3',
    mevolve_2: 'mevolve_2.mp3',
    mevolve_3: 'mevolve_3.mp3',
    mevolve_4: 'mevolve_4.mp3',
    mevolve_5: 'mevolve_5.mp3',
    none: ''
  };

  futureDates: WritableSignal<string[]> = signal([]);

  public cacheSubject = new BehaviorSubject<boolean>(false);
  public cache$: Observable<boolean> = this.cacheSubject.asObservable();

  language: WritableSignal<LanguageCode> = signal(this.ss.getLanguageCode() || 'en');
  languages: LanguageCode[] = languages;
  languageMap: { [key in LanguageCode]: { name: string; value: string } } = languageMap;
  countryToLanguageMap: { [key: string]: LanguageCode } = countryToLanguageMap;

  private userAgent: string;
  private renderer: Renderer2;
  dp: DateFormatPipe = new DateFormatPipe(); // Date pipe

  isOpeningAppstore: boolean = false;
  texts: Signal<Record<string, string>> = computed(() => {
    return translatedTexts.data[this.language()] as unknown as Record<string, string>;
  })

  constructor(
    private cfs: CloudFlareService,
    private http: HttpClient,
    private clipboard: Clipboard,
    private router: Router,
    private meta: Meta,
    private as: AnalyticsService,
    private ss: StorageService,
    private dialog: MatDialog,
    private rendererFactory: RendererFactory2,
  ) {
    this.userAgent = window.navigator.userAgent.toLowerCase();
    this.renderer = rendererFactory.createRenderer(null, null);
  }

  async init() {
    this.initModeObserver();

    const countryCode = this.ss.getCountryCode();
    if (!countryCode) {
      try {
        const countryCode = await this.getCountryCode();
        this.ss.setCountryCode(countryCode);
        this.as.commonProperties.country = countryCode;
        this.setLanguage(countryCode);
      } catch (error) {
        this.setLanguage('NA');
      }
    } else {
      this.as.commonProperties.country = countryCode;
      this.setLanguage(countryCode);
    }

    // User Id set
    const uid = this.ss.getUid();
    if (!uid) {
      const newUid = getNewId();
      this.as.commonProperties.muid = newUid;
      this.ss.setUid(newUid);
    } else {
      this.as.commonProperties.muid = uid;
    }

    // Device Id set
    const deviceId = this.ss.getDeviceId();
    if (!deviceId) {
      const newDeviceId = getNewId();
      this.as.commonProperties.device_id = newDeviceId;
      this.ss.setDeviceId(newDeviceId);
    } else {
      this.as.commonProperties.device_id = deviceId;
    }

    // Session Id set
    const sessionId = this.ss.getSessionId();
    if (!sessionId) {
      const newSessionId = getNewId();
      this.as.commonProperties.session_id = newSessionId;
      this.ss.setSessionId(newSessionId);
    } else {
      this.as.commonProperties.session_id = sessionId;
    }

    // Device Type set
    const deviceType = this.ss.getDeviceType();
    if (!deviceType) {
      const deviceType = this.getDeviceType();
      this.as.commonProperties.device_type = deviceType;
      this.ss.setDeviceType(deviceType);
    } else {
      this.as.commonProperties.device_type = deviceType;
    }

    // Device OS set
    const deviceOs = this.ss.getDeviceOs();
    if (!deviceOs) {
      const deviceOs = this.getOS();
      this.as.commonProperties.device_os = deviceOs;
      this.ss.setDeviceOs(deviceOs);
    } else {
      this.as.commonProperties.device_os = deviceOs;
    }

    // Browser set
    const browser = this.ss.getBrowser();
    if (!browser) {
      const browser = this.getBrowser();
      this.as.commonProperties.browser = browser;
      this.ss.setBrowser(browser);
    } else {
      this.as.commonProperties.browser = browser;
    }

    const futureDates = this.getFutureDates();
    this.futureDates.set(futureDates);

    this.cacheSubject.next(true);
  }

  initModeObserver() {
    const isDark = this.isDarkMode();
    this.updateTheme(isDark ? 'dark' : 'light');

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)')
      .addEventListener('change', (event) => {
        this.updateTheme(event.matches ? 'dark' : 'light');
      });
  }

  addModeClass(className: string): void {
    this.renderer.addClass(document.body, className);
  }

  removeModeClass(className: string): void {
    this.renderer.removeClass(document.body, className);
  }

  updateTheme(newMode: 'light' | 'dark') {
    this.mode = newMode;
    if (this.mode === 'dark') {
      this.removeModeClass('me-light-mode');
      this.addModeClass('me-dark-mode');
    } else {
      this.removeModeClass('me-dark-mode');
      this.addModeClass('me-light-mode');
    }
    this.theme = themeData['Default'][this.mode];
    // localStorage.setItem('preferred-theme', newMode);
    document.documentElement.setAttribute('data-theme', newMode);
  }

  setLanguage(countryCode: string) {
    const localSelectedLanguage = this.ss.getLanguageCode() || 'en';
    if (!localSelectedLanguage) {
      const languageCode = this.countryToLanguageMap[countryCode];
      if (this.isLanguageSupported(languageCode || 'en')) {
        this.ss.setLanguageCode(languageCode || 'en');
        this.as.commonProperties.language = this.languageMap[languageCode].value || this.languageMap['en'].value;
        this.language.set(languageCode || 'en');
      }
    } else {
      this.language.set(localSelectedLanguage);
      this.as.commonProperties.language = this.languageMap[localSelectedLanguage].value;
    }
  }

  isLanguageSupported(langCode: LanguageCode) {
    return this.languages.some(code => code === langCode);
  }

  getCountryCode(): Promise<string> {
    const GEOLOCATION_API = 'https://ipapi.co/json/'; // 'http://ip-api.com/json/'

    return new Promise((resolve, reject) => {
      fetch(GEOLOCATION_API)
        .then(response => response.json())
        .then(data => {
          const countryCode = data.country_code?.toLowerCase();
          if (countryCode) {
            resolve(countryCode);
          } else {
            reject('Country code not found'); // Reject if the country code is missing
          }
        })
        .catch(error => {
          console.error('Error fetching country:', error);
          reject(error); // Reject on API failure
        });
    });
  }

  isMobile(): boolean {
    return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(this.userAgent);
  }

  isTablet(): boolean {
    return /(ipad|tablet|playbook|silk)|(android(?!.*mobile))/i.test(this.userAgent);
  }

  isDesktop(): boolean {
    return !this.isMobile() && !this.isTablet();
  }

  getDeviceType(): string {
    if (this.isMobile()) return 'mobile';
    if (this.isTablet()) return 'tablet';
    return 'desktop';
  }

  getOS(): string {
    const userAgent = navigator.userAgent;
    if (/Windows NT 10.0/.test(userAgent)) return 'Windows 10';
    if (/Windows NT 6.3/.test(userAgent)) return 'Windows 8.1';
    if (/Windows NT 6.2/.test(userAgent)) return 'Windows 8';
    if (/Windows NT 6.1/.test(userAgent)) return 'Windows 7';
    if (/Mac OS X/.test(userAgent)) return 'macOS';
    if (/Android/.test(userAgent)) return 'Android';
    if (/iPhone|iPad|iPod/.test(userAgent)) return 'iOS';
    if (/Linux/.test(userAgent)) return 'Linux';
    return 'Unknown OS';
  }

  getBrowser(): string {
    const userAgent = navigator.userAgent;
    if (/Chrome\/\d+/.test(userAgent) && !/Edg/.test(userAgent)) return 'Chrome';
    if (/Safari\/\d+/.test(userAgent) && !/Chrome/.test(userAgent)) return 'Safari';
    if (/Firefox\/\d+/.test(userAgent)) return 'Firefox';
    if (/Edg\/\d+/.test(userAgent)) return 'Edge';
    if (/MSIE \d+/.test(userAgent) || /Trident\/.*rv:\d+/.test(userAgent)) return 'Internet Explorer';
    return 'Unknown Browser';
  }

  // text(id: string): string {
  //   return this.translatedTexts[id][this.language.value];
  // }

  getFutureDates(end: Date | null = null, start: Date | null = null): string[] {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Default start date is tomorrow (in local time)
    const defaultStartDate = new Date(today);
    defaultStartDate.setDate(today.getDate() + 1);

    // After 10 years from tomorrow
    const afterTenYears = new Date(
      defaultStartDate.getFullYear() + 10,
      defaultStartDate.getMonth(),
      defaultStartDate.getDate()
    );
    afterTenYears.setDate(afterTenYears.getDate() - 1);

    const startDate = start ? new Date(start) : defaultStartDate;
    const endDate = end ? new Date(end) : afterTenYears;

    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(0, 0, 0, 0);

    const result: string[] = [];

    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      result.push(`${year}-${month}-${day}`);
    }

    return result;
  }

  getPastDates(start: Date, end: Date | null = null): string[] {
    const today = new Date();

    // Get yesterday in local time
    const defaultEndDate = new Date(today);
    defaultEndDate.setDate(today.getDate() - 1);
    defaultEndDate.setHours(0, 0, 0, 0);

    const startDate = new Date(start);
    startDate.setHours(0, 0, 0, 0);

    const endDate = end ? new Date(end) : defaultEndDate;
    endDate.setHours(0, 0, 0, 0);

    const result: string[] = [];

    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      result.push(`${year}-${month}-${day}`);
    }

    return result.reverse();
  }

  isDarkMode(): boolean {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  }

  handleSharedModulesSave(event: Event, from: 'list' | 'note' | 'user', id: string): void {
    event.preventDefault();

    const platform = this.detectPlatform();

    switch (platform) {
      case 'ios':
        // window.location.href = environment.appStoreLink;
        this.openInApp(`${from}/${id}`);
        break;
      case 'android':
        window.location.href = environment.playStoreLink;
        // this.openSubscriptionDialog();
        break;
      default:
        // Use Angular router for desktop/other platforms
        this.router.navigate(['/no-access', from === 'list' ? 'save-list' : from === 'note' ? 'save-note' : from === 'user' ? 'follow-user' : '']);
        break;
    }
  }

  openInApp(id: string) {
    this.isOpeningAppstore = true;
    // Try to open app with custom scheme
    window.location.href = `hla://${id}`;

    // Also open App Store (will open both if app installed)
    setTimeout(() => {
      this.isOpeningAppstore = false;
      window.open(environment.appStoreLink, '_blank');
    }, 1000);
  }

  getPublicUser(id: string) {
    if (!this.user || !this.user.uid) {
      this.cfs.getKvData(id, 'user').pipe(takeUntil(this.unSubscribe)).subscribe(response => {
        if (response.success && response.data) {
          this.user = response.data;
        } else {
          throw new Error(`Failed to get value for id ${id}`);
        }
      })
    }
  }

  updateAppleMetaTag(id: string): void {
    const content = `app-id=${environment.appStoreId}, app-argument=${environment.webDomain}/${id}`;

    // Remove old tag if exists
    this.meta.removeTag("name='apple-itunes-app'");

    // Add updated tag
    this.meta.addTag({
      name: 'apple-itunes-app',
      content,
    });
  }

  getUserDisplayLetter(): string {
    return '';
  }

  downloadFile(url: string, fileName: string): void {
    this.http.get(url, { responseType: 'blob' }).pipe(takeUntil(this.unSubscribe)).subscribe((blob: Blob | MediaSource) => {
      const a = document.createElement('a');
      const objectUrl = URL.createObjectURL(blob);
      a.href = objectUrl;
      a.download = fileName;
      a.click();
      URL.revokeObjectURL(objectUrl); // Clean up memory
    });
  }

  detectPlatform(): 'ios' | 'android' | 'other' {
    const userAgent = navigator.userAgent.toLowerCase();
    // iOS detection
    if (/iphone|ipad|ipod/.test(userAgent)) {
      return 'ios';
    }
    // Android detection
    if (/android/.test(userAgent)) {
      return 'android';
    }
    return 'other';
  }

  copyLink(link: string) {
    this.clipboard.copy(link);
  }

  openLanguageDialog() {
    this.dialog.open(LanguageDialogComponent, {
      maxWidth: '400px',
      width: '100%',
      disableClose: true,
    });
  }

  setCurrentLanguage(langCode: LanguageCode) {
    this.ss.setLanguageCode(langCode);
    this.language.set(langCode);
  }

  textsWithVariables(textVars: { var: string; eg: string; }[]) {
    let text = '';
    textVars.forEach((item, index) => {
      text += item.var;
      if (index !== textVars.length - 1) {
        text += ', ';
      }
    });
    return text;
  }

  interpolateText(templateName: string, values: Record<string, string>): string {
    const template = this.texts()[templateName];
    const result = template.replace(/{{(.*?)}}/g, (_, key) => values[key.trim()] ?? '');
    return result;
  }

  getFormattedDate(date: Date | string | null, format: string): string {
    return formatDate(date || new Date(), format, this.language());
  }

  stopSubscription() {
    this.unSubscribe?.next();
    this.unSubscribe?.complete();
  }
}

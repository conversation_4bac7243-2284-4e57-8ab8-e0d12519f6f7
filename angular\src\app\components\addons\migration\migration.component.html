<div class="ri-p-8">
    <div class="flex justify-center items-center">
        <app-svg name="mevolve" [color]="cc.theme.color1" style="height: 49px;"></app-svg>
    </div>

    <div class="migration-imgs">
        <app-svg name="smartPhone" [color]="cc.theme.color22"></app-svg>
        <app-svg name="sync" [color]="cc.theme.color7"></app-svg>
        <app-svg name="cloud" [color]="cc.theme.color22"></app-svg>
    </div>
    
    <div>
        <p class="text-18-500 color-8 mb-0 text-center">Migration Required</p>
        <p class="text-16-400 color-8 mb-0 ri-pt-4 text-center">Your data was created in an older version of the app. To continue using the web app, you need to migrate your data to the latest version.</p>
    </div>

    <div class="ri-pt-8 ri-pb-3" *ngIf="migrating">
        <p class="text-14-400 color-1 ri-pb-4">{{cc.texts()['screen_forceUpdate_sync']}}...</p>
        <div class="bar-loader"></div>
    </div>

    <div class="flex justify-end align-center ri-pt-8" *ngIf="!migrating">
        <button class="btn btn-text color-7" (click)="logout()">{{cc.texts()['screen_forceUpdate_migrateDataContentLogout']}}</button>
        <button class="btn btn-text ri-ms-4 color-1" (click)="migrate()">{{cc.texts()['screen_forceUpdate_migrationText']}}</button>
    </div>
</div>
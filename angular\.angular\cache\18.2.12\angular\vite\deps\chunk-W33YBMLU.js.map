{"version": 3, "sources": ["../../../../../../node_modules/@angular/fire/fesm2022/angular-fire-compat-auth.mjs"], "sourcesContent": ["import 'firebase/compat/auth';\nimport { isPlatformServer } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, Injectable, Inject, Optional, NgModule } from '@angular/core';\nimport * as i1 from '@angular/fire';\nimport { keepUnstableUntilFirst, VERSION } from '@angular/fire';\nimport * as i2 from '@angular/fire/app-check';\nimport { ɵcacheInstance as _cacheInstance, ɵfirebaseAppFactory as _firebaseAppFactory, ɵlazySDKProxy as _lazySDKProxy, FIREBASE_OPTIONS, FIREBASE_APP_NAME, ɵapplyMixins as _applyMixins } from '@angular/fire/compat';\nimport { Subject, of, Observable, from, merge } from 'rxjs';\nimport { observeOn, switchMap, map, shareReplay, first, switchMapTo, subscribeOn, filter } from 'rxjs/operators';\nimport firebase from 'firebase/compat/app';\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\n// Export a null object with the same keys as firebase/compat/auth, so Proxy can work with proxy-polyfill in Internet Explorer\nconst proxyPolyfillCompat = {\n  name: null,\n  config: null,\n  emulatorConfig: null,\n  app: null,\n  applyActionCode: null,\n  checkActionCode: null,\n  confirmPasswordReset: null,\n  createUserWithEmailAndPassword: null,\n  currentUser: null,\n  fetchSignInMethodsForEmail: null,\n  isSignInWithEmailLink: null,\n  getRedirectResult: null,\n  languageCode: null,\n  settings: null,\n  onAuthStateChanged: null,\n  onIdTokenChanged: null,\n  sendSignInLinkToEmail: null,\n  sendPasswordResetEmail: null,\n  setPersistence: null,\n  signInAndRetrieveDataWithCredential: null,\n  signInAnonymously: null,\n  signInWithCredential: null,\n  signInWithCustomToken: null,\n  signInWithEmailAndPassword: null,\n  signInWithPhoneNumber: null,\n  signInWithEmailLink: null,\n  signInWithPopup: null,\n  signInWithRedirect: null,\n  signOut: null,\n  tenantId: null,\n  updateCurrentUser: null,\n  useDeviceLanguage: null,\n  useEmulator: null,\n  verifyPasswordResetCode: null\n};\nconst USE_EMULATOR = new InjectionToken('angularfire2.auth.use-emulator');\nconst SETTINGS = new InjectionToken('angularfire2.auth.settings');\nconst TENANT_ID = new InjectionToken('angularfire2.auth.tenant-id');\nconst LANGUAGE_CODE = new InjectionToken('angularfire2.auth.langugage-code');\nconst USE_DEVICE_LANGUAGE = new InjectionToken('angularfire2.auth.use-device-language');\nconst PERSISTENCE = new InjectionToken('angularfire.auth.persistence');\nconst ɵauthFactory = (app, zone, useEmulator, tenantId, languageCode, useDeviceLanguage, settings, persistence) => _cacheInstance(`${app.name}.auth`, 'AngularFireAuth', app.name, () => {\n  const auth = zone.runOutsideAngular(() => app.auth());\n  if (useEmulator) {\n    auth.useEmulator(...useEmulator);\n  }\n  if (tenantId) {\n    auth.tenantId = tenantId;\n  }\n  auth.languageCode = languageCode;\n  if (useDeviceLanguage) {\n    auth.useDeviceLanguage();\n  }\n  if (settings) {\n    for (const [k, v] of Object.entries(settings)) {\n      auth.settings[k] = v;\n    }\n  }\n  if (persistence) {\n    auth.setPersistence(persistence);\n  }\n  return auth;\n}, [useEmulator, tenantId, languageCode, useDeviceLanguage, settings, persistence]);\nclass AngularFireAuth {\n  /**\n   * Observable of authentication state; as of Firebase 4.0 this is only triggered via sign-in/out\n   */\n  authState;\n  /**\n   * Observable of the currently signed-in user's JWT token used to identify the user to a Firebase service (or null).\n   */\n  idToken;\n  /**\n   * Observable of the currently signed-in user (or null).\n   */\n  user;\n  /**\n   * Observable of the currently signed-in user's IdTokenResult object which contains the ID token JWT string and other\n   * helper properties for getting different data associated with the token as well as all the decoded payload claims\n   * (or null).\n   */\n  idTokenResult;\n  /**\n   * Observable of the currently signed-in user's credential, or null\n   */\n  credential;\n  constructor(options, name,\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  platformId, zone, schedulers, useEmulator,\n  // can't use the tuple here\n  settings,\n  // can't use firebase.auth.AuthSettings here\n  tenantId, languageCode, useDeviceLanguage, persistence, _appCheckInstances) {\n    const logins = new Subject();\n    const auth = of(undefined).pipe(observeOn(schedulers.outsideAngular), switchMap(() => zone.runOutsideAngular(() => import('firebase/compat/auth'))), map(() => _firebaseAppFactory(options, zone, name)), map(app => ɵauthFactory(app, zone, useEmulator, tenantId, languageCode, useDeviceLanguage, settings, persistence)), shareReplay({\n      bufferSize: 1,\n      refCount: false\n    }));\n    if (isPlatformServer(platformId)) {\n      this.authState = this.user = this.idToken = this.idTokenResult = this.credential = of(null);\n    } else {\n      // HACK, as we're exporting auth.Auth, rather than auth, developers importing firebase.auth\n      //       (e.g, `import { auth } from 'firebase/compat/app'`) are getting an undefined auth object unexpectedly\n      //       as we're completely lazy. Let's eagerly load the Auth SDK here.\n      //       There could potentially be race conditions still... but this greatly decreases the odds while\n      //       we reevaluate the API.\n      auth.pipe(first()).subscribe();\n      const redirectResult = auth.pipe(switchMap(auth => auth.getRedirectResult().then(it => it, () => null)), keepUnstableUntilFirst, shareReplay({\n        bufferSize: 1,\n        refCount: false\n      }));\n      const authStateChanged = auth.pipe(switchMap(auth => new Observable(sub => ({\n        unsubscribe: zone.runOutsideAngular(() => auth.onAuthStateChanged(next => sub.next(next), err => sub.error(err), () => sub.complete()))\n      }))));\n      const idTokenChanged = auth.pipe(switchMap(auth => new Observable(sub => ({\n        unsubscribe: zone.runOutsideAngular(() => auth.onIdTokenChanged(next => sub.next(next), err => sub.error(err), () => sub.complete()))\n      }))));\n      this.authState = redirectResult.pipe(switchMapTo(authStateChanged), subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n      this.user = redirectResult.pipe(switchMapTo(idTokenChanged), subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n      this.idToken = this.user.pipe(switchMap(user => user ? from(user.getIdToken()) : of(null)));\n      this.idTokenResult = this.user.pipe(switchMap(user => user ? from(user.getIdTokenResult()) : of(null)));\n      this.credential = merge(redirectResult, logins,\n      // pipe in null authState to make credential zipable, just a weird devexp if\n      // authState and user go null to still have a credential\n      this.authState.pipe(filter(it => !it))).pipe(\n      // handle the { user: { } } when a user is already logged in, rather have null\n      // TODO handle the type corcersion better\n      map(credential => credential?.user ? credential : null), subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n    }\n    return _lazySDKProxy(this, auth, zone, {\n      spy: {\n        apply: (name, _, val) => {\n          // If they call a signIn or createUser function listen into the promise\n          // this will give us the user credential, push onto the logins Subject\n          // to be consumed in .credential\n          if (name.startsWith('signIn') || name.startsWith('createUser')) {\n            // TODO fix the types, the trouble is UserCredential has everything optional\n            val.then(user => logins.next(user));\n          }\n        }\n      }\n    });\n  }\n  static ɵfac = function AngularFireAuth_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AngularFireAuth)(i0.ɵɵinject(FIREBASE_OPTIONS), i0.ɵɵinject(FIREBASE_APP_NAME, 8), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.ɵAngularFireSchedulers), i0.ɵɵinject(USE_EMULATOR, 8), i0.ɵɵinject(SETTINGS, 8), i0.ɵɵinject(TENANT_ID, 8), i0.ɵɵinject(LANGUAGE_CODE, 8), i0.ɵɵinject(USE_DEVICE_LANGUAGE, 8), i0.ɵɵinject(PERSISTENCE, 8), i0.ɵɵinject(i2.AppCheckInstances, 8));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AngularFireAuth,\n    factory: AngularFireAuth.ɵfac,\n    providedIn: 'any'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularFireAuth, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'any'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [FIREBASE_OPTIONS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [FIREBASE_APP_NAME]\n    }]\n  }, {\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1.ɵAngularFireSchedulers\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [USE_EMULATOR]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [SETTINGS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [TENANT_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [LANGUAGE_CODE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [USE_DEVICE_LANGUAGE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [PERSISTENCE]\n    }]\n  }, {\n    type: i2.AppCheckInstances,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\n_applyMixins(AngularFireAuth, [proxyPolyfillCompat]);\nclass AngularFireAuthModule {\n  constructor() {\n    firebase.registerVersion('angularfire', VERSION.full, 'auth-compat');\n  }\n  static ɵfac = function AngularFireAuthModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AngularFireAuthModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: AngularFireAuthModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [AngularFireAuth]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularFireAuthModule, [{\n    type: NgModule,\n    args: [{\n      providers: [AngularFireAuth]\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularFireAuth, AngularFireAuthModule, LANGUAGE_CODE, PERSISTENCE, SETTINGS, TENANT_ID, USE_DEVICE_LANGUAGE, USE_EMULATOR, ɵauthFactory };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,IAAM,sBAAsB;AAAA,EAC1B,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,KAAK;AAAA,EACL,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,gCAAgC;AAAA,EAChC,aAAa;AAAA,EACb,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,UAAU;AAAA,EACV,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,gBAAgB;AAAA,EAChB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,qBAAqB;AAAA,EACrB,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,yBAAyB;AAC3B;AACA,IAAM,eAAe,IAAI,eAAe,gCAAgC;AACxE,IAAM,WAAW,IAAI,eAAe,4BAA4B;AAChE,IAAM,YAAY,IAAI,eAAe,6BAA6B;AAClE,IAAM,gBAAgB,IAAI,eAAe,kCAAkC;AAC3E,IAAM,sBAAsB,IAAI,eAAe,uCAAuC;AACtF,IAAM,cAAc,IAAI,eAAe,8BAA8B;AACrE,IAAM,eAAe,CAAC,KAAK,MAAM,aAAa,UAAU,cAAc,mBAAmB,UAAU,gBAAgB,eAAe,GAAG,IAAI,IAAI,SAAS,mBAAmB,IAAI,MAAM,MAAM;AACvL,QAAM,OAAO,KAAK,kBAAkB,MAAM,IAAI,KAAK,CAAC;AACpD,MAAI,aAAa;AACf,SAAK,YAAY,GAAG,WAAW;AAAA,EACjC;AACA,MAAI,UAAU;AACZ,SAAK,WAAW;AAAA,EAClB;AACA,OAAK,eAAe;AACpB,MAAI,mBAAmB;AACrB,SAAK,kBAAkB;AAAA,EACzB;AACA,MAAI,UAAU;AACZ,eAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,QAAQ,GAAG;AAC7C,WAAK,SAAS,CAAC,IAAI;AAAA,IACrB;AAAA,EACF;AACA,MAAI,aAAa;AACf,SAAK,eAAe,WAAW;AAAA,EACjC;AACA,SAAO;AACT,GAAG,CAAC,aAAa,UAAU,cAAc,mBAAmB,UAAU,WAAW,CAAC;AAClF,IAAM,kBAAN,MAAM,iBAAgB;AAAA;AAAA;AAAA;AAAA,EAIpB;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,EACA,YAAY,SAAS,MAErB,YAAY,MAAM,YAAY,aAE9B,UAEA,UAAU,cAAc,mBAAmB,aAAa,oBAAoB;AAC1E,UAAM,SAAS,IAAI,QAAQ;AAC3B,UAAM,OAAO,GAAG,MAAS,EAAE,KAAK,UAAU,WAAW,cAAc,GAAG,UAAU,MAAM,KAAK,kBAAkB,MAAM,OAAO,yBAAsB,CAAC,CAAC,GAAG,IAAI,MAAM,oBAAoB,SAAS,MAAM,IAAI,CAAC,GAAG,IAAI,SAAO,aAAa,KAAK,MAAM,aAAa,UAAU,cAAc,mBAAmB,UAAU,WAAW,CAAC,GAAG,YAAY;AAAA,MACxU,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,CAAC;AACF,QAAI,iBAAiB,UAAU,GAAG;AAChC,WAAK,YAAY,KAAK,OAAO,KAAK,UAAU,KAAK,gBAAgB,KAAK,aAAa,GAAG,IAAI;AAAA,IAC5F,OAAO;AAML,WAAK,KAAK,MAAM,CAAC,EAAE,UAAU;AAC7B,YAAM,iBAAiB,KAAK,KAAK,UAAU,CAAAA,UAAQA,MAAK,kBAAkB,EAAE,KAAK,QAAM,IAAI,MAAM,IAAI,CAAC,GAAG,wBAAwB,YAAY;AAAA,QAC3I,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ,CAAC,CAAC;AACF,YAAM,mBAAmB,KAAK,KAAK,UAAU,CAAAA,UAAQ,IAAI,WAAW,UAAQ;AAAA,QAC1E,aAAa,KAAK,kBAAkB,MAAMA,MAAK,mBAAmB,UAAQ,IAAI,KAAK,IAAI,GAAG,SAAO,IAAI,MAAM,GAAG,GAAG,MAAM,IAAI,SAAS,CAAC,CAAC;AAAA,MACxI,EAAE,CAAC,CAAC;AACJ,YAAM,iBAAiB,KAAK,KAAK,UAAU,CAAAA,UAAQ,IAAI,WAAW,UAAQ;AAAA,QACxE,aAAa,KAAK,kBAAkB,MAAMA,MAAK,iBAAiB,UAAQ,IAAI,KAAK,IAAI,GAAG,SAAO,IAAI,MAAM,GAAG,GAAG,MAAM,IAAI,SAAS,CAAC,CAAC;AAAA,MACtI,EAAE,CAAC,CAAC;AACJ,WAAK,YAAY,eAAe,KAAK,YAAY,gBAAgB,GAAG,YAAY,WAAW,cAAc,GAAG,UAAU,WAAW,aAAa,CAAC;AAC/I,WAAK,OAAO,eAAe,KAAK,YAAY,cAAc,GAAG,YAAY,WAAW,cAAc,GAAG,UAAU,WAAW,aAAa,CAAC;AACxI,WAAK,UAAU,KAAK,KAAK,KAAK,UAAU,UAAQ,OAAO,KAAK,KAAK,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;AAC1F,WAAK,gBAAgB,KAAK,KAAK,KAAK,UAAU,UAAQ,OAAO,KAAK,KAAK,iBAAiB,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;AACtG,WAAK,aAAa;AAAA,QAAM;AAAA,QAAgB;AAAA;AAAA;AAAA,QAGxC,KAAK,UAAU,KAAK,OAAO,QAAM,CAAC,EAAE,CAAC;AAAA,MAAC,EAAE;AAAA;AAAA;AAAA,QAGxC,IAAI,gBAAc,YAAY,OAAO,aAAa,IAAI;AAAA,QAAG,YAAY,WAAW,cAAc;AAAA,QAAG,UAAU,WAAW,aAAa;AAAA,MAAC;AAAA,IACtI;AACA,WAAO,cAAc,MAAM,MAAM,MAAM;AAAA,MACrC,KAAK;AAAA,QACH,OAAO,CAACC,OAAM,GAAG,QAAQ;AAIvB,cAAIA,MAAK,WAAW,QAAQ,KAAKA,MAAK,WAAW,YAAY,GAAG;AAE9D,gBAAI,KAAK,UAAQ,OAAO,KAAK,IAAI,CAAC;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAoB,SAAS,gBAAgB,GAAM,SAAS,mBAAmB,CAAC,GAAM,SAAS,WAAW,GAAM,SAAY,MAAM,GAAM,SAAY,sBAAsB,GAAM,SAAS,cAAc,CAAC,GAAM,SAAS,UAAU,CAAC,GAAM,SAAS,WAAW,CAAC,GAAM,SAAS,eAAe,CAAC,GAAM,SAAS,qBAAqB,CAAC,GAAM,SAAS,aAAa,CAAC,GAAM,SAAY,oBAAmB,CAAC,CAAC;AAAA,EACxa;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,IACzB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,aAAa,iBAAiB,CAAC,mBAAmB,CAAC;AACnD,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,cAAc;AACZ,aAAS,gBAAgB,eAAe,QAAQ,MAAM,aAAa;AAAA,EACrE;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,eAAe;AAAA,EAC7B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,eAAe;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;", "names": ["auth", "name"]}
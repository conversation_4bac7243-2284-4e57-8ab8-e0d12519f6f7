import { AttachmentType, EntityStatus, RepeatFilterType } from "@app/_types/generic.type";

export const moods: number[] = [0, 1, 2, 3, 4];

export const attachmentTypes: AttachmentType[] = ['image', 'audio', 'video', 'document'];

export const entityStatus: EntityStatus[] = ['all', 'missed', 'overdue', 'draft', 'skipped', 'completed', 'none'];

export const repeatTypes: RepeatFilterType[] = ['all', 'one_time', 'repeated'];
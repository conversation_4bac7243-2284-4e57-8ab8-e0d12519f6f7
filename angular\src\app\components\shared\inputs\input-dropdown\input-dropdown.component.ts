import { CommonModule } from '@angular/common';
import { Component, computed, EventEmitter, inject, Input, Output, signal, Signal, WritableSignal } from '@angular/core';
import { MatMenuModule, MatMenuTrigger } from '@angular/material/menu';
import { SvgComponent } from '../../svg/svg.component';
import { InputDropdownConfig } from '@app/_interfaces/generic.interface';
import { UserStore } from '@app/_stores';
import { CacheService } from '@app/_services/cache.service';
import { AlertService } from '@app/_services/alert.service';

@Component({
  selector: 'app-input-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    MatMenuModule,
    SvgComponent
  ],
  templateUrl: './input-dropdown.component.html',
  styleUrl: './input-dropdown.component.scss'
})

export class InputDropdownComponent {

  @Input() placeholder: string = '';
  @Input() type: 'LISTADDON' | null = null;
  @Input() enumData: { [key: string]: string } = {};
  @Input() disabled: boolean = false;
  @Input() multiple: boolean = false;
  @Input() signal: WritableSignal<any> = signal<any>(null);
  @Input() defaultValue: Signal<string> = computed(() => this.cc.texts()['screen_common_none']);
  @Input() config: { [key: string]: InputDropdownConfig } = {};
  @Input() disabledValues: string[] = [];
  @Input() hiddenValues: string[] = [];
  @Input() notApplicableValues: {value: string, reason: string }[] = [];
  @Input() selectedMark: 'RADIO' | 'TICK' = 'RADIO';
  @Input() textClass: string = 'text-16-400';
  @Input() colorClass: string = 'color-35';
  readonly userStore = inject(UserStore);
  viewSignals: Signal<Record<string, WritableSignal<any>>> = this.userStore.viewSignals;

  @Output() select = new EventEmitter<string>();
  @Output() selectNested = new EventEmitter<{ value: string, nesteadValue: string }>();

  constructor(public cc: CacheService, private alertService: AlertService) {

  }

  get values(): string[] {
    return Object.keys(this.config);
  };

  keepOriginalOrder = () => 0;

  async selectValue(value: string, event: MouseEvent, trigger: MatMenuTrigger) {
    if (this.type === 'LISTADDON' && this.signal() !== 'none') {
      event.stopPropagation();
      if (this.signal() === value) return;
      const currentValue = this.config[this.signal()].name.toLocaleLowerCase();
      const selectedValue = this.config[value];
      const confirm = await this.alertService.confirm(`Switch to ${selectedValue.name}?`, `The ${currentValue} will be hidden${selectedValue.name === 'None' ? '' : ' and ' + selectedValue.name.toLocaleLowerCase() + ' will be displayed'}`, this.cc.texts()['overlay_listSwitch_switch'], this.cc.texts()['screen_common_buttonCancel']);
      if (!confirm) return;
    }
    if (this.multiple) {
      event.stopPropagation();
      if (this.config[value] && this.config[value]?.signalName) {
        return;
      }
      const selectedValues = this.signal();
      const updatedValues = selectedValues.includes(value) ? selectedValues.filter((item: string) => item !== value) : [...selectedValues, value];
      this.signal.set(updatedValues);
      this.select.emit(updatedValues);
    } else {
      this.signal.set(value);
      this.select.emit(value);
      trigger.closeMenu();
    }
  }

  selectNestedValue(value: string, nesteadValue: string, trigger: MatMenuTrigger, event: MouseEvent) {
    event.stopPropagation();
    const signal = this.viewSignals()[this.config[value].signalName || ''];
    signal?.set(nesteadValue);
    this.selectNested.emit({ value: value, nesteadValue });
    trigger.closeMenu();
  }

  getNestedSignal(value: string): WritableSignal<any> {
    return this.viewSignals()[this.config[value].signalName || ''];
  }

  getNestedConfigValues(key: string): string[] {
    return Object.keys(this.config[key]?.config || {});
  }

  getNestedIcon(value: string, nesteadValue: string): string {
    return this.config?.[value]?.config?.[nesteadValue]?.icon || '';
  }

  getNestedName(value: string, nesteadValue: string): string {
    return this.config?.[value]?.config?.[nesteadValue]?.name || '';
  }

  getNesteadValue(value: string): string {
    const nesteadVal = this.config?.[value];
    return nesteadVal.config?.[this.viewSignals()[nesteadVal.signalName || '']()]?.name || '';
  }

  isNesteadIcon(value: string, nesteadValue: string): boolean {
    return this.config[value]?.config?.[nesteadValue]?.icon ? true : false;
  }

  get selectedValue(): string {
    if (this.multiple) {
      const configKeys = Object.keys(this.config);
      const withSignalConfig = configKeys.filter((key: string) => this.config[key]?.signalName && this.getNestedSignal(key)() !== 'none');
      const values = this.signal().filter((str: string) => !this.hiddenValues.includes(str)).map((str: string) => this.config[str].name);

      const nesteadValues = withSignalConfig.map((key: string) => this.config[key]?.name) || [];
      return [...nesteadValues, ...values].join(", ");
    } else {
      if (!this.config[this.signal()]) return this.defaultValue();
      return this.config[this.signal()].name;
    }
  }
}

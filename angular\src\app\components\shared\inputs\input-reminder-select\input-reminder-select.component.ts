import { CommonModule } from '@angular/common';
import { Component, Inject, signal, WritableSignal } from '@angular/core';
import { SvgComponent } from '../../svg/svg.component';
import { InputDropdownComponent } from '../input-dropdown/input-dropdown.component';
import { InputDurationComponent } from '../input-duration/input-duration.component';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { AlertService } from '@app/_services/alert.service';
import { FormControl } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { CacheService } from '@app/_services/cache.service';
import { RemindTypeConfig } from '@app/_datas/generic-config.data';
import { InputDropdownConfig } from '@app/_interfaces/generic.interface';
import { RemindType } from '@app/_types/generic.type';

@Component({
  selector: 'app-input-reminder-select',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    InputDropdownComponent,
    InputDurationComponent
  ],
  templateUrl: './input-reminder-select.component.html',
  styleUrl: './input-reminder-select.component.scss'
})

export class InputReminderSelectComponent {

  remindTypeConfig: { [key: string]: InputDropdownConfig } = RemindTypeConfig;
  remindType: WritableSignal<RemindType> = signal<RemindType>('BEFORE');
  initialRemindType: RemindType = 'BEFORE';
  reminderTime: FormControl = new FormControl(null);
  unSubscribe = new Subject<void>();
  lastReminderTime: number = 0;

  constructor(
    public dialogRef: MatDialogRef<InputReminderSelectComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { value: number, type: 'add' | 'edit', allRemainders: number[] },
    private alertService: AlertService,
    public cc: CacheService,
    private dialog: MatDialog
  ) {

    console.log("data--->", this.data.value);
    const reminderType = this.getRemainderType(this.data.value);
    this.initialRemindType = reminderType;
    this.remindType.set(reminderType);
    this.reminderTime.setValue(Math.abs(this.data.value));
    this.lastReminderTime = this.reminderTime.value;

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  remindTypeChange() {
    const type = this.remindType();
    if (this.reminderTime.value !== 0) {
      this.lastReminderTime = this.reminderTime.value;
    }
    this.reminderTime.setValue(type === 'ON_TIME' ? 0 : this.lastReminderTime);
  }

  async deleteReminder() {
    const confirmed = await this.alertService.confirm(this.cc.texts()['overlay_reminderDelete_title'], this.cc.texts()['overlay_reminderDelete_content'], this.cc.texts()['overlay_reminderDelete_buttonDelete'], this.cc.texts()['screen_common_buttonCancel']);
    if (!confirmed) {
      return;
    } else {
      const updatedArray = this.data.allRemainders.filter(r => r !== this.data.value);
      this.dialogRef.close({ value: updatedArray, type: 'DELETE' });
    }
  }

  hasChanges(): boolean {
    return this.remindType() !== this.initialRemindType || this.reminderTime.value !== Math.abs(this.data.value);
  }

  async save() {
    const reminderValue = this.getRemainderValue(this.remindType(), this.reminderTime.value);
    if (this.data.allRemainders.includes(reminderValue)) {
      await this.alertService.alert(this.cc.texts()['overlay_duplicateReminder_title'], this.cc.texts()['overlay_duplicateReminder_content'], this.cc.texts()['screen_common_ok']);
    } else {
      const updatedArray = [...this.data.allRemainders, reminderValue];
      this.dialogRef.close({ value: updatedArray, type: 'UPDATE' });
    }
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
        this.dialogRef.close();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }

  getRemainderType(value: number): RemindType {
    if (value < 0) return 'BEFORE';
    if (value === 0) return 'ON_TIME';
    return 'AFTER';
  }

  getRemainderValue(type: RemindType, value: number): number {
    if (type === 'BEFORE') return -value;
    if (type === 'ON_TIME') return 0;
    return value;
  }

  ngOnDestroy() {
    this.unSubscribe.complete();
    this.unSubscribe.next();
  }
}

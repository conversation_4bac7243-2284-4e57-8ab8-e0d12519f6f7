import { CommonModule } from '@angular/common';
import { Component, Inject, inject, signal, ViewChild, WritableSignal } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, NgForm, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { CalendarGroup, CalendarIntegration } from '@app/_interfaces/calendar-integration.interface';
import { Subject, takeUntil } from 'rxjs';
import * as _ from 'lodash';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { getNewId, getNormalizedCurrentTimezone } from '@app/_utils/utils';
import { InputToggleComponent } from '@app/components/shared/inputs/input-toggle/input-toggle.component';
import { CalendarIntegrationStore, FeatureStore, HabitStore, JournalStore, TodoStore, UserStore } from '@app/_stores';
import { FirebaseFunctionService } from '@app/_services/firebase-function.service';
import { CalendarIntegrationType, SyncType } from '@app/_types/generic.type';
import { dbVersion } from '@app/_configs/db.config';
import { CryptographyService } from '@app/_services/cryptography.service';
import { CacheService } from '@app/_services/cache.service';
import { AlertService } from '@app/_services/alert.service';
import { FirestoreCollection } from '@app/_enums/firestore-collection.enum';

@Component({
  selector: 'app-calendar-account-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatMenuModule,
    SvgComponent,
    InputToggleComponent
  ],
  templateUrl: './calendar-account-form.component.html',
  styleUrl: './calendar-account-form.component.scss'
})

export class CalendarAccountFormComponent {

  @ViewChild('jSetupForm') jSetupForm!: NgForm;
  unSubscribe = new Subject<void>();
  calendarAccountForm: FormGroup;
  calendarAccountInitial: CalendarIntegration;
  readonly calendarStore = inject(CalendarIntegrationStore);
  readonly userStore = inject(UserStore);
  readonly featureStore = inject(FeatureStore);
  readonly todoStore = inject(TodoStore);
  readonly habitStore = inject(HabitStore);
  readonly journalStore = inject(JournalStore);
  calendarConfig: { [key: string]: SyncType } = {};
  syncConfig: { [key: string]: WritableSignal<boolean> } = {};
  calendarType: CalendarIntegrationType;

  constructor(
    private dialogRef: MatDialogRef<CalendarAccountFormComponent>,
    private fb: FormBuilder,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'new' | 'edit', value: CalendarIntegration },
    private fbfs: FirebaseFunctionService,
    private cryptoService: CryptographyService,
    public cc: CacheService,
    private alertService: AlertService
  ) {

    this.calendarAccountInitial = this.initiateForm(data.value);
    console.log('calendarAccountInitial', this.calendarAccountInitial);
    console.log(" Calendar account data--->>>", data.value);
    this.calendarType = data.value.calendarType;

    this.calendarAccountForm = this.fb.group({
      id: new FormControl(this.calendarAccountInitial.id, Validators.required),
      name: new FormControl(this.calendarAccountInitial.name, Validators.required),
      email: new FormControl(this.calendarAccountInitial.email, Validators.required),
      isPaused: new FormControl(this.calendarAccountInitial.isPaused),
      syncTodos: new FormControl(this.calendarAccountInitial.syncTodos),
      syncHabits: new FormControl(this.calendarAccountInitial.syncHabits),
      syncJournals: new FormControl(this.calendarAccountInitial.syncJournals),
      calendarGroups: new FormControl(
        this.calendarAccountInitial.calendarGroups.map(group => ({ ...group }))
      )
    });

    this.calendarConfig = {
      syncTodos: this.calendarAccountForm.get('syncTodos')?.value,
      syncHabits: this.calendarAccountForm.get('syncHabits')?.value,
      syncJournals: this.calendarAccountForm.get('syncJournals')?.value,
    }
    this.syncConfig = {
      syncTodos: signal(this.calendarAccountForm.get('syncTodos')?.value !== 'disabled'),
      syncHabits: signal(this.calendarAccountForm.get('syncHabits')?.value !== 'disabled'),
      syncJournals: signal(this.calendarAccountForm.get('syncJournals')?.value !== 'disabled'),
    }

    this.calendarAccountForm.get('calendarGroups')?.value.forEach((group: CalendarGroup) => {
      if (group.title !== 'Mevolve') {
        this.calendarConfig[group.id] = group.syncStatus;
        this.syncConfig[group.id] = signal(group.syncStatus !== 'disabled');
      }
    });

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  initiateForm(calendarAccount: CalendarIntegration): any {
    return {
      id: calendarAccount.id,
      name: calendarAccount.name,
      email: calendarAccount.email,
      isPaused: calendarAccount.isPaused || false,
      syncTodos: calendarAccount.syncTodos,
      syncHabits: calendarAccount.syncHabits,
      syncJournals: calendarAccount.syncJournals,
      calendarGroups: calendarAccount.calendarGroups.map((group: CalendarGroup) => {
        if (group.title !== 'Mevolve') {
          group.syncStatus = this.data.mode === 'new' ? 'syncNotifications' : group.syncStatus;
        }
        return group;
      })
    }
  }

  getFc(fcName: string): FormControl {
    return this.calendarAccountForm.get(fcName) as FormControl;
  }

  syncNotification(type: 'syncTodos' | 'syncHabits' | 'syncJournals' | string) {
    switch (type) {
      case 'syncTodos':
      case 'syncHabits':
      case 'syncJournals':
        this.calendarAccountForm.get(type)?.setValue(this.calendarConfig[type] === 'syncNotifications' ? 'onlySync' : 'syncNotifications');
        this.calendarConfig[type] = this.calendarAccountForm.get(type)?.value;
        break;

      default:
        this.calendarAccountForm.get('calendarGroups')?.setValue((this.calendarAccountForm.get('calendarGroups')?.value as CalendarGroup[]).map((group: CalendarGroup) => {
          if (group.title !== 'Mevolve' && group.id === type) {
            group.syncStatus = this.calendarConfig[type] === 'syncNotifications' ? 'onlySync' : 'syncNotifications';
            this.calendarConfig[group.id] = group.syncStatus;
          }
          return group;
        }));
        break;
    }
  }

  syncChange(type: 'syncTodos' | 'syncHabits' | 'syncJournals' | string) {
    switch (type) {
      case 'syncTodos':
      case 'syncHabits':
      case 'syncJournals':
        this.calendarAccountForm.get(type)?.setValue(this.syncConfig[type]() ? 'syncNotifications' : 'disabled');
        this.calendarConfig[type] = this.calendarAccountForm.get(type)?.value;
        break;

      default:
        const groups = this.calendarAccountForm.get('calendarGroups')?.value as CalendarGroup[];
        const calendar = groups.find((group: CalendarGroup) => group.title !== 'Mevolve');
        if (calendar) {
          this.calendarAccountForm.get('calendarGroups')?.setValue(groups.map((group: CalendarGroup) => {
            if (group.title !== 'Mevolve' && group.id === type) {
              group.syncStatus = this.syncConfig[group.id]() ? 'syncNotifications' : 'disabled';
              this.calendarConfig[group.id] = group.syncStatus;
            }
            return group;
          }));
        }
        break;
    }
  }

  hasChanges() {
    const initial = _.cloneDeep(this.calendarAccountInitial);
    const current = _.cloneDeep(this.calendarAccountForm.value);
    return !_.isEqual(initial, current);
  }

  async save(isResync = false) {
    const payload = this.getCalenderPayload();
    this.cc.isLoading = true;
    try {
      const callable = this.fbfs.getCallable(isResync ? 'calendar-resyncCalendar' : this.data.mode === 'new' ? 'calendar-saveNewCalendar' : 'calendar-saveCalendarConfig');
      const response: any = await callable(payload);
      this.dialogRef.close();
      console.log('✅ Calendar Connect Response:', response);
    } catch (error) {
      console.error('❌ Calendar connection failed:', error);
    } finally {
      this.cc.isLoading = false;
    }
  }

  getCalenderPayload(): any {
    const updatedCalendarAccount = { ...this.data.value, ...this.calendarAccountForm.value };
    updatedCalendarAccount.uid = this.userStore.user!()?.uid || '';
    updatedCalendarAccount.docVer = dbVersion;
    updatedCalendarAccount.docCollection = 'calendarIntegrations';
    updatedCalendarAccount.source = 'client';
    updatedCalendarAccount.deletedAt = null;
    updatedCalendarAccount.createdAt = this.data.mode === 'new' ? new Date() : this.data.value.createdAt;
    updatedCalendarAccount.sessionId = getNewId();

    const syncRequest = this.cryptoService.decrypt(updatedCalendarAccount, true)
    const payload = {
      uid: this.userStore.user?.() ? this.userStore.user()?.uid : '',
      calendarDetails: JSON.stringify(syncRequest),
      calAccId: updatedCalendarAccount.id,
      mevolveCalId: this.getMevolveCalId(updatedCalendarAccount.calendarGroups),
      refreshKey: updatedCalendarAccount.refreshToken,
      timeZone: this.data.mode === 'new' ? getNormalizedCurrentTimezone() : updatedCalendarAccount.timeZone,
      eventsList: null,
      tasksList: this.featureStore.calendarEventEntities(),
      type: updatedCalendarAccount.calendarType,
      calendarConfig: JSON.stringify(this.calendarConfig),
      redirectUri: 'postmessage'
    };
    return payload;
  }

  getMevolveGroup(calendarGroup: CalendarGroup[]): CalendarGroup | null {
    if (!calendarGroup.length) {
      return null;
    }

    return calendarGroup.find(group =>
      group.title === 'Mevolve'
    ) || null;
  }

  getMevolveCalId(calendarGroup: CalendarGroup[]): string | null {
    const mevolveGroup = this.getMevolveGroup(calendarGroup);
    if (!mevolveGroup) {
      return null;
    }
    return mevolveGroup.id;
  }

  async deleteCalendar() {
    const res = await this.alertService.confirm(this.cc.texts()['overlay_deleteCalendarAccountConfirmation_title'], this.cc.texts()['overlay_deleteCalendarAccountConfirmation_content'], this.cc.texts()['screen_common_delete'], this.cc.texts()['screen_common_buttonCancel'], 'color-11', 'color-7');
    if (!res) return;
    if (res.confirm === true) {
      this.deleteCalendarAccount();
    } else if (res.confirm === false) {
      return;
    }
  }

  async deleteCalendarAccount() {
    const payload = this.deleteCalendarPayload();
    this.cc.isLoading = true;
    try {
      const callable = this.fbfs.getCallable('calendar-deleteCalendar');
      const response: any = await callable(payload);
      if(response.data.statusCode === 200) {
        this.dialogRef.close();
        console.log('✅ Calendar Delete Response:', response);
      }else {
        throw new Error(response);
      }
    } catch (error) {
      console.error('❌ Calendar deletion failed:', error);
    } finally {
      this.cc.isLoading = false;
    }
  }

  deleteCalendarPayload(): {
    dataList: { id: string; docCollection: FirestoreCollection }[];
    uid: string;
    calId: string;
    type: CalendarIntegrationType;
  } {
    const payload = {
      dataList: this.getDataList(),
      uid: this.cc.user.uid,
      calId: this.data.value.id,
      type: this.data.value.calendarType,
    };
    return payload;
  }

  getDataList(): { id: string; docCollection: FirestoreCollection }[] {
    const dataList: { id: string; docCollection: FirestoreCollection }[] = [];
    const todos = this.todoStore.todos();
    const habitSetups = this.habitStore.setups();
    const journalSetups = this.journalStore.setups();
    const calendarEventSetups = this.calendarStore.eventSetups();
    const calendarEvents = this.calendarStore.events();
    const calAccountId = this.data.value.calendarType === 'google' ? `g_${this.data.value.email}` : `m_${this.data.value.email}`;
    todos.forEach(todo => {
      dataList.push({ id: todo.id, docCollection: FirestoreCollection.Todos });
    });
    habitSetups.forEach(habit => {
      dataList.push({ id: habit.id, docCollection: FirestoreCollection.HabitSetups });
    });
    journalSetups.forEach(journal => {
      dataList.push({ id: journal.id, docCollection: FirestoreCollection.JournalSetups });
    });
    calendarEventSetups.forEach(event => {
      if (event.calendarAccountId === calAccountId) {
        dataList.push({ id: event.id, docCollection: FirestoreCollection.CalendarEventSetups });
      }
    });
    calendarEvents.forEach(event => {
      if (event.calendarAccountId === calAccountId) {
        dataList.push({ id: event.id, docCollection: FirestoreCollection.CalendarEventActions });
      }
    });
    return dataList;
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }
}

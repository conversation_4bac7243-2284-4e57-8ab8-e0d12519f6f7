import { Component, computed, inject, Input, signal, Signal, SimpleChanges, ViewChild, WritableSignal } from '@angular/core';
import { EntityGroup, EntitySetup } from '@app/_interfaces/feature.interface';
import { CacheService } from '@app/_services/cache.service';
import { MoneyTrackerStore, UserStore } from '@app/_stores';
import { CdkVirtualScrollViewport, ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { MoneyTrackerBlockComponent } from '../money-tracker-block/money-tracker-block.component';
import { FilterHashtagComponent } from '@app/components/shared/filters/filter-hashtag/filter-hashtag.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { FilterDateRangeComponent } from '@app/components/shared/filters/filter-date-range/filter-date-range.component';
import { FilterAttachmentComponent } from '@app/components/shared/filters/filter-attachment/filter-attachment.component';
import { FilterSearchComponent } from '@app/components/shared/filters/filter-search/filter-search.component';
import { DateFormatPipe } from '@app/_pipes/date-format.pipe';
import { DragScrollDirective } from '@app/_directives/drag-scroll.directive';
import { AttachmentType, TransactionType } from '@app/_types/generic.type';
import { MapService } from '@app/_services/map.service';

@Component({
  selector: 'app-money-tracker-tab',
  standalone: true,
  imports: [
    CommonModule,
    ScrollingModule,
    MoneyTrackerBlockComponent,
    FilterHashtagComponent,
    SvgComponent,
    FilterDateRangeComponent,
    FilterAttachmentComponent,
    FilterSearchComponent,
    DragScrollDirective
  ],
  templateUrl: './money-tracker-tab.component.html',
  styleUrl: './money-tracker-tab.component.scss'
})

export class MoneyTrackerTabComponent {


  @ViewChild(CdkVirtualScrollViewport) viewport!: CdkVirtualScrollViewport;

  readonly moneyStore = inject(MoneyTrackerStore);
  readonly userStore = inject(UserStore);
  @Input() mode: Signal<'PAST' | 'FUTURE'> = computed(() => 'PAST');
  @Input() show: string[] = [];
  @Input() groupedViewType: string[] = [];
  @Input() filter: boolean = false;
  @Input() groupType: string = 'date';
  @Input() activeTab: string = 'todo';
  @Input() isDataAvailable: boolean = false;
  @Input() descriptionType: string = 'none';
  groupTypeSignal: WritableSignal<string> = signal<string>('date');

  filterSearch = signal<string>('');
  filterType = signal<string>('all');
  filterHashtag = signal<string[]>([]);
  filterAttachment = signal<AttachmentType[]>([]);
  startDate = signal<Date | null>(null);
  endDate = signal<Date | null>(null);
  transactionTypeMap: Signal<Record<TransactionType, string>> = this.mapService.transactionTypeMap;

  scrollOffset: number = 0;

  transactions: Signal<EntityGroup[]> = computed(() => {
    const firstDateString = this.moneyStore.firstDateString();
    const firstDate = new Date(firstDateString || '');
    const groupType = this.groupTypeSignal();
    const groupedData = new Map<string, EntityGroup>();

    let dateStrings: string[] = [];
    switch (this.mode()) {
      case 'PAST':
        dateStrings = this.cc.getPastDates(this.startDate() || firstDate, this.endDate() ? this.endDate() : null);
        break;
      case 'FUTURE':
        dateStrings = this.endDate() === null && this.startDate() === null ? this.cc.futureDates() : this.cc.getFutureDates(this.endDate(), this.startDate());
        break;
    }

    dateStrings.forEach(dateString => {
      const entities = this.moneyStore.getComputedEntities(dateString, groupType as 'date' | 'hashtag' | 'transactionType' | 'setup');
      let allEntities = [...entities.withoutTime, ...entities.withTime];
      const entityGroupData = entities.groupedData;

      switch (groupType) {
        case 'date':
          allEntities = this.applyFilter(allEntities);
          if (!groupedData.has(dateString)) {
            groupedData.set(dateString, {
              id: dateString,
              name: dateString,
              data: [],
            });
          }
          groupedData.get(dateString)!.data.push(...allEntities);

          break;
        case 'hashtag':
          const tagId = Object.keys(entityGroupData);
          tagId.forEach(key => {
            const entities = this.applyFilter(entityGroupData[key]);
            if (!groupedData.has(key)) {
              groupedData.set(key, {
                id: key,
                name: key,
                data: [],
              });
            }
            groupedData.get(key)!.data.push(...entities);
          });
          break;
        case 'transactionType':
          const entryId = Object.keys(entityGroupData);
          entryId.forEach(key => {
            const entities = this.applyFilter(entityGroupData[key]);
            if (!groupedData.has(key)) {
              groupedData.set(key, {
                id: key,
                name: key,
                data: [],
              });
            }
            groupedData.get(key)!.data.push(...entities);
          });
          break;
        case 'setup':
          const setupId = Object.keys(entityGroupData);
          setupId.forEach(key => {
            const entities = this.applyFilter(entityGroupData[key]);
            if (!groupedData.has(key)) {
              groupedData.set(key, {
                id: key,
                name: key,
                data: [],
              });
            }
            groupedData.get(key)!.data.push(...entities);
          });
          break;
      }
    });
    const orderedKeys = [...groupedData.keys()];
    const result = orderedKeys
      .filter(key => key !== '')                     // all non-empty keys, in original order
      .map(key => groupedData.get(key)!)
      .concat(
        groupedData.has('')
          ? [groupedData.get('')!]                  // add the empty key group last, if it exists
          : []
      );
    return result;
  });

  constructor(public cc: CacheService, private mapService: MapService) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['activeTab']) {
      this.viewport?.checkViewportSize();
      this.viewport?.scrollToOffset(this.scrollOffset);
    }

    if (changes['groupType']) {
      this.groupTypeSignal.set(this.groupType);
    }
  }

  getGroupName(group: EntityGroup) {
    const groupType = this.groupTypeSignal();
    switch (groupType) {
      case 'date':
        return this.cc.getFormattedDate(group.name, 'd MMM y, EEE');
      case 'hashtag':
        return this.userStore.tagMap()[group.id] ? this.userStore.tagMap()[group.id] : this.cc.texts()['screen_common_groupByNoHashtags'];
      case 'transactionType':
        return this.transactionTypeMap()[group.id as TransactionType];
      case 'setup':
        return this.moneyStore.idToSetup()[group.id]?.title;
      default:
        return group.name;
    }
  }

  getNetAmount(entities: EntitySetup[]): { netAmount: string, type: 'positive' | 'negative' } {
    let netAmount = 0;
    let currency = '₹';
    entities.forEach(entity => {
      const transaction = this.moneyStore.idToTransaction()[entity.id];
      const setup = this.moneyStore.idToSetup()[transaction.setupId];
      if (!currency) {
        currency = setup.currency;
      }
      if (transaction.transactionType === 'expense') {
        netAmount -= Number(transaction.amount);
      } else {
        netAmount += Number(transaction.amount);
      }
    });

    return { netAmount: currency + ' ' + Math.abs(netAmount), type: netAmount >= 0 ? 'positive' : 'negative' };
  }

  onScrolledIndexChange(index: number) {
    this.scrollOffset = this.viewport?.measureScrollOffset() ?? 0;
  }

  getIsOpen() {
    return this.groupedViewType.includes('collapsedView') ? 'false' : 'true';
  }

  applyFilter(entities: EntitySetup[]): EntitySetup[] {
    // Filters
    const search = this.filterSearch().toLowerCase();
    const hashtags = this.filterHashtag();
    const attachments = this.filterAttachment();
    return entities.filter(entity => {
      const transaction = this.moneyStore.idToTransaction()[entity.id];
      const matchesSearch = !search || entity.title.toLowerCase().includes(search);
      const matchesHashtag = hashtags.length === 0 || (transaction.tags || []).some(tag => hashtags.includes(tag));
      const matchesAttachment = attachments.length === 0 || (transaction.attachments || []).some(att => attachments.includes(att.fileType));

      return matchesSearch && matchesHashtag && matchesAttachment;
    });
  }

  isFiltered() {
    return this.filterSearch() || this.filterType() !== 'all' || this.filterHashtag().length || this.filterAttachment().length || this.startDate() || this.endDate();
  }

  clearFilter() {
    this.filterSearch.set('');
    this.filterType.set('all');
    this.filterHashtag.set([]);
    this.filterAttachment.set([]);
    this.startDate.set(null);
    this.endDate.set(null);
  }

  trackByForEntity(index: number, entity: EntitySetup) {
    return entity.id;
  }

  trackByForGroup(index: number, group: EntityGroup) {
    return group.id;
  }

  toggleChildGroup(element: HTMLElement, groupId: string) {
    const isOpened = element.getAttribute('isOpen');
    const groupedElement = document.getElementById(groupId);
    if (!groupedElement) {
      return
    }
    if (isOpened === 'false') {
      groupedElement.setAttribute('isGroupOpened', 'true');
      element.setAttribute('isOpen', 'true');
    } else {
      groupedElement.setAttribute('isGroupOpened', 'false');
      element.setAttribute('isOpen', 'false');
    }
  }
}

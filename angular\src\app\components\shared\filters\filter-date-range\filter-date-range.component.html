<button class="btn btn-filter btn-toggle h-full" (click)="openRangePiceker()" [ngClass]="{ 'active': startDate() && endDate() }">
    <app-svg name="calendar" [color]="startDate() && endDate() ? cc.theme.color12 : cc.theme.color7" class="me-1" style="height: 15px; width: 15px;"></app-svg>
    <span>{{startDate() && endDate() ? cc.getFormattedDate(startDate(), 'd MMM y') + ' - ' + cc.getFormattedDate(endDate(), 'd MMM y') : 'Date'}}</span>
    <div class="clear-icon ms-2" *ngIf="startDate() && endDate()" role="button" (click)="clearFilter();$event.stopPropagation()">
        <app-svg class="d-flex align-items-center justify-content-center" name="close" [color]="cc.theme.color1"></app-svg>
    </div>
</button>
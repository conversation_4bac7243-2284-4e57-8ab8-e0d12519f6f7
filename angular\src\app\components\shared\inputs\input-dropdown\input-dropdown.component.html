<div class="mb-0 me-input-value {{colorClass}} {{textClass}}" [ngClass]="{ 'me-disabled' : disabled }" [matMenuTriggerFor]="meInputDropdown" #meInputTrigger="matMenuTrigger" role="button">{{ selectedValue || defaultValue() }}</div>
<mat-menu #meInputDropdown="matMenu" class="me-menu me-input-dropdown">
    <ng-container *ngFor="let value of values">
        <button mat-menu-item *ngIf="!hiddenValues.includes(value)" [disabled]="disabledValues.includes(value)" class=""  (click)="selectValue(value, $event, meInputTrigger)">
            <div class="flex justify-between items-center">
                <span class="ri-me-6 text-14-400 color-8 flex items-center"><app-svg class="ri-pe-3" [name]="config[value].icon || ''" *ngIf="config && config[value]?.icon" [color]="cc.theme.color35"></app-svg>{{ config[value].name }}</span>
                <app-svg name="tick" *ngIf="multiple && !config[value]?.signalName" [color]="signal().includes(value) ? cc.theme.color35 : cc.theme.color10"></app-svg>
                <div *ngIf="multiple && config[value] && config[value].signalName && config[value].config">
                    <div class="text-14-400 mb-0 me-input-value {{colorClass}} {{textClass}}" [ngClass]="{ 'me-disabled' : config[value].disabled }" [matMenuTriggerFor]="meNestedInputDropdown" #meNestedInputTrigger="matMenuTrigger" role="button">{{ getNesteadValue(value) }}</div>
                    <mat-menu #meNestedInputDropdown="matMenu" class="me-menu me-input-dropdown">
                        <button mat-menu-item class="" *ngFor="let nesteadValue of getNestedConfigValues(value)" (click)="selectNestedValue(value, nesteadValue, meNestedInputTrigger, $event)">
                            <div class="flex justify-between items-center">
                                <span class="ri-me-6 text-14-400 color-8 flex items-center"><app-svg class="ri-pe-3" [name]="getNestedIcon(value, nesteadValue)" *ngIf="isNesteadIcon(value, nesteadValue)" [color]="cc.theme.color35"></app-svg>{{ getNestedName(value, nesteadValue) }}</span>
                                <div class="radio-mark" [ngClass]="{'selected': nesteadValue === getNestedSignal(value)()}"></div>
                            </div>
                        </button>
                    </mat-menu>
                </div>
                <ng-container *ngIf="!multiple">
                    <div class="radio-mark" *ngIf="selectedMark === 'RADIO'" [ngClass]="{'selected': value === signal()}"></div>
                    <app-svg name="tick" *ngIf="selectedMark === 'TICK' && value === signal()" [color]="cc.theme.color35"></app-svg>
                </ng-container>
            </div>
            <span class="text-12-400 color-7 mb-0 ri-pt-6px" *ngIf="config[value].description">{{ config[value].description }}</span>
        </button>
    </ng-container>
    <button mat-menu-item class="not-applicable" *ngFor="let data of notApplicableValues" (click)="$event.stopPropagation()">
        <div class="flex justify-between items-center">
            <span class="ri-me-6 flex items-start">
                <app-svg class="ri-pe-3" [name]="config[data.value].icon || ''" *ngIf="config && config[data.value]?.icon" [color]="cc.theme.color7" style="margin-top: 5px;"></app-svg>
                <div>
                    <span class="text-14-400 color-7 mb-0 whitespace-nowrap">{{ config[data.value].name }}</span>
                    <span class="text-12-400 color-7 mb-0 ri-pt-1 whitespace-nowrap">{{ data.reason }}</span>
                </div>
            </span>
            <app-svg name="tick" [color]="cc.theme.color7"></app-svg>
        </div>
    </button>
</mat-menu>
<div class="top-section">
  <h6 class="heading mb-0">{{ cc.texts()['bottomSheet_habitNumericalTypeValueInput_placeholder'] }}</h6>
  <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
</div>
<div class="flex items-center justify-between ri-p-4">
    <app-input-number [control]="numericControl" name="habitNumericValue" [placeholder]="cc.texts()['bottomSheet_habitNumericalTypeValueInput_placeholder']" [hideIcon]="true"></app-input-number>
    <p class="text-12-400 color-7 mb-0 unit-block whitespace-nowrap">{{ data.unit }}</p>
</div>
<div class="ri-dialog-footer ri-p-4 flex items-center justify-between">
  <button type="button" class="btn-text text-16-500 color-8 flex items-center" (click)="closeDialog()"><app-svg class="ri-me-2" name="clock" [color]="cc.theme.color35"></app-svg>{{ cc.getFormattedDate(timestamp.value, 'h:mm a') }}</button>
  <div class="flex items-center">
    <button type="button" class="btn-text text-16-500 color-7 me-4 p-0" (click)="closeDialog()">{{ cc.texts()['screen_common_buttonCancel'] }}</button>
    <button type="submit" class="btn-text text-16-500 color-1 p-0" (click)="save()" [disabled]="!hasChanges()">{{ cc.texts()['screen_common_save'] }}</button>
  </div>
</div>
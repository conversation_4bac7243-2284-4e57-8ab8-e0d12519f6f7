import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, Output, signal, WritableSignal } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { CacheService } from '@app/_services/cache.service';
import { HashtagComponent } from '@app/components/addons/hashtags/hashtag/hashtag.component';
import { Subject, takeUntil } from 'rxjs';
import { SvgComponent } from '../../svg/svg.component';
import { UserStore } from '@app/_stores';

@Component({
  selector: 'app-filter-hashtag',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent
  ],
  templateUrl: './filter-hashtag.component.html',
  styleUrl: './filter-hashtag.component.scss'
})
export class FilterHashtagComponent {

  unSubscribe = new Subject<void>();
  @Input() signal: WritableSignal<string[]> = signal<string[]>([]);
  @Output() select = new EventEmitter<string[]>();
  readonly userStore = inject(UserStore);

  constructor(public dialog: MatDialog, public cc: CacheService) {
    
  }

  getLabel() {
    // {{signal().length > 0 ? signal().length === 1 ? userStore.tagMap()[signal()[0]] : signal().length + ' ' + 'Hashtags' : 'Hashtag' }}
    if (this.signal().length === 0) {
      return this.cc.texts()['screen_common_hashtag'];
    } else if (this.signal().length === 1) {
      return this.userStore.tagMap()[this.signal()[0]];
    } else {
      return this.cc.interpolateText('screen_common_filterHashtagMultiple', { numberOfHashtag: this.signal().length.toString() });
    }
  }

  openHashtagsDialog() {
    const dialog = this.dialog.open(HashtagComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        values: this.signal(),
        type: 'filter'
      },
    })

    dialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.signal.set(result);
        this.select.emit(result);
      }
    });
  }
}

import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SvgComponent } from '../../svg/svg.component';
import { MatDialog } from '@angular/material/dialog';
import { AlertService } from '@app/_services/alert.service';
import { InputTimerSelectComponent } from '../input-timer-select/input-timer-select.component';
import { Subject, takeUntil } from 'rxjs';
import { CacheService } from '@app/_services/cache.service';

@Component({
  selector: 'app-input-timer',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SvgComponent
  ],
  templateUrl: './input-timer.component.html',
  styleUrl: './input-timer.component.scss'
})

export class InputTimerComponent {

  @Input() title: string = '';
  @Input() placeholder: string = '';
  @Input() name: string = '';
  @Input() hideIcon: boolean = false;
  @Input() defaultValue!: string;
  @Input() control: FormControl = new FormControl();
  @Input() durationRepeatCount: FormControl = new FormControl(1);
  @Input() durationRepeatType: FormControl = new FormControl(1);
  @Input() minLength: string | null = null;
  @Input() maxLength: string | null = null;
  @Input() readonly: boolean = false;
  @Input() icon: string = 'text';
  unSubscribe = new Subject<void>();
  duration: { year: number, month: number, day: number, hour: number, minute: number, second: number } | null;

  constructor(private dialog: MatDialog, private alertService: AlertService, public cc: CacheService) {
    this.duration = this.control.value ? this.parseMinutes(this.control.value) : null;
  }

  ngOnInit() {
    this.duration = this.control.value ? this.parseMinutes(this.control.value) : null;
  }

  getRepeatText(): string {
    if (this.durationRepeatType.value === 0) {
      return this.durationRepeatCount.value === 1 ? this.cc.texts()['overlay_roundPicker_previewMinimumRound'] : this.cc.interpolateText('overlay_roundPicker_previewMinimumRounds', { rounds: this.durationRepeatCount.value });
    } else {
      return this.durationRepeatCount.value === 1 ? this.cc.texts()['overlay_roundPicker_exactRound'] : this.cc.interpolateText('overlay_roundPicker_exactRounds', { rounds: this.durationRepeatCount.value });
    }
  }

  openTimerDialog() {
    const dialog = this.dialog.open(InputTimerSelectComponent, {
      width: '100%',
      maxWidth: '500px',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        mode: this.duration ? 'edit' : 'new',
        hour: this.duration ? this.duration.hour.toString().padStart(2, '0') : '00',
        minute: this.duration ? this.duration.minute.toString().padStart(2, '0') : '20',
        second: this.duration ? this.duration.second.toString().padStart(2, '0') : '00',
        durationRepeatCount: this.durationRepeatCount.value,
        durationRepeatType: this.durationRepeatType.value
      },
    })

    dialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        if (result.type === 'DELETE') {
          this.duration = null;
          this.control.setValue(null);
          this.durationRepeatCount.setValue(1);
          this.durationRepeatType.setValue(1);
        } else {
          this.duration = { year: 0, month: 0, day: 0, hour: result.hour, minute: result.minute, second: result.second };
          const isoDuration = this.toISODuration(this.duration);
          this.control.setValue(isoDuration);
          this.durationRepeatCount.setValue(result.durationRepeatCount);
          this.durationRepeatType.setValue(result.durationRepeatType);
        }
      }
    });
  }

  parseMinutes(isoDuration: string): { year: number, month: number, day: number, hour: number, minute: number, second: number } | null {
    const regex = /P(?:(\d+)Y)?(?:(\d+)M)?(?:(\d+)D)?T?(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/;
    const match = isoDuration.match(regex);

    if (!match) return null;

    const [, year, month, day, hour, minute, second] = match.map(v => parseInt(v || '0', 10));

    return { year, month, day, hour, minute, second };
  }

  toISODuration(duration: {
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
    second: number;
  }): string {
    return `P${duration.year}Y${duration.month}M${duration.day}DT${duration.hour}H${duration.minute}M${duration.second}S`;
  }
}

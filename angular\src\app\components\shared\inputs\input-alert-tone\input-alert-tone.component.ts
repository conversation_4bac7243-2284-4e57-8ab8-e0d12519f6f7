import { CommonModule } from '@angular/common';
import { Component, computed, Input, Signal } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatMenuModule } from '@angular/material/menu';
import { AlertToneType } from '@app/_types/generic.type';
import { SvgComponent } from '../../svg/svg.component';
import { CacheService } from '@app/_services/cache.service';

@Component({
  selector: 'app-input-alert-tone',
  standalone: true,
  imports: [
    CommonModule,
    MatMenuModule,
    SvgComponent
  ],
  templateUrl: './input-alert-tone.component.html',
  styleUrl: './input-alert-tone.component.scss'
})

export class InputAlertToneComponent {

  @Input() title: string = '';
  @Input() name: string = '';
  @Input() hideIcon: boolean = false;
  @Input() control: FormControl = new FormControl('mevolve_1');
  @Input() readonly: boolean = false;
  @Input() volume: FormControl = new FormControl(100);
  @Input() placeholder: Signal<string> = computed(() => this.cc.texts()['screen_habitSetupAdd_noCompletionAlert']);

  alertTones: AlertToneType[] = ['none', 'mevolve_1', 'mevolve_2', 'mevolve_3', 'mevolve_4', 'mevolve_5'];
  alertTone: { [key in any]: string } = {
    none: 'None',
    mevolve_1: 'Mevolve 1',
    mevolve_2: 'Mevolve 2',
    mevolve_3: 'Mevolve 3',
    mevolve_4: 'Mevolve 4',
    mevolve_5: 'Mevolve 5'
  };

  constructor(public cc: CacheService) {

  }

  selectTone(tone: AlertToneType, event: MouseEvent) {
    event.stopPropagation();
    if (tone === this.control.value) return;
    this.control.setValue(tone);
    if(tone !== 'none') {
      this.playAudio(tone);
    }
  }

  playAudio(value: AlertToneType) {
    const audio = new Audio();
    audio.src = `assets/tones/${this.cc.toneMap[value]}`;
    audio.load();
    audio.play();
  }
}

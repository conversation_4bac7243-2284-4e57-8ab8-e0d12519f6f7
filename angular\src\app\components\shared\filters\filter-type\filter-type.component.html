<button class="btn btn-filter btn-toggle h-full" [matMenuTriggerFor]="filterTypeMenu" [ngClass]="{ 'active': value() !== 'all' }">
    <app-svg name="habit" [color]="value() !== 'all' ? cc.theme.color12 : cc.theme.color7" class="me-1" style="height: 15px; width: 15px;"></app-svg>
    <span>{{value() === 'all' ? cc.texts()['screen_common_filterType'] : repeatTypeMap()[value()]}}</span>
</button>

<mat-menu #filterTypeMenu="matMenu" class="me-menu ri-w-350px">
    <button mat-menu-item *ngFor="let type of repeatTypes; let i = index;" (click)="onTypeSelected(type, $event)">
        <span class="text-14-400 color-8 flex items-center justify-between">
            <span>{{repeatTypeMap()[type]}}</span>
            <app-svg name="tick" *ngIf="value() === type" [color]="cc.theme.color35"></app-svg>
        </span>
    </button>
</mat-menu>
<div class="flex justify-between items-center" [ngClass]="blockClass" (click)="openHabit(setup().id)" role="button">
    <div class="">
        <p class="ri-pb-2 mb-0" *ngIf="show.includes('label') && isLabel"><span class="entity-badge text-12-400 color-7" >{{ cc.texts()['screen_common_habit'] }}</span></p>
        <p class="text-16-400 color-8 mb-0">{{ setup().title }}</p>
        <p class="text-12-400 color-7 mb-0 ri-pt-2" *ngIf="descriptionType !== 'none' && habit()?.description">{{ (habit()?.description || '') | parseText: descriptionType === 'short' ? 'short' : 'full' }}</p>
        <div class="mb-0 ri-pt-2"
            *ngIf="(show.includes('time') || show.includes('reminder') || show.includes('duration')) || show.includes('repeat') && setup().startAt.timeString !== '00:00' || (show.includes('attachments') && habit() && (habit()?.attachments?.length || 0) > 0)">
            <div class="inline-flex items-center ri-pe-3" *ngIf="show.includes('time') && setup().startAt.timeString !== '00:00'">
                <app-svg name="clock" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7">{{ setup().startAt.timeString | parseTime }}</span>
            </div>
            <div class="inline-flex items-center ri-pe-3"
                *ngIf="show.includes('reminder') && setup().reminderAt.length > 0 && setup().startAt.timeString !== '00:00'">
                <app-svg name="bell" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7"><span *ngFor="let reminder of setup().reminderAt;let i = index;">{{ ds.getReminderText(reminder) }}<span *ngIf="i < setup().reminderAt.length - 1">, </span></span></span>
            </div>
            <div class="inline-flex items-center ri-pe-3" *ngIf="show.includes('duration') && setup().startAt.timeString !== '00:00'">
                <app-svg name="timer" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7">{{ setup().duration | parseMinutes }}</span>
            </div>
            <div class="inline-flex items-center ri-pe-3"
                *ngIf="show.includes('attachments') && habit() && (habit()?.attachments?.length || 0) > 0">
                <app-svg name="attachment" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7">{{ habit()?.attachments?.length }}</span>
            </div>
            <div class="inline-flex items-center ri-pe-3" *ngIf="show.includes('repeat') && setup().repeat.length > 0">
                <app-svg name="repeat" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7">{{ setup().repeat[0] | parseRule }}</span>
            </div>
        </div>
        <p class="text-12-400 color-7 mb-0 ri-pt-2 hashtags-block"
            *ngIf="show.includes('hashtag') && setup().tags.length > 0">
            <span class="hashtag" *ngFor="let tagId of setup().tags">#{{userStore.tagMap()[tagId]}}</span>
        </p>
    </div>
    <div>
        <app-input-checkmark-advanced [inputId]="setup().id" [checked]="entity.status === 'completed'" [readonly]="true" [draft]="entity.status === 'draft'" [invalid]="entity.status === 'missed'" [percentage]="entity.percentage"></app-input-checkmark-advanced>
    </div>
</div>
import { patchState, signalStore, withComputed, withHooks, withMethods, withState } from "@ngrx/signals";
import { computed, inject } from "@angular/core";
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CryptographyService } from "@app/_services/cryptography.service";
import { FirebaseFunctionService } from "@app/_services/firebase-function.service";
import { firstValueFrom } from "rxjs";
import { UserStore } from "./user.store";
import { IndexDbService } from "@app/_services/index-db.service";
import { dbVersion } from "@app/_configs/db.config";
import { FirestoreCollection } from "@app/_enums/firestore-collection.enum";
import { DependencyService } from "@app/_services/dependency.service";
import { UtilsService } from "@app/_services/utils.service";
import { Journal, JournalSetup } from "@app/_interfaces/journal.interface";
import { CalendarEntitySetup, CalendarEventEntity, EntitySetup, FeatureSetup } from "@app/_interfaces/feature.interface";
import { getDateString, getTodayDateWithoutTime } from "@app/_utils/utils";
import { DayCode } from "@app/_types/generic.type";

type JournalState = {
    journals: Journal[];
    setups: JournalSetup[];
    isLoading: boolean;
    selectedJournalSetupIds: string[];
    filter: { query: string; isFav: boolean; isCompleted: boolean | null; tags: string[] };
};

const initialState: JournalState = {
    journals: [],
    setups: [],
    isLoading: false,
    selectedJournalSetupIds: [],
    filter: { query: "", isFav: false, isCompleted: null, tags: [] }
};

export const JournalStore = signalStore(
    { providedIn: 'root' },
    withState(initialState),

    withComputed((store, userStore = inject(UserStore), ds = inject(DependencyService)) => ({
        activeSetups: computed<JournalSetup[]>(() => {
            return store.setups().filter(setup => !setup.deletedAt);
        }),
        enabledSetups: computed<JournalSetup[]>(() => {
            return store.setups().filter(setup => setup.isPaused === false && !setup.deletedAt);
        }),
        activeJournals: computed<Journal[]>(() => {
            return store.journals().filter(journal => !journal.deletedAt);
        }),
        firstDateString: computed<string>(() => {
            let todayDateString = getDateString();
            const setups = store.setups();
            if (!setups || setups.length === 0) {
                return todayDateString;
            }

            return setups.reduce(
                (acc, setup) => {
                    if (setup.deletedAt || !setup.startAt || setup.isPaused === true) return acc;
                    const dateStr = setup.startAt.dateString;

                    if (!acc || (acc && dateStr < acc)) {
                        acc = dateStr;
                    }

                    return acc;
                },
                todayDateString
            );
        }),
        lastDateString: computed<string | null>(() => {
            const setups = store.setups();
            const todayDateString = getDateString();

            if (!setups || setups.length === 0) {
                return null;
            }

            let lastDate: string | null = null;

            for (const setup of setups) {
                const date = setup.endAt?.dateString ?? null;

                if (setup.deletedAt || !setup.startAt || setup.isPaused === true || (date && date > todayDateString)) continue;

                if (date === null) return null;

                if (!lastDate || date > lastDate) {
                    lastDate = date;
                }
            }

            return lastDate;
        }),
        journalMap: computed<{ setups: Record<string, Record<string, Journal>> }>(() => {
            const setups: Record<string, Record<string, Journal>> = {};

            store.journals().forEach(item => {
                const setupId = item.setupId;
                const dateString = item.dueAt?.dateString;

                if (!setupId || !dateString || item.deletedAt) return;

                if (!setups[setupId]) {
                    setups[setupId] = {};
                }

                setups[setupId][dateString] = item;
            });

            return { setups };
        }),
        selectedSetups: computed(() => {
            const ids = store.selectedJournalSetupIds();
            return new Map(
                [...ids].map(id => [id, store.setups().find(setup => setup.id === id) || null])
            );
        }),
        calendarEventEntities: computed<CalendarEventEntity[]>(() => {
            const entities: CalendarEventEntity[] = [];
            store.setups().forEach(entity => {
                entities.push({
                    id: entity.id,
                    data: entity,
                    localUpdatedAt: entity.localUpdatedAt,
                    updateType: 'raw',
                    isCreate: true
                });
            });
            return entities;
        }),
        idToSetup: computed<Record<string, JournalSetup>>(() => {
            const idToSetup: Record<string, JournalSetup> = {};
            store.setups().forEach(setup => {
                idToSetup[setup.id] = setup;
            });
            return idToSetup;
        }),
        ruleMap: computed<{
            oneDay: { [key in string]: EntitySetup[] },
            daily: EntitySetup[],
            weekly: { [key in DayCode]: EntitySetup[] },
            monthly: { [key in string]: { [key in string]: EntitySetup[] } },
            ordinally: { [key in string]: { [key in DayCode]: EntitySetup[] } },
        }>(() => {
            const { oneDay, daily, weekly, monthly, ordinally } = ds.computeRule(store.setups(), 'journal');
            return { oneDay, daily, weekly, monthly, ordinally };
        }),
    })),

    withMethods((
        store,
        idbService = inject(IndexDbService),
        userStore = inject(UserStore),
        cryptoService = inject(CryptographyService),
        firebaseFunctionService = inject(FirebaseFunctionService),
        utilsService = inject(UtilsService),
        ds = inject(DependencyService)
    ) => ({

        addJournalSetup: async (journalSetup: JournalSetup) => {

            const journal: JournalSetup = journalSetup;
            await idbService.add('journalSetups', journalSetup);

            // Upload data to the backend
            const syncRequest = cryptoService.prepareRawData({ ...journal });
            await firebaseFunctionService.uploadData(syncRequest);
        },

        updateJournalSetups: async (journalSetups: JournalSetup[]) => {
            const oldJournals = [];
            const newJournals = [];
            for (const journal of journalSetups) {
                const oldJournal = await firstValueFrom(idbService.getEntityById('journalSetups', journal.id));
                const newJournal = await idbService.update('journalSetups', journal.id, journal);
                oldJournal!.localUpdatedAt = new Date();
                newJournal!.cloudUpdatedAt = null;
                newJournal!.lastUpdatedAt = new Date();
                oldJournals.push(oldJournal);
                newJournals.push(newJournal);
            };

            const syncRequest = cryptoService.preparePatchData(
                oldJournals,
                newJournals,
                FirestoreCollection.JournalSetups
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },

        deleteJournalSetup: async (journalSetups: JournalSetup[]) => {
            const oldJournals = [];
            const newJournals = [];
            for (const journal of journalSetups) {
                journal.deletedAt = new Date();
                const oldJournal = await firstValueFrom(idbService.getEntityById('journalSetups', journal.id));
                const newJournal = await idbService.update('journalSetups', journal.id, journal);
                oldJournal!.localUpdatedAt = new Date();
                newJournal!.cloudUpdatedAt = null;
                oldJournals.push(oldJournal);
                newJournals.push(newJournal);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldJournals,
                newJournals,
                FirestoreCollection.JournalSetups
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },

        addJournal: async (journalData: Journal) => {

            const journal: Journal = journalData;
            await idbService.add('journalActions', journalData);

            // Upload data to the backend
            const syncRequest = cryptoService.prepareRawData({ ...journal });
            await firebaseFunctionService.uploadData(syncRequest);
        },

        updateJournals: async (journals: Journal[]) => {
            const oldJournals = [];
            const newJournals = [];
            for (const journal of journals) {
                const oldJournal = await firstValueFrom(idbService.getEntityById('journalActions', journal.id));
                const newJournal = await idbService.update('journalActions', journal.id, journal);
                oldJournal!.localUpdatedAt = new Date();
                newJournal!.cloudUpdatedAt = null;
                newJournal!.lastUpdatedAt = new Date();
                oldJournals.push(oldJournal);
                newJournals.push(newJournal);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldJournals,
                newJournals,
                FirestoreCollection.JournalActions
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },

        deleteJournals: async (journals: Journal[]) => {
            const oldJournals = [];
            const newJournals = [];
            for (const journal of journals) {
                journal.deletedAt = new Date();
                const oldJournal = await firstValueFrom(idbService.getEntityById('journalActions', journal.id));
                const newJournal = await idbService.update('journalActions', journal.id, journal);
                oldJournal!.localUpdatedAt = new Date();
                newJournal!.cloudUpdatedAt = null;
                oldJournals.push(oldJournal);
                newJournals.push(newJournal);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldJournals,
                newJournals,
                FirestoreCollection.JournalActions
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },

        getComputedEntities: (dateString: string, filterCompleted: boolean = false, groupBy: 'date' | 'hashtag' | 'mood' = 'date'): { withTime: EntitySetup[], withoutTime: EntitySetup[], calenderEvents: CalendarEntitySetup[], groupedData: { [key in string]: EntitySetup[] } } => {
            const [day, weekday, nthWeek, month, isLastDay] = ds.parseDateInfo(dateString);
            let entityList: EntitySetup[] = [];
            const computedJournals = store.ruleMap();
            entityList = [...(computedJournals.oneDay[dateString] || []), ...computedJournals.daily, ...computedJournals.weekly[weekday], ...computedJournals.monthly[month][day], ...computedJournals.ordinally[nthWeek][weekday], ...(isLastDay ? computedJournals.monthly[month]['-1'] : [])];
            const entitiesWithTime: EntitySetup[] = [];
            const entitiesWithoutTime: EntitySetup[] = [];
            const calenderEvents: CalendarEntitySetup[] = [];
            const groupedData: { [key in string]: EntitySetup[] } = {};
            const today = getTodayDateWithoutTime();

            entityList.forEach(entity => {
                const startsBeforeOrOn = entity.startAt ? entity.startAt.dateString <= dateString : true; // The entity either starts before or on the target date, or doesn't have a start date
                const endsAfterOrOn = entity.endAt ? entity.endAt.dateString >= dateString : true; // The entity either ends after or on the target date, or doesn't have an end date
                const isInDateRange = startsBeforeOrOn && endsAfterOrOn;

                if (!isInDateRange) return;

                const clonedEntity = { ...entity };

                const journal = store.journalMap().setups[clonedEntity.id] && store.journalMap().setups[clonedEntity.id][dateString] || null;
                const setup = store.idToSetup()[clonedEntity.id];
                if (!setup || setup.isPaused) return;
                const isCompleted: boolean = journal && journal.completedAt && journal.dueAt.dateString === dateString || false;
                const isDraft: boolean = journal && (journal.description.trim() !== '' || journal.emotion !== null || journal.attachments.length > 0) || false;
                const isMissed = new Date(`${dateString}T${clonedEntity.startAt.timeString}:00`) < today && !isCompleted && !isDraft;
                clonedEntity.invalid = isMissed;
                clonedEntity.status = isCompleted ? 'completed' : isDraft ? 'draft' : isMissed ? 'missed' : 'none';

                if (filterCompleted && isCompleted) {
                    return;
                }

                const hasTime = clonedEntity.startAt && clonedEntity.startAt.timeString !== "00:00";
                if (hasTime) {
                    entitiesWithTime.push(clonedEntity);
                    const startDate = new Date(`${dateString}T${clonedEntity.startAt.timeString}:00`);
                    calenderEvents.push({
                        id: clonedEntity.id,
                        start: startDate,
                        end: new Date(startDate.getTime() + clonedEntity.duration * 60 * 1000),
                        title: clonedEntity.title,
                        cssClass: clonedEntity.status,
                        meta: {
                            entity: 'journal',
                            startAt: clonedEntity.startAt,
                            endAt: clonedEntity.endAt,
                            invalid: clonedEntity.invalid
                        }
                    })
                } else {
                    entitiesWithoutTime.push(clonedEntity);
                }

                switch (groupBy) {
                    case 'date':
                        (groupedData[dateString] ??= []).push(clonedEntity);
                        break;
                    case 'hashtag':
                        const tags = [...clonedEntity.tags, ...(journal ? journal.tags : [])];
                        if (tags.length === 0) {
                            (groupedData[''] ??= []).push(clonedEntity);
                        } else {
                            tags.forEach(tag => {
                                const id = tag || '';
                                (groupedData[id] ??= []).push(clonedEntity);
                            });
                        }
                        break;
                    case 'mood':
                        const mood = journal ? journal.emotion : null;
                        if (mood === null) {
                            (groupedData[''] ??= []).push(clonedEntity);
                        } else {
                            (groupedData[mood.toString()] ??= []).push(clonedEntity);
                        }
                        break;
                }
            })
            return { withTime: entitiesWithTime, withoutTime: entitiesWithoutTime, calenderEvents, groupedData: groupedData };
        },

        getNewJournalSetup(): JournalSetup {
            const user = userStore.user?.();
            return {
                id: utilsService.getNewId(),
                title: '',
                duration: 15,
                startAt: {
                    dateString: '',
                    timeString: '',
                    dateTime: new Date(),
                    timeWithOffset: ''
                },
                endAt: null,
                isPaused: false,
                isStartTimeSet: false,
                isTmzAffected: false,
                reminderAt: [],
                repeat: ["RRULE:FREQ=DAILY"],
                tags: [],
                members: { memberHashedEmails: [], membersConfig: {} },
                uid: user?.uid ?? '',
                ownerName: user?.name,
                ownerEmail: user?.email,
                docVer: dbVersion,
                docCollection: FirestoreCollection.JournalSetups.toString(),
                encData: {
                    dek: cryptoService.createEncryptedDocKey(),
                    encFields: [
                        'title',
                        'ownerName',
                        'ownerEmail',
                        'members.membersConfig{}.eEmail',
                    ]
                },
                source: 'client',
                sessionId: utilsService.getNewId(),
                createdAt: new Date(),
                localUpdatedAt: new Date(),
                cloudUpdatedAt: new Date(),
                lastUpdatedAt: new Date(),
                permaDeletedAt: null,
                deletedAt: null,
            }
        },

        getNewJournal: (): Journal => {
            const user = userStore.user?.();
            return {
                id: utilsService.getNewId(),
                uid: user?.uid ?? '',
                docVer: dbVersion,
                docCollection: FirestoreCollection.JournalActions.toString(),
                ownerName: user?.name,
                ownerEmail: user?.email,
                source: 'client',
                encData: {
                    dek: cryptoService.createEncryptedDocKey(),
                    encFields: [
                        'description',
                        'ownerName',
                        'ownerEmail',
                        'lastUpdatedBy',
                        'members.membersConfig{}.eEmail',
                    ]
                },
                setupId: '',
                attachments: [],
                completedAt: null,
                description: '',
                dueAt: {
                    dateString: '',
                    timeString: '',
                    dateTime: new Date(),
                    timeWithOffset: ''
                },
                emotion: null,
                members: { memberHashedEmails: [], membersConfig: {} },
                tags: [],
                sessionId: utilsService.getNewId(),
                createdAt: new Date(),
                localUpdatedAt: new Date(),
                cloudUpdatedAt: new Date(),
                lastUpdatedAt: new Date(),
                lastUpdatedBy: user?.uid ?? '',
                permaDeletedAt: null,
                deletedAt: null,
            }
        },

        filterTags: (tags: string[]) => {
            patchState(store, (state) => ({ filter: { ...state.filter, tags } }));
        },

        getJournalFeature(journalSetup: JournalSetup): FeatureSetup {
            const status = ds.getFeatureStatus(journalSetup.startAt.dateString, journalSetup.endAt?.dateString);
            return {
                id: journalSetup.id,
                name: 'journal',
                icon: 'journal',
                entityName: 'journal',
                description: journalSetup.title,
                status: status,
                disabled: !journalSetup.isPaused,
                isDefault: false,
                startAt: journalSetup.startAt,
                endAt: journalSetup.endAt,
                entity: journalSetup
            };
        },
        selectSetup(id: string) {
            patchState(store, (state) => ({ selectedJournalSetupIds: [...state.selectedJournalSetupIds, id] }));
        }
    })),

    withHooks({
        async onInit(
            store,
            idbService = inject(IndexDbService)
        ) {
            idbService.journalSetups$.pipe(takeUntilDestroyed()).subscribe(data => {
                if (data) {
                    patchState(store, { setups: data });
                }
            });
            idbService.journals$.pipe(takeUntilDestroyed()).subscribe(data => {
                if (data) {
                    patchState(store, { journals: data });
                }
            });
        },
    }),
);

import {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  Mat<PERSON>nch<PERSON>,
  MatButton,
  MatButtonModule,
  MatFabAnchor,
  Mat<PERSON><PERSON>Button,
  MatIconAnchor,
  <PERSON><PERSON><PERSON>Button,
  Mat<PERSON>iniFabAnchor,
  MatMiniFabButton
} from "./chunk-BDNJ6BOL.js";
import "./chunk-F57CFVEQ.js";
import "./chunk-2NKILEDD.js";
import "./chunk-HBY6INAZ.js";
import "./chunk-CB7IVAZV.js";
import "./chunk-2PRKVIQ6.js";
import "./chunk-S4OGKXCW.js";
import "./chunk-N25OJVE5.js";
import "./chunk-RXHPGQPJ.js";
export {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>uttonMod<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MatMiniFabAnchor,
  <PERSON><PERSON>iniFabButton
};
//# sourceMappingURL=@angular_material_button.js.map

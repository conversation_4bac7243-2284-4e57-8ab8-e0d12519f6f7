import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, Signal, signal, WritableSignal } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { SvgComponent } from '../../svg/svg.component';
import { CacheService } from '@app/_services/cache.service';
import { attachmentTypes } from '@app/_datas/const.data';
import { AttachmentType } from '@app/_types/generic.type';
import { MapService } from '@app/_services/map.service';

@Component({
  selector: 'app-filter-attachment',
  standalone: true,
  imports: [
    CommonModule,
    MatMenuModule,
    SvgComponent
  ],
  templateUrl: './filter-attachment.component.html',
  styleUrl: './filter-attachment.component.scss'
})

export class FilterAttachmentComponent {

  @Input() signal: WritableSignal<AttachmentType[]> = signal<AttachmentType[]>([]);
  @Input() multiple: boolean = false;
  attachmentTypes: AttachmentType[] = attachmentTypes;
  attachmentMap: Signal<Record<AttachmentType, string>> = this.mapService.attachmentTypeMap;

  @Output() select = new EventEmitter<string[]>();

  constructor(public cc: CacheService, private mapService: MapService) {

  }

  getLabel(): string {
    if (this.signal().length === 0) {
      return this.cc.texts()['screen_common_attachment'];
    } else if (this.signal().length === 1) {
      return this.attachmentMap()[this.signal()[0]];
    } else {
      return this.signal().length + ' ' + this.cc.texts()['screen_common_attachment'];
    }
  }

  onSelected(value: AttachmentType, event: MouseEvent): void {
    if (this.multiple) {
      const currentValue = this.signal() || [];
      if (currentValue.includes(value)) {
        this.signal.set(currentValue.filter((v: string) => v !== value));
      } else {
        this.signal.set([...currentValue, value]);
      };
      this.select.emit();
      event.stopPropagation();
    } else {
      this.select.emit();
      this.signal.set([value]);
    }
  }

}

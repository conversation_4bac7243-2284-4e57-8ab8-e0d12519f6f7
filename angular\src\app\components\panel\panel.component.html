<mat-drawer-container class="app-container">
  <mat-drawer class="sidebar" [opened]="showSidenav" #drawer mode="side">
    <!-- <div class="sidebar-nav-toggle" (click)="toggleSidenav()">
      <app-svg [name]="'close'" class="nav-icon"></app-svg>
    </div> -->
    <div class="brand-logo">
      <app-svg name="mevolveEvolveDaily" class="logo" [color]="cc.theme.color1" style="height: 20px;" height="30px"></app-svg>
      <app-svg name="hamburger" class="hamburger-icon" (click)="toggleSidenav()" role="button" [color]="cc.theme.color1"></app-svg>
    </div>
    <div class="sidebar-nav">
      <div class="sidebar-nav-block" *ngIf="featureStore.activeEntities().length > 0 || vs.type()['showNoteFeature']() || vs.type()['showListFeature']()">
        <h6 class="block-heading">HOME</h6>
        <a class="nav-item" routerLink="/notes" routerLinkActive="active" *ngIf="vs.type()['showNoteFeature']()" #rlaNote="routerLinkActive">
          <app-svg name="note" class="nav-icon" [color]="rlaNote.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['screen_navigationBar_notes'] }}</span>
        </a>
        <a class="nav-item" routerLink="/past" routerLinkActive="active" #rlaPast="routerLinkActive" *ngIf="featureStore.activeEntities().length > 0">
          <app-svg name="leftArrow" class="nav-icon" [color]="rlaPast.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['screen_navigationBar_past'] }}</span>
        </a>
        <a class="nav-item" routerLink="/today" routerLinkActive="active" #rlaToday="routerLinkActive" *ngIf="featureStore.activeEntities().length > 0">
          <app-svg name="radioButton" class="nav-icon" [color]="rlaToday.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['screen_common_today'] }}</span>
        </a>
        <a class="nav-item" routerLink="/future" routerLinkActive="active" #rlaFuture="routerLinkActive" *ngIf="featureStore.activeEntities().length > 0">
          <app-svg name="rightArrow" class="nav-icon" [color]="rlaFuture.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['screen_navigationBar_future'] }}</span>
        </a>
        <a class="nav-item" routerLink="/lists" routerLinkActive="active" *ngIf="vs.type()['showListFeature']()" #rlaList="routerLinkActive">
          <app-svg name="list" class="nav-icon" [color]="rlaList.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['screen_navigationBar_lists'] }}</span>
        </a>
      </div>
      <div class="sidebar-nav-block">
        <h6 class="block-heading">{{ cc.texts()['overlay_hamburgerTray_main'] }}</h6>
        <a class="nav-item flex items-center justify-between" routerLink="/features" routerLinkActive="active" #rlaFeatures="routerLinkActive">
          <div class="flex items-center">
            <app-svg name="feature" class="nav-icon" [color]="rlaFeatures.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
            <span>{{ cc.texts()['overlay_hamburgerTray_features'] }}</span>
          </div>
          <app-svg name="plus" class="nav-icon" [color]="rlaFeatures.isActive ? cc.theme.color12 : cc.theme.color35" style="height: 16px; width: 16px;"></app-svg>
        </a>
        <a class="nav-item" routerLink="/insight" routerLinkActive="active" #rlaInsight="routerLinkActive">
          <app-svg name="insight" class="nav-icon" [color]="rlaInsight.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['overlay_hamburgerTray_featuresInsight'] }}</span>
        </a>
        <a class="nav-item" routerLink="/attachment" routerLinkActive="active" #rlaAttachment="routerLinkActive">
          <app-svg name="attachment" class="nav-icon" [color]="rlaAttachment.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['screen_common_attachment'] }}</span>
        </a>
        <a class="nav-item" routerLink="/trash" routerLinkActive="active" #rlaTrash="routerLinkActive">
          <app-svg name="trash" class="nav-icon" [color]="rlaTrash.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['overlay_hamburgerTray_featuresTrash'] }}</span>
        </a>
      </div>
      <div class="sidebar-nav-block">
        <h6 class="block-heading">{{ cc.texts()['overlay_hamburgerTray_settings'] }}</h6>
        <a class="nav-item" routerLink="/account" routerLinkActive="active" #rlaAccount="routerLinkActive">
          <app-svg name="user" class="nav-icon" [color]="rlaAccount.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['overlay_hamburgerTray_settingsAccount'] }}</span>
        </a>
        <a class="nav-item" routerLink="/app" routerLinkActive="active" #rlaApp="routerLinkActive">
          <app-svg name="setting" class="nav-icon" [color]="rlaApp.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['overlay_hamburgerTray_settingApp'] }}</span>
        </a>
        <a class="nav-item" routerLink="/notification" routerLinkActive="active" #rlaNotification="routerLinkActive">
          <app-svg name="notification" class="nav-icon" [color]="rlaNotification.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['overlay_hamburgerTray_settingsNotification'] }}</span>
        </a>
        <a class="nav-item" routerLink="/subscription" routerLinkActive="active" #rlaSubscription="routerLinkActive">
          <app-svg name="subscription" class="nav-icon" [color]="rlaSubscription.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['screen_common_subscription'] }}</span>
        </a>
      </div>
      <div class="sidebar-nav-block">
        <h6 class="block-heading">{{ cc.texts()['overlay_hamburgerTray_help'] }}</h6>
        <a class="nav-item" routerLink="/chat" routerLinkActive="active" #rlaChat="routerLinkActive">
          <app-svg name="chat" class="nav-icon" [color]="rlaChat.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['overlay_hamburgerTray_supportChat'] }}</span>
        </a>
        <a class="nav-item" routerLink="/feedback" routerLinkActive="active" #rlaFeedback="routerLinkActive">
          <app-svg name="bulb" class="nav-icon" [color]="rlaFeedback.isActive ? cc.theme.color12 : cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['screen_common_feedback'] }}</span>
        </a>
      </div>

      <div class="sidebar-nav-block">
        <a class="nav-item" (click)="logout()" role="button">
          <app-svg name="trash" class="nav-icon" [color]="cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['overlay_logoutThisDeviceConfirmation_logout'] }}</span>
        </a>

        <a class="nav-item" (click)="cc.openLanguageDialog()" role="button">
          <app-svg name="language" class="nav-icon" [color]="cc.theme.color35"></app-svg>
          <span>{{ cc.texts()['screen_common_language'] }}</span>
        </a>
      </div>
    </div>
  </mat-drawer>

  <mat-drawer-content [class.side-nav-opened]="showSidenav">
    <div class="panel-container">
      <router-outlet />
    </div>
  </mat-drawer-content>
</mat-drawer-container>
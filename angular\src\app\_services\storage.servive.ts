import { Injectable } from '@angular/core';
import { dbVersion } from '@app/_configs/db.config';
import { User } from '@app/_interfaces/user.interface';
import { LanguageCode } from '@app/_types/generic.type';

@Injectable({
    providedIn: 'root',
})

export class StorageService {

    constructor() {

    }

    public setUser(data: User): void {
        localStorage.setItem('user', JSON.stringify(data));
    }

    public getUser(): User | null {
        const user = localStorage.getItem('user');
        if (!user) return null;
        return JSON.parse(user);
    }

    public setUid(id: string): void {
        localStorage.setItem('uid', id);
    }

    public getUid(): string {
        return localStorage.getItem('uid') as string;
    }

    public setHashedEmail(hashedEmail: string): void {
        localStorage.setItem('hashedEmail', hashedEmail);
    }

    public getHashedEmail(): string {
        return localStorage.getItem('hashedEmail') as string;
    }

    public setDbVersion(version: number): void {
        localStorage.setItem('dbVersion', version.toString());
    }

    public getDbVersion(): number {
        const localDbVersion = localStorage.getItem('dbVersion');
        return localDbVersion ? Number(localDbVersion) : dbVersion;
    }

    public setResynced(isResync: boolean): void {
        localStorage.setItem('resynced', JSON.stringify(isResync));
    }

    public isResynced(): any {
        return JSON.parse(localStorage.getItem('resynced') || 'false');
    }

    public setSessionId(sessionId: string): void {
        localStorage.setItem('sessionId', sessionId);
    }

    public getSessionId(): string {
        return localStorage.getItem('sessionId') || '';
    }

    public setLanguageCode(type: string): void {
        localStorage.setItem('languageCode', type);
    }

    public getLanguageCode(): LanguageCode {
        return localStorage.getItem('languageCode') as LanguageCode;
    }

    // Analytics datas

    public setDeviceId(id: string): void {
        localStorage.setItem('deviceId', id);
    }

    public getDeviceId(): string {
        return localStorage.getItem('deviceId') as string;
    }

    public setDeviceType(type: string): void {
        localStorage.setItem('deviceType', type);
    }

    public getDeviceType(): string {
        return localStorage.getItem('deviceType') as string;
    }

    public setDeviceOs(type: string): void {
        localStorage.setItem('deviceOs', type);
    }

    public getDeviceOs(): string {
        return localStorage.getItem('deviceOs') as string;
    }

    public setBrowser(type: string): void {
        localStorage.setItem('browser', type);
    }

    public getBrowser(): string {
        return localStorage.getItem('browser') as string;
    }

    public setCountryCode(code: string): void {
        localStorage.setItem('countryCode', code);
    }

    public getCountryCode(): string {
        return localStorage.getItem('countryCode') as string;
    }

    public setUtmSource(id: string): void {
        localStorage.setItem('utmSource', id);
    }

    public getUtmSource(): string {
        return localStorage.getItem('utmSource') as string;
    }

    public setUtmCampaign(id: string): void {
        localStorage.setItem('utmCampaign', id);
    }

    public getUtmCampaign(): string {
        return localStorage.getItem('utmCampaign') as string;
    }

    public setUtmMedium(id: string): void {
        localStorage.setItem('utmMedium', id);
    }

    public getUtmMedium(): string {
        return localStorage.getItem('utmMedium') as string;
    }

    public setUtmContent(id: string): void {
        localStorage.setItem('utmContent', id);
    }

    public getUtmContent(): string {
        return localStorage.getItem('utmContent') as string;
    }

    public setDesignerId(id: string): void {
        localStorage.setItem('designerId', id);
    }

    public getDesignerId(): string {
        return localStorage.getItem('designerId') as string;
    }

    public setReferrerId(id: string): void {
        localStorage.setItem('referrerId', id);
    }

    public getReferrerId(): string {
        return localStorage.getItem('referrerId') as string;
    }

    public setLastUtmSource(id: string): void {
        sessionStorage.setItem('lastUtmSource', id);
    }

    public getLastUtmSource(): string {
        return sessionStorage.getItem('lastUtmSource') as string;
    }

    public setLastUtmCampaign(id: string): void {
        sessionStorage.setItem('lastUtmCampaign', id);
    }

    public getLastUtmCampaign(): string {
        return sessionStorage.getItem('lastUtmCampaign') as string;
    }

    public setLastUtmMedium(id: string): void {
        sessionStorage.setItem('lastUtmMedium', id);
    }

    public getLastUtmMedium(): string {
        return sessionStorage.getItem('lastUtmMedium') as string;
    }

    public setLastUtmContent(id: string): void {
        sessionStorage.setItem('lastUtmContent', id);
    }

    public getLastUtmContent(): string {
        return sessionStorage.getItem('lastUtmContent') as string;
    }

    public setLastDesignerId(id: string): void {
        sessionStorage.setItem('lastDesignerId', id);
    }

    public getLastDesignerId(): string {
        return sessionStorage.getItem('lastDesignerId') as string;
    }

    public setLastReferrerId(id: string): void {
        sessionStorage.setItem('lastReferrerId', id);
    }

    public getLastReferrerId(): string {
        return sessionStorage.getItem('lastReferrerId') as string;
    }

    // Save all the secret to local storage.
    public saveSecretToLocalStorage(key: string, uid: string, secret: string) {
        localStorage.setItem(key, `${uid}___${secret}`);
    }

    // Get all the secret from local storage
    public getSecretFromLocal(key: string, uid: string): string | null {
        try {
            const secret = localStorage.getItem(key);
            if (secret) {
                const parts = secret.split('___');
                if (parts[0] === uid) {
                    return parts[1];
                }
            }
        } catch (error) {
            console.error('Error in getting secret key:', error);
            return null;
        }
        return null;
    }

    public clearLocalStore(): void {
        localStorage.clear();
        sessionStorage.clear();
    }
}

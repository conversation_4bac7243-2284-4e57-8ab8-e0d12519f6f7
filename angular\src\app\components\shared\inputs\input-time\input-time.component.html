<div class="flex items-start w-full" [ngClass]="{'readonly': readonly}">
    <app-svg name="clock" [color]="cc.theme.color35" class="ri-pe-4" *ngIf="!hideIcon"></app-svg>
    <div class="me-input-time flex items-center justify-between w-full">
        <div class="" role="button" (click)="openTimePicker()">
            <p class="text-14-400 mb-0" [ngClass]="control.value.timeString && control.value.timeString !== '00:00' ? 'color-8' : 'color-7'">{{ control.value.timeString | parseTime }}</p>
            <span class="text-12-400 color-7" *ngIf="control.value.timeString && control.value.timeString !== '00:00'">{{ timeTypeMap()[isTmzAffected.value ? 'true' : 'false'] }}</span>
        </div>
        <app-input-duration [control]="duration" *ngIf="control.value.timeString && control.value.timeString !== '00:00'" [maxHour]="100"></app-input-duration>
    </div>
</div>
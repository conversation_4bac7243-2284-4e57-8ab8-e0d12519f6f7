<p class="ri-p-4 text-16-500 color-8 text-center mb-0">{{ cc.texts()['overlay_repeatMonthPicker_title'] }}</p>
<div class="ri-p-4 input-month-block">
    <span class="text-16-500 color-8 month-value" [ngClass]="{'selected' : selectedMonths.includes(month[0])}" *ngFor="let month of monthEntries()" role="button" (click)="selectMonth(month[0])"><span class="month-badge">{{ month[1] }}</span></span>
</div>
<div class="bottom-section flex align-items-end justify-end ri-p-4 ri-bt-2">
    <button class="btn-text text-16-500 color-7 me-4" (click)="closeDialog()">{{ cc.texts()['screen_common_buttonCancel'] }}</button>
    <button type="submit" class="btn-text text-16-500 check-disabled color-35" [disabled]="!hasChanges()" (click)="save()">{{ cc.texts()['screen_common_save'] }}</button>
</div>
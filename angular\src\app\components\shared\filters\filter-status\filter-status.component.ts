import { CommonModule } from '@angular/common';
import { Component, Input, Signal, signal } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { SvgComponent } from '../../svg/svg.component';
import { CacheService } from '@app/_services/cache.service';
import { EntityStatus } from '@app/_types/generic.type';
import { entityStatus } from '@app/_datas/const.data';
import { MapService } from '@app/_services/map.service';

@Component({
  selector: 'app-filter-status',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    MatMenuModule
  ],
  templateUrl: './filter-status.component.html',
  styleUrl: './filter-status.component.scss'
})

export class FilterStatusComponent {

  @Input() value = signal<EntityStatus>('all');
  @Input() multiple: boolean = false;
  @Input() label: string = '';
  @Input() hiddenValues: EntityStatus[] = [];
  entityFilterStatus: EntityStatus[] = entityStatus;
  entityStatusMap: Signal<Record<EntityStatus, string>> = this.mapService.entityStatusMap;

  constructor(public cc: CacheService, private mapService: MapService) {

  }

  onStatusSelected(value: EntityStatus, event: MouseEvent): void {
    this.value.set(value);
  }
}

import { Pipe, PipeTransform } from '@angular/core';
import { CacheService } from '@app/_services/cache.service';

@Pipe({ 
    name: 'relativeDate',
    standalone: true 
})

export class RelativeDatePipe implements PipeTransform {

  constructor(private cc: CacheService) { }

  transform(value: Date | string): string {
    const date = new Date(value);
    const today = new Date();
    const input = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const now = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    const diff = Math.round((input.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    if (diff === 0) return this.cc.texts()['screen_common_today'] as string;
    if (diff === 1) return this.cc.texts()['tab_todayToday_tomorrow'] as string;
    if (diff === -1) return this.cc.texts()['tab_todayToday_yesterday'] as string;
    if (diff > 1) return this.cc.interpolateText('screen_today_swipeFutureRelatedDate', { days: diff.toString() });
    return this.cc.interpolateText('screen_trash_durationInfoDays', { durationCount: Math.abs(diff).toString() });
  }
}
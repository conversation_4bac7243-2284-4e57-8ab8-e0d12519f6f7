<div class="filter-block" [ngClass]="{'h-auto filtered': filter}" dragScroll>
    <app-filter-search [signal]="filterSearch" [name]="'habit'" class="me-2"></app-filter-search>
    <app-filter-date-range [startDate]="startDate" [endDate]="endDate" class="me-2"></app-filter-date-range>
    <button class="btn btn-filter btn-toggle" (click)="clearFilter()" *ngIf="isFiltered()">{{ cc.texts()['screen_common_clearAll'] }}</button>
</div>

<div class="calendar-tab-block" [ngClass]="{'filter-applied': filter}">
  <cdk-virtual-scroll-viewport itemSize="15" class="viewport" #viewport autoSize (scrolledIndexChange)="onScrolledIndexChange($event)" *ngIf="isDataAvailable">
  <ng-container *cdkVirtualFor="let group of calendarEvents(); let i = index;">
    <ng-container *ngIf="group.data.length > 0">
        <div class="group-title text-16-400 ri-p-4 ri-bb-2" #tgroupHead role="button" [attr.isOpen]="getIsOpen()"
            (click)="toggleChildGroup(tgroupHead,'calendarEntityGroup' + i)" *ngIf="group.data.length !== 0 || (group.data.length === 0 && show.includes('emptyDays'))">
            <span [ngClass]="group.data.length === 0 ? 'color-4' : 'color-1'">{{ cc.getFormattedDate(group.name, 'd MMM y, EEE') }}</span>
            <span class="group-count" *ngIf="groupedViewType.includes('showCounts') && group.data.length > 0">{{ group.data.length }}</span>
        </div>

        <div class="row-grouped" [attr.id]="'calendarEntityGroup' + i" [attr.isGroupOpened]="getIsOpen()" *ngIf="group.data.length !== 0 || (group.data.length === 0 && show.includes('emptyDays'))">
            <app-calendar-event-block class="ri-bb-2 entity-block"  *ngFor="let entity of group.data; let i = index; trackBy: trackByForEntity;" [blockClass]="'ri-px-4 ri-py-3'" [show]="show" [dateString]="group.id" [entity]="entity" [descriptionType]="descriptionType" [isLabel]="false"></app-calendar-event-block>
        </div>
    </ng-container>
  </ng-container>
 </cdk-virtual-scroll-viewport>
  <div class="no-result-block h-full flex items-center justify-center flex-col" *ngIf="!isDataAvailable">
    <app-svg name="calendarPlaceholder" [color]="cc.theme.color1"></app-svg>
    <p class="text-16-400 color-8 mb-0 ri-pt-4">{{ cc.texts()['screen_common_calendarEmptyScreenContent'] }}</p>
  </div>
</div>
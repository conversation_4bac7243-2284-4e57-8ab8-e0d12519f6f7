import { CommonModule } from '@angular/common';
import { Component, computed, Inject, Signal } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AlertService } from '@app/_services/alert.service';
import { Subject, takeUntil } from 'rxjs';
import * as _ from 'lodash';
import { MapService } from '@app/_services/map.service';
import { CacheService } from '@app/_services/cache.service';

@Component({
  selector: 'app-input-month-select',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './input-month-select.component.html',
  styleUrl: './input-month-select.component.scss'
})

export class InputMonthSelectComponent {

  unSubscribe = new Subject<void>();
  initialMonths: string[] = [];
  selectedMonths: string[] = [];
  min: number = 0;
  monthMap: Signal<Record<string, string>> = this.mapService.monthMap;
  monthEntries: Signal<[string, string][]> = computed(() => {
    return Object.entries(this.monthMap());
  });

  constructor(
    public dialogRef: MatDialogRef<InputMonthSelectComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { value: string[], min: number },
    private alertService: AlertService,
    private mapService: MapService,
    public cc: CacheService
  ) {

    this.initialMonths = this.data.value;
    this.selectedMonths = this.data.value;
    this.min = this.data.min ? this.data.min : this.min;

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  selectMonth(selectedMonth: string) {
    const selectedValues = this.selectedMonths;
    const updatedValues = selectedValues.includes(selectedMonth) ? (selectedValues.length === this.data.min ? selectedValues : selectedValues.filter((date: string) => date !== selectedMonth)) : [...selectedValues, selectedMonth];
    this.selectedMonths = updatedValues;
  }

  hasChanges(): boolean {
    const initial = _.cloneDeep(this.initialMonths);
    const current = _.cloneDeep(this.selectedMonths);
    if (initial?.length !== current?.length) return true;
    initial.sort((a: string, b: string) => (a > b ? 1 : -1));
    current.sort((a: string, b: string) => (a > b ? 1 : -1));
    return !_.isEqual(initial, current);
  }

  save() {
    this.dialogRef.close([this.selectedMonths]);
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm('Save Changes?', 'You have some unsaved changes', 'SAVE', 'DISCARD', 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
        this.dialogRef.close();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }
}

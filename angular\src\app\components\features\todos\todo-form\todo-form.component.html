<div class="top-section">
    <h6 class="heading mb-0">{{ cc.texts()['screen_common_todo'] }} <span class="text-12-400 ri-ps-2" *ngIf="data.mode === 'edit'">{{ cc.getFormattedDate(data.dateString, 'd MMM y, EEE') }}</span></h6>
    <div class="flex">
        <app-svg name="more" class="more-icon ri-me-6" [matMenuTriggerFor]="infoMenu" role="button"
            *ngIf="data.mode === 'edit'" [color]="cc.theme.color12"></app-svg>
        <mat-menu #infoMenu="matMenu" class="me-menu info-menu ri-w-350px">
            <button mat-menu-item class="text-center text-14-400 color-8" (click)="addCollaborator()"><span class="w-full text-center">{{ cc.texts()['screen_common_dropdownCollaboration'] }}</span></button>
            <button mat-menu-item (click)="deleteTodo()"><span class="text-center text-14-400 color-11 w-full">{{ cc.texts()['screen_common_delete'] }}</span></button>
        </mat-menu>

        <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
    </div>
</div>
<div class="todo-setup-form ri-p-4">
    <form [formGroup]="todoForm" #jSetupForm="ngForm">
        <div class="flex justify-between items-center ri-pb-3">
            <app-input-text [control]="getFc('title')" name="meTodoTitle" [placeholder]="cc.texts()['screen_common_titlePlaceholder']" maxLength="120" [submitted]="submitted"></app-input-text>
            <app-input-checkmark-advanced [inputId]="todoInitial.id" [checked]="actionMap()[data.dateString] && actionMap()[data.dateString].isSkipped === false" [canceled]="actionMap()[data.dateString] && actionMap()[data.dateString].isSkipped === true" (change)="onActionChange()"></app-input-checkmark-advanced>
        </div>
        <app-input-checklist *ngIf="todoForm.value.checkLists && todoForm.value.checkLists.length > 0" class="ri-pb-3" [control]="checkLists" name="meTodoChecklist" [placeholder]="cc.texts()['bottomSheet_todoAction_checklist']"></app-input-checklist>
        <app-input-text class="ri-pb-3" *ngIf="isDescription" [control]="getFc('description')" name="meTodoDescription" [placeholder]="cc.texts()['screen_common_description']" maxLength="500" icon="description"></app-input-text>
        <app-input-date-period class="ri-pb-3" name="meTodoDateRange" [startControl]="getFc('startAt')" [endControl]="getFc('endAt')" [repeatControl]="getFc('repeat')"></app-input-date-period>
        <app-input-time class="ri-pb-3" [control]="getFc('startAt')" [isTmzAffected]="getFc('isTmzAffected')"
            [isStartTimeSet]="getFc('isStartTimeSet')" [duration]="getFc('duration')"
            [reminder]="getFc('reminderAt')"></app-input-time>
        <app-input-reminder [control]="getFc('reminderAt')" *ngIf="todoForm.value.isStartTimeSet"></app-input-reminder>
        <!-- <pre class="color-8">{{ todoForm.value | json }}</pre>
        <pre class="color-8">{{ todoInitial | json }}</pre> -->
    </form>

    <div class="todo-setup-addons">
        <app-input-hashtag class="ri-pt-3" *ngIf="todoForm.value.tags && todoForm.value.tags.length > 0"
            [control]="getFc('tags')"></app-input-hashtag>
        <app-attachment-list class="ri-pt-3" *ngIf="attachmentValues && attachmentValues.length > 0"
            [attachments]="attachmentValues" [basePath]="basePath" [entity]="data.value" [title]="data.value.title"
            (deleteEvent)="deleteAttachments($event)"></app-attachment-list>
    </div>
</div>
<div class="bottom-section flex items-center justify-between ri-p-4 text-end ri-bt-2">
    <div class="flex items-center">
        <app-svg *ngIf="!todoForm.value.checkLists || todoForm.value.checkLists.length === 0" name="checklist" [color]="cc.theme.color35" class="ri-pe-6" role="button" (click)="addChecklist()"></app-svg>
        <app-svg *ngIf="!isDescription" name="description" [color]="cc.theme.color35" class="ri-pe-6" role="button"
            (click)="isDescription = true"></app-svg>
        <app-svg *ngIf="!todoForm.value.tags || todoForm.value.tags.length === 0" name="hashtag" [color]="cc.theme.color35"
            class="ri-pe-6" role="button" (click)="openHashtagsDialog()"></app-svg>
        <app-input-file [newAttachments]="newAttachments" [multiple]="true"></app-input-file>
    </div>
    <button type="submit" class="btn-text text-16-500 color-1" (click)="hasChanges() ? save() : closeDialog()">{{ hasChanges() ? cc.texts()['screen_common_save'] : cc.texts()['screen_common_close'] }}</button>
</div>
import { CommonModule } from '@angular/common';
import { Component, inject, Signal } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { FeatureSetup } from '@app/_interfaces/feature.interface';
import { FeatureStore } from '@app/_stores';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { JournalSetupFormComponent } from '../../journals/journal-setup-form/journal-setup-form.component';
import { MoneyTrackerSetupFormComponent } from '../../money-tracker/money-tracker-setup-form/money-tracker-setup-form.component';
import { CalendarConnectComponent } from '../../calendar-integration/calendar-connect/calendar-connect.component';
import { HabitSetupFormComponent } from '../../habits/habit-setup-form/habit-setup-form.component';
import { CacheService } from '@app/_services/cache.service';
import { MapService } from '@app/_services/map.service';
import { FeatureNameType } from '@app/_types/generic.type';

@Component({
  selector: 'app-feature-list',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent
  ],
  templateUrl: './feature-list.component.html',
  styleUrl: './feature-list.component.scss'
})

export class FeatureListComponent {

  readonly featureStore = inject(FeatureStore);
  featureName: Signal<Record<FeatureNameType, string>> = this.mapService.featureNameMap;

  constructor(
    public dialogRef: MatDialogRef<FeatureListComponent>,
    public dialog: MatDialog,
    public cc: CacheService,
    private mapService: MapService
  ) {

  }

  addFeature(feature: FeatureSetup) {
    switch (feature.name) {
      case 'boolean':
      case 'timer':
      case 'numeric':
      case 'single':
      case 'multiple':
        const setupHabitDialog = this.dialog.open(HabitSetupFormComponent, {
          width: '100%',
          maxWidth: '750px',
          maxHeight: '90vh',
          minHeight: '90vh',
          disableClose: true,
          data: {
            mode: 'new',
            value: null,
            habitType: feature.name,
          },
        });
        break;
      case 'journal':
        const setupDialog = this.dialog.open(JournalSetupFormComponent, {
          width: '100%',
          maxWidth: '750px',
          maxHeight: '90vh',
          minHeight: '90vh',
          disableClose: true,
          data: {
            mode: 'new',
            value: null,
          },
        });
        break;
      case 'moneyTracker':
        const setupMoneyDialog = this.dialog.open(MoneyTrackerSetupFormComponent, {
          width: '100%',
          maxWidth: '750px',
          maxHeight: '90vh',
          minHeight: '90vh',
          disableClose: true,
          data: {
            mode: 'new',
            value: null,
          },
        });
        break;
      case 'google':
      case 'microsoft':
        const setupCalendarDialog = this.dialog.open(CalendarConnectComponent, {
          width: '100%',
          maxWidth: '500px',
          height: 'auto',
          disableClose: true,
          data: {
            type: feature.name
          },
        });
        break;
    
      default:
        break;
    }

  }

  async closeDialog() {
    this.dialogRef.close();
  }

}

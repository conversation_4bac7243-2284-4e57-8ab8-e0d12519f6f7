import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { HabitNumericAnswer } from '@app/_interfaces/habit.interface';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';
import { getNumStrId } from '@app/_utils/utils';
import { InputNumberComponent } from '@app/components/shared/inputs/input-number/input-number.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-habit-numeric-value',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    InputNumberComponent
  ],
  templateUrl: './habit-numeric-value.component.html',
  styleUrl: './habit-numeric-value.component.scss'
})

export class HabitNumericValueComponent {

  numericControl: FormControl = new FormControl(null);
  timestamp: FormControl = new FormControl(new Date());
  unSubscribe = new Subject<void>();

  constructor(
    public dialogRef: MatDialogRef<HabitNumericValueComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'new' | 'edit', value: HabitNumericAnswer | null, unit: string },
    public cc: CacheService,
    private alertService: AlertService
  ) {
    if (this.data.value) {
      this.numericControl.setValue(this.data.value.value ? String(this.data.value.value) : null);
      this.timestamp.setValue(this.data.value.timestamp);
    };

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  hasChanges(): boolean {
    return this.numericControl.value !== this.data.value?.value || this.timestamp.value !== this.data.value?.timestamp;
  }

  save() {
    const numericAnswer: HabitNumericAnswer = {
      id: this.data.value ? this.data.value.id : getNumStrId(),
      value: Number(this.numericControl.value),
      timestamp: this.timestamp.value
    };
    this.dialogRef.close({ type: this.data.mode === 'new' ? 'ADD' : 'EDIT', value: numericAnswer });
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }
}

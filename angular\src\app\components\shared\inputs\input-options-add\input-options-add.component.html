<div class="top-section">
  <h6 class="heading mb-0">{{ cc.texts()['screen_habitSetupAdd_optionAdd'] }}</h6>
  <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
</div>
<div class="flex items-center justify-between ri-p-4">
    <app-input-text [control]="control" name="meUserGoal" [placeholder]="cc.texts()['bottomSheet_optionAdd_placeholder']" [hideIcon]="true" [maxLength]="maxLength" class="w-full"></app-input-text>
    <app-svg class="ri-ms-4" name="plus" [color]="!hasChanges() || control.invalid ? cc.theme.color10 : cc.theme.color35" (click)="hasChanges() && !control.invalid && addOption()" role="button"></app-svg>
</div>
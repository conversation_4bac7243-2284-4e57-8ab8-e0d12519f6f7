import { CommonModule } from '@angular/common';
import { Component, Input, Signal } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { InputTimePickerComponent, InputTimePickerModel } from '../input-time-picker/input-time-picker.component';
import { FormControl } from '@angular/forms';
import { SvgComponent } from '../../svg/svg.component';
import { getCustomDate } from '@app/_utils/utils';
import { ParseTimePipe } from '@app/_pipes/parse-time.pipe';
import { InputDurationComponent } from '../input-duration/input-duration.component';
import { CacheService } from '@app/_services/cache.service';
import { MapService } from '@app/_services/map.service';

@Component({
  selector: 'app-input-time',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    ParseTimePipe,
    InputDurationComponent
  ],
  templateUrl: './input-time.component.html',
  styleUrl: './input-time.component.scss'
})

export class InputTimeComponent {

  @Input() control: FormControl = new FormControl(getCustomDate());
  @Input() hideIcon: boolean = false;
  @Input() isTmzAffected: FormControl = new FormControl(false);
  @Input() isStartTimeSet: FormControl = new FormControl(false);
  @Input() duration: FormControl = new FormControl(15);
  @Input() reminder: FormControl = new FormControl([]);
  timeTypeMap: Signal<Record<string, string>> = this.mapService.timeTypeMap;
  @Input() readonly: boolean = false;

  constructor(private dialog: MatDialog, public cc: CacheService, private mapService: MapService) {

  }

  openTimePicker() {
    const dialogRef = this.dialog.open(InputTimePickerComponent, {
      width: '400px',
      minWidth: "300px",
      disableClose: true,
      data: new InputTimePickerModel(this.control.value, this.isTmzAffected.value)
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.control.setValue(result.customDate);
        this.isTmzAffected.setValue(result.isTmzAffected);
        this.isStartTimeSet.setValue(result.type !== 'DELETE' ? true : false);
        this.reminder.setValue(result.type === 'ADD' ? [-10] : result.type === 'DELETE' ? [] : this.reminder.value);
      }
    });
  }
}

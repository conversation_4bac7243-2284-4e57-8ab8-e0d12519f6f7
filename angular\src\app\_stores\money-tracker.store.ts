import { signalStore, withComputed, withState, withMethods, withHooks, patchState } from "@ngrx/signals";
import { computed, inject } from "@angular/core";
import { UserStore } from "./user.store";
import { DependencyService } from "@app/_services/dependency.service";
import { UtilsService } from "@app/_services/utils.service";
import { IndexDbService } from "@app/_services/index-db.service";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { MoneyTransaction, MoneyTrackerSetup } from "@app/_interfaces/money-tracker.interface";
import { CryptographyService } from "@app/_services/cryptography.service";
import { FirebaseFunctionService } from "@app/_services/firebase-function.service";
import { dbVersion } from "@app/_configs/db.config";
import { FirestoreCollection } from "@app/_enums/firestore-collection.enum";
import { firstValueFrom } from "rxjs";
import { CalendarEntitySetup, EntitySetup, FeatureSetup } from "@app/_interfaces/feature.interface";
import { getCustomDate, getDateString } from "@app/_utils/utils";

type MoneyTrackerState = {
    transactions: MoneyTransaction[];
    setups: MoneyTrackerSetup[];
    activeSetupIds: string[];
    isLoading: boolean;
    filter: { date: Date };
    selectedMoneyTrackerSetupIds: string[];
    selectedMoneyTransactionIds: string[];
};

const initialState: MoneyTrackerState = {
    transactions: [],
    setups: [],
    activeSetupIds: [],
    isLoading: false,
    filter: { date: new Date() },
    selectedMoneyTrackerSetupIds: [],
    selectedMoneyTransactionIds: []
};

export const MoneyTrackerStore = signalStore(
    { providedIn: 'root' },
    withState(initialState),

    withComputed((
        store,
        userStore = inject(UserStore),
        ds = inject(DependencyService)
    ) => ({
        firstDateString: computed<string>(() => {
            let todayDateString = getDateString();
            const transactions = store.transactions();
            if (!transactions || transactions.length === 0) {
                return todayDateString;
            }

            return transactions.reduce(
                (acc, setup) => {
                    if (setup.deletedAt || !setup.transactionDate) return acc;
                    const dateStr = setup.transactionDate.dateString;

                    if (!acc || (acc && dateStr < acc)) {
                        acc = dateStr;
                    }

                    return acc;
                },
                todayDateString
            );
        }),
        lastDateString: computed<string>(() => {
            let todayDateString = getDateString();
            const transactions = store.transactions();
            if (!transactions || transactions.length === 0) {
                return todayDateString;
            }

            return transactions.reduce(
                (acc, setup) => {
                    if (setup.deletedAt || !setup.transactionDate) return acc;
                    const dateStr = setup.transactionDate.dateString;

                    if (!acc || (acc && dateStr > acc)) {
                        acc = dateStr;
                    }

                    return acc;
                },
                todayDateString
            );
        }),
        selectedSetups: computed(() => {
            const ids = store.selectedMoneyTrackerSetupIds();
            return new Map(
                [...ids].map(id => [id, store.setups().find(setup => setup.id === id) || null])
            );
        }),
        idToSetup: computed<Record<string, MoneyTrackerSetup>>(() => {
            const idToSetup: Record<string, MoneyTrackerSetup> = {};
            store.setups().forEach(setup => {
                idToSetup[setup.id] = setup;
            });
            return idToSetup;
        }),
        idToTransaction: computed<Record<string, MoneyTransaction>>(() => {
            const idToTransaction: Record<string, MoneyTransaction> = {};
            store.transactions().forEach(transaction => {
                idToTransaction[transaction.id] = transaction;
            });
            return idToTransaction;
        }),
        activeSetups: computed<MoneyTrackerSetup[]>(() => {
            return store.setups().filter(setup => !setup.deletedAt);
        }),
        enabledSetups: computed<MoneyTrackerSetup[]>(() => {
            return store.setups().filter(setup => !setup.deletedAt && !setup.isPaused);
        }),
        activeTransactions: computed<MoneyTransaction[]>(() => {
            return store.transactions().filter(transaction => !transaction.deletedAt && store.activeSetupIds().includes(transaction.setupId));
        }),
    })),
    withMethods((
        store,
        userStore = inject(UserStore),
        utilsService = inject(UtilsService),
        cryptoService = inject(CryptographyService),
        firebaseFunctionService = inject(FirebaseFunctionService),
        idbService = inject(IndexDbService)
    ) => ({
        getNewMoneySetup: (): MoneyTrackerSetup => {
            const user = userStore.user?.();
            return {
                id: utilsService.getNewId(),
                title: '',
                currency: '₹',
                isPaused: false,
                tags: [],
                members: { memberHashedEmails: [], membersConfig: {} },
                uid: user?.uid ?? '',
                ownerName: user?.name,
                ownerEmail: user?.email,
                docVer: dbVersion,
                docCollection: FirestoreCollection.MoneyTrackerSetups,
                encData: {
                    dek: cryptoService.createEncryptedDocKey(),
                    encFields: [
                        'title',
                        'ownerName',
                        'ownerEmail',
                        'members.membersConfig{}.eEmail',
                    ]
                },
                source: 'client',
                sessionId: utilsService.getNewId(),
                createdAt: new Date(),
                localUpdatedAt: new Date(),
                cloudUpdatedAt: new Date(),
                lastUpdatedAt: new Date(),
                permaDeletedAt: null,
                deletedAt: null,
            }
        },
        getNewTransaction: (): MoneyTransaction => {
            const user = userStore.user?.();
            return {
                id: utilsService.getNewId(),
                title: '',
                description: '',
                amount: '',
                transactionDate: getCustomDate(),
                transactionType: 'expense',
                tags: [],
                attachments: [],
                setupId: '',
                ownerName: user?.name,
                ownerEmail: user?.email,
                uid: user?.uid ?? '',
                docVer: dbVersion,
                docCollection: FirestoreCollection.MoneyTrackerTransactions,
                encData: {
                    dek: cryptoService.createEncryptedDocKey(),
                    encFields: [
                        'title',
                        'description',
                        'ownerName',
                        'ownerEmail',
                        'lastUpdatedBy',
                        'members.membersConfig{}.eEmail',
                    ]
                },
                source: 'client',
                sessionId: utilsService.getNewId(),
                createdAt: new Date(),
                localUpdatedAt: new Date(),
                cloudUpdatedAt: new Date(),
                lastUpdatedAt: new Date(),
                permaDeletedAt: null,
                deletedAt: null,
            }
        },

        addMoneySetup: async (moneySetup: MoneyTrackerSetup) => {
            const setup: MoneyTrackerSetup = moneySetup;
            await idbService.add('moneyTrackerSetups', moneySetup);

            // Upload data to the backend
            const syncRequest = cryptoService.prepareRawData({ ...setup });
            await firebaseFunctionService.uploadData(syncRequest);
        },
        updateMoneySetups: async (moneySetups: MoneyTrackerSetup[]) => {
            const oldmoneySetups = [];
            const newmoneySetups = [];
            for (const moneySetup of moneySetups) {
                const oldmoneySetup = await firstValueFrom(idbService.getEntityById('moneyTrackerSetups', moneySetup.id));
                const newmoneySetup = await idbService.update('moneyTrackerSetups', moneySetup.id, moneySetup);
                oldmoneySetup!.localUpdatedAt = new Date();
                newmoneySetup!.cloudUpdatedAt = null;
                newmoneySetup!.lastUpdatedAt = new Date();
                oldmoneySetups.push(oldmoneySetup);
                newmoneySetups.push(newmoneySetup);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldmoneySetups,
                newmoneySetups,
                FirestoreCollection.MoneyTrackerSetups
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },
        deleteMoneySetups: async (moneySetups: MoneyTrackerSetup[]) => {
            const oldmoneySetups = [];
            const newmoneySetups = [];
            for (const moneySetup of moneySetups) {
                moneySetup.deletedAt = new Date();
                const oldmoneySetup = await firstValueFrom(idbService.getEntityById('moneyTrackerSetups', moneySetup.id));
                const newmoneySetup = await idbService.update('moneyTrackerSetups', moneySetup.id, moneySetup);
                oldmoneySetup!.localUpdatedAt = new Date();
                newmoneySetup!.cloudUpdatedAt = null;
                oldmoneySetups.push(oldmoneySetup);
                newmoneySetups.push(newmoneySetup);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldmoneySetups,
                newmoneySetups,
                FirestoreCollection.MoneyTrackerSetups
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },

        addMoneyTransaction: async (moneyTransaction: MoneyTransaction) => {
            const transaction: MoneyTransaction = moneyTransaction;
            await idbService.add('moneyTrackerTransactions', moneyTransaction);

            // Upload data to the backend
            const syncRequest = cryptoService.prepareRawData({ ...transaction });
            await firebaseFunctionService.uploadData(syncRequest);
        },
        updateMoneyTransactions: async (moneyTransactions: MoneyTransaction[]) => {
            const oldmoneyTransactions = [];
            const newmoneyTransactions = [];
            for (const moneyTransaction of moneyTransactions) {
                const oldmoneyTransaction = await firstValueFrom(idbService.getEntityById('moneyTrackerTransactions', moneyTransaction.id));
                const newmoneyTransaction = await idbService.update('moneyTrackerTransactions', moneyTransaction.id, moneyTransaction);
                oldmoneyTransaction!.localUpdatedAt = new Date();
                newmoneyTransaction!.cloudUpdatedAt = null;
                newmoneyTransaction!.lastUpdatedAt = new Date();
                oldmoneyTransactions.push(oldmoneyTransaction);
                newmoneyTransactions.push(newmoneyTransaction);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldmoneyTransactions,
                newmoneyTransactions,
                FirestoreCollection.MoneyTrackerTransactions
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },
        deleteMoneyTransactions: async (moneyTransactions: MoneyTransaction[]) => {
            const oldmoneyTransactions = [];
            const newmoneyTransactions = [];
            for (const moneyTransaction of moneyTransactions) {
                moneyTransaction.deletedAt = new Date();
                const oldmoneyTransaction = await firstValueFrom(idbService.getEntityById('moneyTrackerTransactions', moneyTransaction.id));
                const newmoneyTransaction = await idbService.update('moneyTrackerTransactions', moneyTransaction.id, moneyTransaction);
                oldmoneyTransaction!.localUpdatedAt = new Date();
                newmoneyTransaction!.cloudUpdatedAt = null;
                oldmoneyTransactions.push(oldmoneyTransaction);
                newmoneyTransactions.push(newmoneyTransaction);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldmoneyTransactions,
                newmoneyTransactions,
                FirestoreCollection.MoneyTrackerTransactions
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },

        getComputedEntities: (dateString: string | null = null, groupBy: 'date' | 'hashtag' | 'transactionType' | 'setup' = 'date'): { withTime: EntitySetup[], withoutTime: EntitySetup[], calenderEvents: CalendarEntitySetup[], groupedData: { [key in string]: EntitySetup[] } } => {
            const entitiesWithTime: EntitySetup[] = [];
            const entitiesWithoutTime: EntitySetup[] = [];
            const calenderEvents: CalendarEntitySetup[] = [];
            const groupedData: { [key in string]: EntitySetup[] } = {};

            store.transactions().forEach(moneyTransaction => {
                const setup = store.idToSetup()[moneyTransaction.setupId] || null;
                if (!setup || setup.isPaused) return;
                const activeTransction = (dateString ? moneyTransaction.transactionDate.dateString === dateString : true) && !moneyTransaction.deletedAt && !setup.deletedAt;
                if (!activeTransction) return;
                const transactionDateString = moneyTransaction.transactionDate.dateString;

                const clonedEntity = { ...moneyTransaction };
                const entitySetup: EntitySetup = {
                    id: clonedEntity.id,
                    entityName: 'moneyTracker',
                    startAt: clonedEntity.transactionDate,
                    endAt: null,
                    title: clonedEntity.title,
                    duration: 0,
                    invalid: false,
                    status: 'all',
                    tags: clonedEntity.tags,
                    repeatType: 'all',
                    percentage: 0
                }
                entitiesWithoutTime.push(entitySetup);

                switch (groupBy) {
                    case 'date':
                        (groupedData[transactionDateString] ??= []).push(entitySetup);
                        break;
                    case 'hashtag':
                        const tags = [...entitySetup.tags];
                        if (tags.length === 0) {
                            (groupedData[''] ??= []).push(entitySetup);
                        } else {
                            tags.forEach(tag => {
                                const id = tag || '';
                                (groupedData[id] ??= []).push(entitySetup);
                            });
                        }
                        break;
                    case 'transactionType':
                        const entry = clonedEntity.transactionType;
                        (groupedData[entry] ??= []).push(entitySetup);
                        break;
                    case 'setup':
                        const setup = store.idToSetup()[clonedEntity.setupId];
                        if (setup) {
                            (groupedData[setup.id] ??= []).push(entitySetup);
                        }
                        break;
                }
            });
            return { withTime: entitiesWithTime, withoutTime: entitiesWithoutTime, calenderEvents, groupedData };
        },

        getMoneyTrackerFeature(moneyTrackerSetup: MoneyTrackerSetup): FeatureSetup {
            return {
                id: moneyTrackerSetup.id,
                name: 'moneyTracker',
                entityName: 'moneyTracker',
                icon: 'moneyTracker',
                description: moneyTrackerSetup.title,
                status: 'active',
                disabled: !moneyTrackerSetup.isPaused,
                isDefault: false,
                startAt: null,
                endAt: null,
                entity: moneyTrackerSetup
            };
        },

        selectSetup(id: string) {
            patchState(store, (state) => ({ selectedMoneyTrackerSetupIds: [...state.selectedMoneyTrackerSetupIds, id] }));
        }
    })),

    withHooks({
        async onInit(
            store,
            idbService = inject(IndexDbService)
        ) {
            idbService.moneyTrackerSetups$.pipe(takeUntilDestroyed()).subscribe(data => {
                if (data) {
                    const activeDataIds: string[] = [];
                    data.forEach(setup => {
                        if (!setup.deletedAt) {
                            activeDataIds.push(setup.id);
                        }
                    });
                    patchState(store, { setups: data });
                    patchState(store, { activeSetupIds: activeDataIds });
                    console.log('activeSetupIds =====>>>>>', activeDataIds, data);
                }
            });
            idbService.moneyTransaction$.pipe(takeUntilDestroyed()).subscribe(data => {
                if (data) {
                    console.log('moneyTransaction ==========>>>>', data);
                    patchState(store, { transactions: data });
                }
            });
        },
    }),
);

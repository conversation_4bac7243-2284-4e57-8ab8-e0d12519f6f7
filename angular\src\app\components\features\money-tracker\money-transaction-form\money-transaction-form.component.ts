import { Component, computed, inject, Inject, signal, Signal, ViewChild, WritableSignal } from '@angular/core';
import { Attachment } from '@app/_interfaces/generic.interface';
import { MoneyTrackerSetup, MoneyTransaction } from '@app/_interfaces/money-tracker.interface';
import { HashtagComponent } from '@app/components/addons/hashtags/hashtag/hashtag.component';
import { takeUntil } from 'rxjs';
import { MoneyTrackerSetupFormComponent } from '../money-tracker-setup-form/money-tracker-setup-form.component';
import { MoneyTrackerStore } from '@app/_stores/money-tracker.store';
import { Subject } from 'rxjs';
import { FormArray, FormBuilder, FormControl, FormGroup, FormsModule, NgForm, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import _ from 'lodash';
import { CacheService } from '@app/_services/cache.service';
import { AlertService } from '@app/_services/alert.service';
import { DependencyService } from '@app/_services/dependency.service';
import { FirebaseService } from '@app/_services/firebase.service';
import { UtilsService } from '@app/_services/utils.service';
import { InputTextComponent } from '@app/components/shared/inputs/input-text/input-text.component';
import { CommonModule } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { AttachmentListComponent } from '@app/components/addons/attachments/attachment-list/attachment-list.component';
import { InputCurrencyComponent } from '@app/components/shared/inputs/input-currency/input-currency.component';
import { InputDateComponent } from '@app/components/shared/inputs/input-date/input-date.component';
import { InputFileComponent } from '@app/components/shared/inputs/input-file/input-file.component';
import { InputHashtagComponent } from '@app/components/shared/inputs/input-hashtag/input-hashtag.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { InputSelectComponent } from '@app/components/shared/inputs/input-select/input-select.component';
import { getCustomDate } from '@app/_utils/utils';
import { notOnlyWhitespace } from '@app/_directives/form-validator.directive';
import { TransactionType } from '@app/_types/generic.type';
import { MapService } from '@app/_services/map.service';

@Component({
  selector: 'app-money-transaction-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatMenuModule,
    InputTextComponent,
    InputHashtagComponent,
    SvgComponent,
    InputCurrencyComponent,
    InputDateComponent,
    AttachmentListComponent,
    InputFileComponent,
    InputSelectComponent
  ],
  templateUrl: './money-transaction-form.component.html',
  styleUrl: './money-transaction-form.component.scss'
})

export class MoneyTransactionFormComponent {

  @ViewChild('mtForm') mtForm!: NgForm;
  unSubscribe = new Subject<void>();
  transactionForm: FormGroup;
  transactionInitial: MoneyTransaction;
  mode: 'new' | 'edit';
  readonly moneyStore = inject(MoneyTrackerStore);
  newAttachments: FormArray = this.fb.array([]);
  deletedAttachments: FormArray = this.fb.array([]);
  setup: WritableSignal<MoneyTrackerSetup | null> = signal<MoneyTrackerSetup | null>(null);
  isDescription = false;
  transactionTypeMap: Signal<Record<TransactionType, string>> = this.mapService.transactionTypeMap;
  submitted: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<MoneyTransactionFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'new' | 'edit', value: MoneyTransaction, transactionType: TransactionType },
    private fb: FormBuilder,
    public cc: CacheService,
    private utilsService: UtilsService,
    public dialog: MatDialog,
    private alertService: AlertService,
    private ds: DependencyService,
    private fbService: FirebaseService,
    private mapService: MapService
  ) {
    this.mode = data.mode;
    this.transactionInitial = data.mode == 'new' ? this.initiateForm() : this.initiateForm(data.value);

    if (this.transactionInitial.description) {
      this.isDescription = true;
    }

    this.transactionForm = this.fb.group({
      id: new FormControl(this.transactionInitial.id, Validators.required),
      title: new FormControl(this.transactionInitial.title, [Validators.required, Validators.maxLength(120), notOnlyWhitespace()]),
      description: new FormControl(this.transactionInitial.description, [Validators.maxLength(500), notOnlyWhitespace()]),
      amount: new FormControl(this.transactionInitial.amount, [Validators.required, Validators.maxLength(10)]),
      transactionDate: new FormControl(this.transactionInitial.transactionDate, Validators.required),
      transactionType: new FormControl(this.transactionInitial.transactionType, Validators.required),
      tags: new FormControl(this.transactionInitial.tags),
      attachments: this.fb.array([]),
      setupId: new FormControl(this.transactionInitial.setupId),
      uid: new FormControl(this.transactionInitial.uid, Validators.required)
    });

    this.setupChange();

    if (this.transactionInitial.attachments && this.transactionInitial.attachments.length !== 0) {
      this.clearAttachments();
      this.transactionInitial.attachments.forEach((attach: Attachment) => {
        this.attachments.push(this.ds.addAttachmentForm(attach));
      });
    }

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  get attachmentValues(): any[] {
    const attachments = [...this.attachments.value, ...this.newAttachments.value];
    return attachments;
  }

  get attachments(): FormArray {
    return this.transactionForm.get('attachments') as FormArray;
  }

  clearAttachments() {
    while (this.attachments.length !== 0) {
      this.attachments.removeAt(0)
    }
  }

  get basePath(): string {
    return `userData/attachments/${this.cc.user ? this.cc.user.uid : ''}/moneyTrackerTransactions/${this.transactionInitial.id}`;
  }

  initiateForm(transaction?: MoneyTransaction): MoneyTransaction {
    return {
      id: transaction ? transaction.id : this.utilsService.getNewId(),
      title: transaction ? transaction.title : '',
      description: transaction ? transaction.description : '',
      amount: transaction ? String(transaction.amount) : '',
      transactionDate: transaction ? transaction.transactionDate : getCustomDate(),
      transactionType: transaction ? transaction.transactionType : this.data.transactionType || 'expense',
      tags: transaction ? transaction.tags : [],
      attachments: transaction ? transaction.attachments : [],
      setupId: transaction ? transaction.setupId : this.moneyStore.enabledSetups()[0].id,
      uid: this.cc.user.uid,
    }
  }

  setupChange() {
    this.setup.set(this.moneyStore.idToSetup()[this.transactionForm.value.setupId]);
  }

  getFc(fcName: string): FormControl {
    return this.transactionForm.get(fcName) as FormControl;
  }

  hasChanges(): boolean {
    const initial = _.cloneDeep(this.transactionInitial);
    const current = _.cloneDeep(this.transactionForm.value);
    if (initial.attachments?.length !== current.attachments?.length || this.newAttachments.length > 0 || this.deletedAttachments.length > 0) return true;
    // initial.attachments.sort((a: { key: string; }, b: { key: string; }) => (a.key > b.key ? 1 : -1));
    // current.attachments.sort((a: { key: string; }, b: { key: string; }) => (a.key > b.key ? 1 : -1));
    return !_.isEqual(initial, current);
  }

  hasValue(): boolean {
    return (this.transactionForm.value.description.trim() !== '' || this.transactionForm.value.emotion !== null || this.transactionForm.value.attachments.length > 0);
  }

  deleteAttachments(attachments: Attachment[]) {
    attachments?.forEach(attach => {
      const attachmentFormArray = attach.status === 'local' ? this.newAttachments : this.attachments;
      const index = attachmentFormArray.controls.findIndex(control => control.get('id')?.value === attach.id);
      if (index !== - 1) {
        attachmentFormArray.removeAt(index);
      }
      if (attach.status === 'cloud') {
        this.deletedAttachments.push(this.ds.addAttachmentForm(attach));
      }
    });
  }

  openSetup(setup: MoneyTrackerSetup | null = null) {
    const setupDialog = this.dialog.open(MoneyTrackerSetupFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      minHeight: '90vh',
      disableClose: true,
      data: {
        mode: 'edit',
        value: setup ? setup : null,
      },
    });
  }

  async save() {
    this.submitted = true;
    if (!this.transactionForm.valid) {
      this.alertService.alert(this.cc.texts()['overlay_moneyTrackerMandatoryFieldAlert_title'], this.cc.texts()['overlay_moneyTrackerMandatoryFieldAlert_content'], this.cc.texts()['screen_common_ok']);
    } else {
      this.cc.isLoading = true;
      this.transactionForm.get('amount')?.setValue(Number(this.transactionForm.value.amount));
      if (!this.transactionForm.value.id) return;
      if (this.newAttachments.length > 0) {
        const attachmentsRecord: Record<number, { attachments: { path: string; file: Uint8Array }[]; dek?: string }> = {};
        this.newAttachments.value.forEach((attachment: Attachment) => {
          const originalPath = `${this.basePath}/originalFiles/${attachment.id}.${attachment.format}`;
          const files = [{ path: originalPath, file: attachment.originalFile as Uint8Array }];
          if (attachment.fileType === 'image') {
            const optimizedPath = `${this.basePath}/optimizedFiles/${attachment.id}.${attachment.format}`;
            const thumbnailPath = `${this.basePath}/thumbnails/${attachment.id}.${attachment.format}`;
            files.push({ path: optimizedPath, file: attachment.optimizedFile as Uint8Array });
            files.push({ path: thumbnailPath, file: attachment.thumbnail as Uint8Array });
          }
          attachmentsRecord[attachment.id] = { attachments: files, dek: this.data?.value?.encData?.dek || '' };
        });
        const uploadRecord = await this.fbService.uploadFilesAsRecord(attachmentsRecord);
        for (const attach of this.newAttachments.value) {
          if (attach.id in uploadRecord && uploadRecord[attach.id]) {
            const updatedAttach: Attachment = {
              ...attach,
              status: 'cloud',
            };
            this.attachments.push(this.ds.addAttachmentForm(updatedAttach));
          }
        }
      }

      if (this.deletedAttachments.length > 0) {
        const deletedAttachmentRecord: Record<number, string[]> = {};
        this.deletedAttachments.value.forEach((attachment: Attachment) => {
          const originalPath = `${this.basePath}/originalFiles/${attachment.id}.${attachment.format}`;
          const filePaths: string[] = [originalPath];
          if (attachment.fileType === 'image') {
            const optimizedPath = `${this.basePath}/optimizedFiles/${attachment.id}.${attachment.format}`;
            const thumbnailPath = `${this.basePath}/thumbnails/${attachment.id}.${attachment.format}`;
            filePaths.push(optimizedPath);
            filePaths.push(thumbnailPath);
          }
          deletedAttachmentRecord[attachment.id] = filePaths;
        });
        await this.fbService.deleteFilesAsRecord(deletedAttachmentRecord);
      }

      const transactionData: MoneyTransaction = this.mode === 'new' ? this.moneyStore.getNewTransaction() : this.data.value;
      const updatedTransaction = { ...transactionData, ...this.transactionForm.value };
      updatedTransaction.description = this.ds.extractOpsString(updatedTransaction.description || '');

      if (this.mode === 'new') {
        this.moneyStore.addMoneyTransaction(updatedTransaction);
      } else if (this.mode === 'edit') {
        this.moneyStore.updateMoneyTransactions([updatedTransaction]);
      }
      this.cc.isLoading = false;
      this.dialogRef.close();
    }
  }

  deleteTransaction() {
    this.moneyStore.deleteMoneyTransactions([this.data.value]);
    this.dialogRef.close();
  }

  openHashtagsDialog() {
    const dialog = this.dialog.open(HashtagComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        values: this.transactionForm.value.tags,
        type: 'map'
      },
    })

    dialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.transactionForm.get('tags')?.setValue(result);
      }
    });
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }

  reset() {
    this.transactionForm.reset();
    this.mtForm.resetForm();
    this.transactionForm.patchValue(this.transactionInitial);
  }

  ngOnDestroy() {
    this.unSubscribe?.complete();
    this.unSubscribe?.next();
  }
}

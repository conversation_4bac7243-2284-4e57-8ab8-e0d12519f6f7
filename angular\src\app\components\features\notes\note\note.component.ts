import { Component, inject, Signal, ViewChild, WritableSignal } from '@angular/core';
import { MatDialog } from "@angular/material/dialog";
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatMenuModule, MatMenuTrigger } from '@angular/material/menu';
import { InputDropdownComponent } from '@app/components/shared/inputs/input-dropdown/input-dropdown.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { NoteStore, UserStore } from '@app/_stores';
import { Note } from '@app/_interfaces/note.interface';
import { NoteFormComponent } from '../note-form/note-form.component';
import { CacheService } from '@app/_services/cache.service';
import { Subject, takeUntil } from 'rxjs';
import { FilterMoodComponent } from '@app/components/shared/filters/filter-mood/filter-mood.component';
import { FilterAttachmentComponent } from '@app/components/shared/filters/filter-attachment/filter-attachment.component';
import { FilterDateRangeComponent } from '@app/components/shared/filters/filter-date-range/filter-date-range.component';
import { ParseTextPipe } from '@app/_pipes/parse-text.pipe';
import { ViewSettingService } from '@app/_services/view-setting.service';
import { FilterSearchComponent } from '@app/components/shared/filters/filter-search/filter-search.component';
import { FilterHashtagComponent } from '@app/components/shared/filters/filter-hashtag/filter-hashtag.component';
import { FilterFavouriteComponent } from '@app/components/shared/filters/filter-favourite/filter-favourite.component';
import { AlertService } from '@app/_services/alert.service';
import { DragScrollDirective } from '@app/_directives/drag-scroll.directive';
import { MapService } from '@app/_services/map.service';

@Component({
  selector: 'app-note',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SvgComponent,
    MatMenuModule,
    InputDropdownComponent,
    FilterMoodComponent,
    FilterAttachmentComponent,
    FilterDateRangeComponent,
    ParseTextPipe,
    FilterSearchComponent,
    FilterHashtagComponent,
    FilterFavouriteComponent,
    DragScrollDirective
  ],
  templateUrl: './note.component.html',
  styleUrl: './note.component.scss'
})

export class NoteComponent {

  readonly noteStore = inject(NoteStore);
  readonly userStore = inject(UserStore);
  isFilter: boolean = false;
  unSubscribe = new Subject<void>();
  viewSignals: Signal<Record<string, WritableSignal<any>>> = this.userStore.viewSignals;
  moodMap: Signal<Record<number, string>> = this.mapService.moodMap;
  moodEmojiMap: Signal<Record<number, string>> = this.mapService.moodEmojiMap;

  @ViewChild('menuTrigger', { static: false }) childMenu!: MatMenuTrigger;

  constructor(public dialog: MatDialog, public cc: CacheService, public vs: ViewSettingService, private alertService: AlertService, private mapService: MapService) {
    // this.noteShowConfig = getNoteShowConfig(this.userStore.userSetup());
  }

  ngOnInit() {

  }

  async toggleFilter() {
    if(this.isFilter && this.noteStore.isFiltered()) {
      const confirmed = await this.alertService.confirm('Filter Applied!', 'Hiding the filters will result in resetting the applied filter', 'PROCEED', 'CANCEL');
      if (!confirmed) {
        return;
      }
      this.noteStore.clearFilter();
      this.isFilter = false;
    } else {
      this.isFilter = !this.isFilter;
    }
  }

  openNoteForm(mode: 'new' | 'edit', note?: Note) {
    const confirmDialog = this.dialog.open(NoteFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      minHeight: '90vh',
      disableClose: true,
      data: {
        mode: mode,
        value: note,
      },
    });
    return confirmDialog.afterClosed();
  }

  getIsOpen() {
    return this.vs.type()['noteGroupedViewType']().includes('collapsedView') ? 'false' : 'true';
  }

  toggleChildMenu(event: Event) {
    event.stopPropagation(); // Prevent parent menu from closing
    this.childMenu.menuOpen ? this.childMenu.closeMenu() : this.childMenu.openMenu();
  }

  toggleChildGroup(element: HTMLElement, groupId: string) {
    const isOpened = element.getAttribute('isOpen');
    const groupedElement = document.getElementById(groupId);
    if (!groupedElement) {
      return
    }
    if (isOpened === 'false') {
      groupedElement.setAttribute('isGroupOpened', 'true');
      element.setAttribute('isOpen', 'true');
    } else {
      groupedElement.setAttribute('isGroupOpened', 'false');
      element.setAttribute('isOpen', 'false');
    }
  }

  updateEvent(key: 'noteGroupBy' | 'noteShow' | 'noteShowType', event: any) {
    // this.userStore.updateSetup(key, event);
    // if (key === 'noteShowType' && event == this.noteShowType.compact) {
    //   this.userStore.updateSetup('noteShow', this.userStore.userSetupDefault().noteShow);
    //   this.showControl.setValue(this.userStore.userSetupDefault().noteShow);
    // }
  }

}

import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { NoSpaceDirective } from '@app/_directives/no-space.directive';
import { User } from '@app/_interfaces/user.interface';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';
import { CryptographyService } from '@app/_services/cryptography.service';
import { SvgComponent } from '@app/components/shared/svg/svg.component';

@Component({
  selector: 'app-custom-encryption-key',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    FormsModule,
    ReactiveFormsModule,
    NoSpaceDirective
  ],
  templateUrl: './custom-encryption-key.component.html',
  styleUrl: './custom-encryption-key.component.scss'
})

export class CustomEncryptionKeyComponent {

  control: FormControl = new FormControl('', [Validators.required, Validators.maxLength(20)]);
  type: 'password' | 'text' = 'password';
  attemptCount: number = 0;

  constructor(
    public dialogRef: MatDialogRef<CustomEncryptionKeyComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { userData: User, encPrivateKey: string },
    public cc: CacheService,
    private cryptoService: CryptographyService,
    private alertService: AlertService
  ) {

  }

  async verifyKey() {
    const hashedCustomEncKey = this.cryptoService.hashDataInBase64(this.control.value);
    const privateKey = this.cryptoService.decryptValue(this.data.encPrivateKey, hashedCustomEncKey);
    this.cryptoService.eccPrivateKeyPKCS8 = privateKey;

    try {
      this.cryptoService.decrypt(this.data.userData, false) as User;
      this.dialogRef.close(privateKey);
    } catch (error) {
      this.attemptCount++;
      this.control.setValue('');
      if (this.attemptCount >= 5) {
        await this.alertService.alert(this.cc.texts()['overlay_loginAccessFailed_titleTopBar'], this.cc.interpolateText('overlay_loginAccessFailed_warningMessage', { minutesValue: '60' }), this.cc.texts()['overlay_loginAccessFailed_logout'], 'color-35');
        this.close();
        return;
      } else {
        this.alertService.alert(this.cc.texts()['overlay_loginEncryptionAttempts_titleTopBar'], this.cc.texts()['overlay_loginEncryptionAttempts_subContent'], this.cc.texts()['overlay_loginEncryptionAttempts_proceed'], 'color-35', this.cc.interpolateText('overlay_loginEncryptionAttempts_mainContent', { valueCount: (5 - this.attemptCount).toString() }), 'color-11');
      }
    }
  }

  close() {
    this.dialogRef.close(null);
  }

}

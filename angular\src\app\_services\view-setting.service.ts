import { computed, inject, Injectable, Signal, WritableSignal } from '@angular/core';
import { InputDropdownConfig } from '@app/_interfaces/generic.interface';
import { UserStore } from '@app/_stores';
import { ShowType, ShowTypeKey } from '@app/_types/generic.type';
import { CacheService } from './cache.service';

@Injectable({
    providedIn: 'root',
})

export class ViewSettingService {

    userStore = inject(UserStore);
    type: Signal<Record<string, WritableSignal<any>>> = this.userStore.viewSignals;

    showMap: { [key in ShowTypeKey]: { showSignalKey: string, compact: ShowType[], custom: ShowType[], descriptionTypeKey?: string | null, descriptionTypeValue?: string | null } } = {
        listShowType: {
            showSignalKey: 'listShow',
            compact: ['itemCount'],
            custom: ['itemCount', 'collaboratorsCount']
        },
        listItemShowType: {
            showSignalKey: 'listItemShow',
            compact: [],
            custom: ['description'],
        },
        noteShowType: {
            showSignalKey: 'noteShow',
            compact: ['date'],
            custom: ['mood', 'date', 'time', 'attachments', 'collaboratorsCount'],
            descriptionTypeKey: 'noteDescriptionType',
            descriptionTypeValue: 'none',
        },
        entityShowType: {
            showSignalKey: 'entityShow',
            compact: ['time'],
            custom: ['habitResponse', 'mood', 'time', 'reminder', 'duration', 'repeat', 'checklist', 'attachments', 'calendarName', 'label'],
            descriptionTypeKey: 'entityDescriptionType',
            descriptionTypeValue: 'none',
        },
        pastTodoShowType: {
            showSignalKey: 'pastTodoShow',
            compact: ['time'],
            custom: ['time', 'reminder', 'duration', 'repeat', 'checklist', 'attachments'],
            descriptionTypeKey: 'pastTodoDescriptionType',
            descriptionTypeValue: 'none',
        },
        futureTodoShowType: {
            showSignalKey: 'futureTodoShow',
            compact: ['time'],
            custom: ['time', 'reminder', 'duration', 'repeat', 'checklist', 'attachments'],
            descriptionTypeKey: 'futureTodoDescriptionType',
            descriptionTypeValue: 'none',
        },
        pastHabitShowType: {
            showSignalKey: 'pastHabitShow',
            compact: ['time'],
            custom: ['habitResponse', 'time', 'reminder', 'duration', 'repeat', 'attachments'],
            descriptionTypeKey: 'pastHabitDescriptionType',
            descriptionTypeValue: 'none',
        },
        futureHabitShowType: {
            showSignalKey: 'futureHabitShow',
            compact: ['time'],
            custom: ['habitResponse', 'time', 'reminder', 'duration', 'repeat', 'attachments'],
            descriptionTypeKey: 'futureHabitDescriptionType',
            descriptionTypeValue: 'none',
        },
        pastJournalShowType: {
            showSignalKey: 'pastJournalShow',
            compact: ['date'],
            custom: ['mood', 'date', 'time', 'reminder', 'duration', 'repeat', 'attachments'],
            descriptionTypeKey: 'pastJournalDescriptionType',
            descriptionTypeValue: 'none',
        },
        futureJournalShowType: {
            showSignalKey: 'futureJournalShow',
            compact: ['time'],
            custom: ['mood', 'time', 'reminder', 'duration', 'repeat', 'attachments'],
            descriptionTypeKey: 'futureJournalDescriptionType',
            descriptionTypeValue: 'none',
        },
        pastMoneyTrackerShowType: {
            showSignalKey: 'pastMoneyTrackerShow',
            compact: ['setup'],
            custom: ['setup', 'attachments'],
            descriptionTypeKey: 'pastMoneyTrackerDescriptionType',
            descriptionTypeValue: 'none',
        },
        futureMoneyTrackerShowType: {
            showSignalKey: 'futureMoneyTrackerShow',
            compact: ['setup'],
            custom: ['setup', 'attachments'],
            descriptionTypeKey: 'futureMoneyTrackerDescriptionType',
            descriptionTypeValue: 'none',
        },
        pastCalendarEventShowType: {
            showSignalKey: 'pastCalendarEventShow',
            compact: ['time'],
            custom: ['time', 'reminder', 'duration', 'repeat', 'calendarName']
        },
        futureCalendarEventShowType: {
            showSignalKey: 'futureCalendarEventShow',
            compact: ['time'],
            custom: ['time', 'reminder', 'duration', 'repeat', 'calendarName']
        }
    }

    constructor(private cc: CacheService) {

    }

    updateShowType(showType: ShowTypeKey) {
        const showTypeSignal = this.type()[showType];
        const showConfig = this.showMap[showType];
        if (showTypeSignal() === 'compact') {
            this.type()[showConfig.showSignalKey].set(showConfig.compact);
        }
        if (showTypeSignal() === 'custom') {
            this.type()[showConfig.showSignalKey].set(showConfig.custom);
        }
        if (showConfig.descriptionTypeKey && showConfig.descriptionTypeValue) {
            this.type()[showConfig.descriptionTypeKey].set(showConfig.descriptionTypeValue);
        }
        this.userStore.updateViewSettings();
    }

    updateView() {
        this.userStore.updateViewSettings();
    }

    // Generic

    showTypeConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            compact: {
                name: this.cc.texts()['dropdown_show_compact'],
            },
            custom: {
                name: this.cc.texts()['dropdown_show_custom'],
            }
        }
    })

    groupedViewTypeConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            collapsedView: {
                icon: 'collapse',
                name: this.cc.texts()['dropdown_groupSettings_collapsedView'],
            },
            showCounts: {
                icon: 'count',
                name: this.cc.texts()['dropdown_groupSettings_showCounts'],
            },
        }
    })

    // Notes

    noteGroupByConfig: { [key: string]: InputDropdownConfig } = {
        none: {
            icon: 'none',
            name: 'None',
        },
        date: {
            icon: 'calendar',
            name: 'Date',
        },
        hashtag: {
            icon: 'hashtag',
            name: 'Hashtag',
        },
        mood: {
            icon: 'smiley',
            name: 'Mood',
        }
    }

    noteShowConfig: { [key: string]: InputDropdownConfig } = {
        description: {
            icon: 'description',
            name: 'Description',
            signalName: 'noteDescriptionType',
            config: {
                none: {
                    icon: 'none',
                    name: 'None',
                },
                short: {
                    icon: 'short',
                    name: 'Short',
                },
                full: {
                    icon: 'full',
                    name: 'Full',
                }
            },
            multiple: false
        },
        mood: {
            icon: 'smiley',
            name: 'Mood',
        },
        date: {
            icon: 'calendar',
            name: 'Date',
        },
        time: {
            icon: 'clock',
            name: 'Time',
        },
        attachments: {
            icon: 'attachment',
            name: 'Attachments',
        },
        collaboratorsCount: {
            icon: 'users',
            name: 'Member Count',
        },
        hashtag: {
            icon: 'hashtag',
            name: 'Hashtag',
        }
    }

    // Lists

    listGroupByConfig: { [key: string]: InputDropdownConfig } = {
        none: {
            icon: 'none',
            name: 'None',
        },
        hashtag: {
            icon: 'hashtag',
            name: 'Hashtag',
        }
    }

    listShowConfig: { [key: string]: InputDropdownConfig } = {
        description: {
            icon: 'description',
            name: 'Description',
        },
        itemCount: {
            icon: 'circleCheck',
            name: 'Item Count',
        },
        collaboratorsCount: {
            icon: 'users',
            name: 'Collaborators Count',
        },
        hashtag: {
            icon: 'hashtag',
            name: 'Hashtag',
        }
    }

    listItemAddonConfig: { [key: string]: InputDropdownConfig } = {
        checkbox: {
            icon: 'circleCheck',
            name: 'Checkbox',
        },
        customText: {
            icon: 'text',
            name: 'Custom Text',
        },
        none: {
            icon: 'none',
            name: 'None',
        }
    }

    listItemShowConfig: { [key: string]: InputDropdownConfig } = {
        description: {
            icon: 'description',
            name: 'Description',
        },
        lastUpdatedBy: {
            icon: 'edit',
            name: 'Last Updated By',
        },
        lastUpdatedAt: {
            icon: 'clock',
            name: 'Last Updated At',
        }
    }

    listItemShowTypeConfig: { [key: string]: InputDropdownConfig } = {
        compact: {
            name: 'Compact',
        },
        custom: {
            name: 'Custom',
        }
    }

    // Entities

    entityGroupByConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            chronological: {
                icon: 'none',
                name: this.cc.texts()['screen_common_none'],
            },
            category: {
                icon: 'feature',
                name: this.cc.texts()['dropdown_groupBy_feature'],
            },
            date: {
                icon: 'calendar',
                name: this.cc.texts()['screen_common_date'],
            },
            hashtag: {
                icon: 'hashtag',
                name: this.cc.texts()['screen_common_hashtag'],
            },
            mood: {
                icon: 'smiley',
                name: this.cc.texts()['dropdown_groupBy_mood'],
            },
            transactionType: {
                icon: 'entry',
                name: this.cc.texts()['dropdown_groupBy_entry'],
            },
            setup: {
                icon: 'widget',
                name: this.cc.texts()['dropdown_groupBy_setup'],
            }
        }
    })

    entityGroupedViewTypeConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            collapsedView: {
                icon: 'collapse',
                name: this.cc.texts()['dropdown_groupSettings_collapsedView'],
            },
            showCounts: {
                icon: 'count',
                name: this.cc.texts()['dropdown_groupSettings_showCounts'],
            },
            showNetAmount: {
                icon: 'count',
                name: this.cc.texts()['dropdown_groupSettings_showNetAmount'],
            }
        }
    })

    entityShowConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            description: {
                icon: 'description',
                name: 'Description',
                signalName: 'entityDescriptionType',
                config: {
                    none: {
                        icon: 'none',
                        name: this.cc.texts()['screen_common_none'],
                    },
                    short: {
                        icon: 'short',
                        name: this.cc.texts()['dropdown_description_short'],
                    },
                    full: {
                        icon: 'full',
                        name: this.cc.texts()['dropdown_description_full'],
                    }
                },
                multiple: false
            },
            habitResponse: {
                icon: 'response',
                name: this.cc.texts()['bottomSheet_viewConfiguration_habitResponse'],
            },
            setup: {
                icon: 'widget',
                name: this.cc.texts()['dropdown_groupBy_setup'],
            },
            date: {
                icon: 'calendar',
                name: this.cc.texts()['bottomSheet_viewConfiguration_date'],
            },
            mood: {
                icon: 'smiley',
                name: this.cc.texts()['bottomSheet_viewConfiguration_mood'],
            },
            time: {
                icon: 'clock',
                name: this.cc.texts()['screen_common_time'],
            },
            reminder: {
                icon: 'bell',
                name: this.cc.texts()['bottomSheet_viewConfiguration_reminder'],
            },
            duration: {
                icon: 'timer',
                name: this.cc.texts()['bottomSheet_viewConfiguration_duration'],
            },
            repeat: {
                icon: 'repeat',
                name: this.cc.texts()['screen_common_repeat'],
            },
            checklist: {
                icon: 'checklist',
                name: this.cc.texts()['bottomSheet_viewConfiguration_checkList'],
            },
            attachments: {
                icon: 'attachment',
                name: this.cc.texts()['screen_common_attachment'],
            },
            calendarName: {
                icon: 'calendarIntegration',
                name: this.cc.texts()['bottomSheet_viewConfiguration_calendarName'],
            },
            label: {
                icon: 'label',
                name: this.cc.texts()['bottomSheet_viewConfiguration_featureLabels'],
            },
            hashtag: {
                icon: 'hashtag',
                name: this.cc.texts()['screen_common_hashtag'],
            },
            emptyDays: {
                icon: 'emptyCalendar',
                name: this.cc.texts()['bottomSheet_viewConfiguration_emptyDays'],
            },
            invalidEntry: {
                icon: 'circleInvalid',
                name: this.cc.texts()['bottomSheet_viewConfiguration_invalidEntry'],
            }
        }
    });

    entityDescriptionTypeConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            none: {
                icon: 'none',
                name: this.cc.texts()['screen_common_none'],
            },
            short: {
                icon: 'short',
                name: this.cc.texts()['dropdown_description_short'],
            },
            full: {
                icon: 'full',
                name: this.cc.texts()['dropdown_description_full'],
            }
        }
    })

    // Collaborator Role

    collaboratorRoleConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            viewer: {
                name: this.cc.texts()['dropdown_permission_viewer'],
                description: this.cc.texts()['dropdown_permission_viewerContent'],
            },
            editor: {
                name: this.cc.texts()['dropdown_permission_editor'],
                description: this.cc.texts()['dropdown_permission_editorContent'],
            },
        }
    })
}

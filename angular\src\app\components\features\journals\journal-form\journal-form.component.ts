import { CommonModule } from '@angular/common';
import { Component, computed, inject, Inject, Signal, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule, NgForm, FormGroup, FormArray, FormBuilder, FormControl, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { Attachment } from '@app/_interfaces/generic.interface';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';
import { DependencyService } from '@app/_services/dependency.service';
import { FirebaseService } from '@app/_services/firebase.service';
import { UtilsService } from '@app/_services/utils.service';
import { AttachmentListComponent } from '@app/components/addons/attachments/attachment-list/attachment-list.component';
import { HashtagComponent } from '@app/components/addons/hashtags/hashtag/hashtag.component';
import { InputFileComponent } from '@app/components/shared/inputs/input-file/input-file.component';
import { InputHashtagComponent } from '@app/components/shared/inputs/input-hashtag/input-hashtag.component';
import { InputMoodDropdownComponent } from '@app/components/shared/inputs/input-mood-dropdown/input-mood-dropdown.component';
import { InputMoodComponent } from '@app/components/shared/inputs/input-mood/input-mood.component';
import { InputTextEditorComponent } from '@app/components/shared/inputs/input-text-editor/input-text-editor.component';
import { InputTextComponent } from '@app/components/shared/inputs/input-text/input-text.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import _ from 'lodash';
import { Subject, takeUntil } from 'rxjs';
import { JournalStore } from '@app/_stores';
import { Journal, JournalSetup } from '@app/_interfaces/journal.interface';
import { JournalSetupFormComponent } from '../journal-setup-form/journal-setup-form.component';
import { InputCheckmarkAdvancedComponent } from '@app/components/shared/inputs/input-checkmark-advanced/input-checkmark-advanced.component';
import { getCustomDate } from '@app/_utils/utils';

@Component({
  selector: 'app-journal-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SvgComponent,
    InputTextComponent,
    InputTextEditorComponent,
    InputHashtagComponent,
    InputMoodComponent,
    InputMoodDropdownComponent,
    AttachmentListComponent,
    InputFileComponent,
    MatMenuModule,
    InputCheckmarkAdvancedComponent
  ],
  templateUrl: './journal-form.component.html',
  styleUrl: './journal-form.component.scss'
})

export class JournalFormComponent {

  @ViewChild('jrForm') jrForm!: NgForm;
  unSubscribe = new Subject<void>();
  journalForm: FormGroup;
  journalInitial: Journal;
  mode: 'new' | 'edit';
  readonly journalStore = inject(JournalStore);
  newAttachments: FormArray = this.fb.array([]);
  deletedAttachments: FormArray = this.fb.array([]);
  setup: Signal<JournalSetup> = computed(() => {
    return this.journalStore.idToSetup()[this.data.setupId];
  });

  constructor(
    public dialogRef: MatDialogRef<JournalFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'new' | 'edit', setupId: string, value: Journal, dateString: string },
    private fb: FormBuilder,
    public cc: CacheService,
    private utilsService: UtilsService,
    public dialog: MatDialog,
    private alertService: AlertService,
    private ds: DependencyService,
    private fbService: FirebaseService
  ) {
    this.mode = data.mode;
    console.log("journal form data--->", data.value);
    // this.setup = this.journalStore.idToSetup()[this.data.setupId];
    this.journalInitial = data.mode == 'new' ? this.initiateForm() : this.initiateForm(data.value);

    this.journalForm = this.fb.group({
      id: new FormControl(this.journalInitial.id, Validators.required),
      description: new FormControl(this.journalInitial.description),
      tags: new FormControl(this.journalInitial.tags),
      emotion: new FormControl(this.journalInitial.emotion),
      attachments: this.fb.array([]),
      completedAt: new FormControl(this.journalInitial.completedAt),
      dueAt: new FormControl(this.journalInitial.dueAt),
      setupId: new FormControl(this.journalInitial.setupId),
      uid: new FormControl(this.journalInitial.uid, Validators.required)
    });


    if (this.journalInitial.attachments && this.journalInitial.attachments.length !== 0) {
      this.clearAttachments();
      this.journalInitial.attachments.forEach((attach: Attachment) => {
        this.attachments.push(this.ds.addAttachmentForm(attach));
      });
    }

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  get attachmentValues(): any[] {
    const attachments = [...this.attachments.value, ...this.newAttachments.value];
    return attachments;
  }

  get attachments(): FormArray {
    return this.journalForm.get('attachments') as FormArray;
  }

  clearAttachments() {
    while (this.attachments.length !== 0) {
      this.attachments.removeAt(0)
    }
  }

  get basePath(): string {
    return `userData/attachments/${this.cc.user ? this.cc.user.uid : ''}/journalActions/${this.journalInitial.id}`;
  }

  initiateForm(journal?: Journal): Journal {
    return {
      id: journal ? journal.id : this.utilsService.getNewId(),
      description: journal ? journal.description : '',
      tags: journal ? journal.tags : [],
      emotion: journal ? journal.emotion : null,
      attachments: journal ? journal.attachments : [],
      completedAt: journal ? journal.completedAt : null,
      dueAt: journal ? journal.dueAt : this.data.dateString ? getCustomDate(this.data.dateString) : getCustomDate(),
      setupId: this.data.setupId,
      uid: this.cc.user.uid,
    }
  }

  getFc(fcName: string): FormControl {
    return this.journalForm.get(fcName) as FormControl;
  }

  hasChanges(): boolean {
    const initial = _.cloneDeep(this.journalInitial);
    const current = _.cloneDeep(this.journalForm.value);
    if (initial.attachments?.length !== current.attachments?.length || this.newAttachments.length > 0 || this.deletedAttachments.length > 0) return true;
    // initial.attachments.sort((a: { key: string; }, b: { key: string; }) => (a.key > b.key ? 1 : -1));
    // current.attachments.sort((a: { key: string; }, b: { key: string; }) => (a.key > b.key ? 1 : -1));
    return !_.isEqual(initial, current);
  }

  hasValue(): boolean {
    return (this.journalForm.value.description.trim() !== '' || this.journalForm.value.emotion !== null || this.journalForm.value.attachments.length > 0);
  }

  deleteAttachments(attachments: Attachment[]) {
    attachments?.forEach(attach => {
      const attachmentFormArray = attach.status === 'local' ? this.newAttachments : this.attachments;
      const index = attachmentFormArray.controls.findIndex(control => control.get('id')?.value === attach.id);
      if (index !== - 1) {
        attachmentFormArray.removeAt(index);
      }
      if (attach.status === 'cloud') {
        this.deletedAttachments.push(this.ds.addAttachmentForm(attach));
      }
    });
  }

  editSetup() {
    const setupDialog = this.dialog.open(JournalSetupFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      minHeight: '90vh',
      disableClose: true,
      data: {
        mode: 'edit',
        value: this.setup(),
      },
    });
  }

  async save() {
    this.cc.isLoading = true;
    if (!this.journalForm.value.id) return;
    if (this.newAttachments.length > 0) {
      const attachmentsRecord: Record<number, { attachments: { path: string; file: Uint8Array }[]; dek?: string }> = {};
      this.newAttachments.value.forEach((attachment: Attachment) => {
        const originalPath = `${this.basePath}/originalFiles/${attachment.id}.${attachment.format}`;
        const files = [{ path: originalPath, file: attachment.originalFile as Uint8Array }];
        if (attachment.fileType === 'image') {
          const optimizedPath = `${this.basePath}/optimizedFiles/${attachment.id}.${attachment.format}`;
          const thumbnailPath = `${this.basePath}/thumbnails/${attachment.id}.${attachment.format}`;
          files.push({ path: optimizedPath, file: attachment.optimizedFile as Uint8Array });
          files.push({ path: thumbnailPath, file: attachment.thumbnail as Uint8Array });
        }
        attachmentsRecord[attachment.id] = { attachments: files, dek: this.data?.value?.encData?.dek || '' };
      });
      const uploadRecord = await this.fbService.uploadFilesAsRecord(attachmentsRecord);
      for (const attach of this.newAttachments.value) {
        if (attach.id in uploadRecord && uploadRecord[attach.id]) {
          const updatedAttach: Attachment = {
            ...attach,
            status: 'cloud',
          };
          this.attachments.push(this.ds.addAttachmentForm(updatedAttach));
        }
      }
    }

    if (this.deletedAttachments.length > 0) {
      const deletedAttachmentRecord: Record<number, string[]> = {};
      this.deletedAttachments.value.forEach((attachment: Attachment) => {
        const originalPath = `${this.basePath}/originalFiles/${attachment.id}.${attachment.format}`;
        const filePaths: string[] = [originalPath];
        if (attachment.fileType === 'image') {
          const optimizedPath = `${this.basePath}/optimizedFiles/${attachment.id}.${attachment.format}`;
          const thumbnailPath = `${this.basePath}/thumbnails/${attachment.id}.${attachment.format}`;
          filePaths.push(optimizedPath);
          filePaths.push(thumbnailPath);
        }
        deletedAttachmentRecord[attachment.id] = filePaths;
      });
      await this.fbService.deleteFilesAsRecord(deletedAttachmentRecord);
    }

    const journalData: Journal = this.mode === 'new' ? this.journalStore.getNewJournal() : this.data.value;
    const updatedJournal = { ...journalData, ...this.journalForm.value };
    updatedJournal.description = this.ds.extractOpsString(updatedJournal.description || '');

    if (this.mode === 'new') {
      this.journalStore.addJournal(updatedJournal);
    } else if (this.mode === 'edit') {
      this.journalStore.updateJournals([updatedJournal]);
    }
    this.cc.isLoading = false;
    this.dialogRef.close();
  }

  openHashtagsDialog() {
    const dialog = this.dialog.open(HashtagComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        values: this.journalForm.value.tags,
        type: 'map'
      },
    })

    dialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.journalForm.get('tags')?.setValue(result);
      }
    });
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }

  reset() {
    this.journalForm.reset();
    this.jrForm.resetForm();
    this.journalForm.patchValue(this.journalInitial);
  }

  ngOnDestroy() {
    this.unSubscribe?.complete();
    this.unSubscribe?.next();
  }
}

import { Component, inject, Inject, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, NgForm, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MoneyTrackerSetup } from '@app/_interfaces/money-tracker.interface';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';
import { UtilsService } from '@app/_services/utils.service';
import { MoneyTrackerStore } from '@app/_stores';
import { CollaboratorComponent } from '@app/components/addons/collaborators/collaborator/collaborator.component';
import { HashtagComponent } from '@app/components/addons/hashtags/hashtag/hashtag.component';
import { Subject, takeUntil } from 'rxjs';
import * as _ from 'lodash';
import { CommonModule } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { InputHashtagComponent } from '@app/components/shared/inputs/input-hashtag/input-hashtag.component';
import { InputTextComponent } from '@app/components/shared/inputs/input-text/input-text.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { notOnlyWhitespace } from '@app/_directives/form-validator.directive';

@Component({
  selector: 'app-money-tracker-setup-form',
  standalone: true,
  imports: [
    CommonModule,
    MatMenuModule,
    FormsModule,
    ReactiveFormsModule,
    InputTextComponent,
    InputHashtagComponent,
    SvgComponent
  ],
  templateUrl: './money-tracker-setup-form.component.html',
  styleUrl: './money-tracker-setup-form.component.scss'
})

export class MoneyTrackerSetupFormComponent {

  @ViewChild('MtSetupForm') MtSetupForm!: NgForm;
  unSubscribe = new Subject<void>();
  moneySetupForm: FormGroup;
  moneySetupInitial: MoneyTrackerSetup;
  readonly moneyStore = inject(MoneyTrackerStore);
  submitted: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<MoneyTrackerSetupFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'new' | 'edit', value: MoneyTrackerSetup },
    private fb: FormBuilder,
    public cc: CacheService,
    private alertService: AlertService,
    private utilsService: UtilsService,
    public dialog: MatDialog,
  ) {
    this.moneySetupInitial = data.mode == 'new' ? this.initiateForm() : this.initiateForm(data.value);

    this.moneySetupForm = this.fb.group({
      id: new FormControl(this.moneySetupInitial.id, Validators.required),
      title: new FormControl(this.moneySetupInitial.title, [Validators.required, Validators.maxLength(120), notOnlyWhitespace()]),
      currency: new FormControl(this.moneySetupInitial.currency, [Validators.required, Validators.maxLength(5), notOnlyWhitespace()]),
      isPaused: new FormControl(this.moneySetupInitial.isPaused),
      tags: new FormControl(this.moneySetupInitial.tags),
      uid: new FormControl(this.moneySetupInitial.uid, Validators.required)
    });

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  initiateForm(moneySetup?: MoneyTrackerSetup): MoneyTrackerSetup {
    return {
      id: moneySetup ? moneySetup.id : this.utilsService.getNewId(),
      title: moneySetup ? moneySetup.title : '',
      currency: moneySetup ? moneySetup.currency : '₹',
      isPaused: moneySetup ? moneySetup.isPaused : false,
      tags: moneySetup ? moneySetup.tags : [],
      uid: this.cc.user.uid
    }
  }

  getFc(fcName: string): FormControl {
    return this.moneySetupForm.get(fcName) as FormControl;
  }

  hasChanges() {
    const initial = _.cloneDeep(this.moneySetupInitial);
    const current = _.cloneDeep(this.moneySetupForm.value);
    return !_.isEqual(initial, current);
  }

  addCollaborator() {
    const confirmDialog = this.dialog.open(CollaboratorComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      data: {
        collection: 'moneyTrackerSetups',
        entityData: this.data.value,
        isPublic: false,
      },
    });
    return confirmDialog.afterClosed();
  }

  openHashtagsDialog() {
    const dialog = this.dialog.open(HashtagComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        values: this.moneySetupForm.value.tags,
        type: 'map'
      },
    })

    dialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.moneySetupForm.get('tags')?.setValue(result);
      }
    });
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }

  save() {
    this.submitted = true;
    if (this.moneySetupForm.invalid) {
      this.alertService.alert(this.cc.texts()['overlay_moneyTrackerMandatoryFieldAlert_title'], this.cc.texts()['overlay_moneyTrackerMandatoryFieldAlert_content'], this.cc.texts()['screen_common_ok']);
      return;
    };
    const moneySetup: MoneyTrackerSetup = this.data.mode === 'new' ? this.moneyStore.getNewMoneySetup() : this.data.value;
    const updatedSetup = { ...moneySetup, ...this.moneySetupForm.value };

    if (this.data.mode === 'new') {
      this.moneyStore.addMoneySetup(updatedSetup);
    } else if (this.data.mode === 'edit') {
      this.moneyStore.updateMoneySetups([updatedSetup]);
    }
    this.dialogRef.close();
  }

  deleteMoneySetup() {
    this.moneyStore.deleteMoneySetups([this.data.value]);
    this.dialogRef.close();
  }

}

import { Component, CUSTOM_ELEMENTS_SCHEMA, Inject } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';
import { Subject, takeUntil } from 'rxjs';
import Swiper from 'swiper';
import { SwiperOptions } from 'swiper/types';
import { SvgComponent } from '../../svg/svg.component';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-input-timer-round-select',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './input-timer-round-select.component.html',
  styleUrl: './input-timer-round-select.component.scss'
})

export class InputTimerRoundSelectComponent {

  rounds = Array.from({ length: 99 }, (_, i) => String(i + 1).padStart(1, '0'));
  unSubscribe = new Subject<void>();
  durationRepeatCount: FormControl = new FormControl(1);
  durationRepeatType: FormControl = new FormControl(1);
  roundSwiper!: Swiper;

  constructor(
    public dialogRef: MatDialogRef<InputTimerRoundSelectComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { durationRepeatCount: string, durationRepeatType: 0 | 1 },
    private alertService: AlertService,
    public cc: CacheService,
  ) {

    this.durationRepeatCount.setValue(data.durationRepeatCount);
    this.durationRepeatType.setValue(data.durationRepeatType);

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  getRepeatText(): string {
    if (this.durationRepeatType.value === 0) {
      return this.durationRepeatCount.value === 1 ? this.cc.texts()['overlay_roundPicker_previewMinimumRound'] : this.cc.interpolateText('overlay_roundPicker_previewMinimumRounds', { rounds: this.durationRepeatCount.value });
    } else {
      return this.durationRepeatCount.value === 1 ? this.cc.texts()['overlay_roundPicker_exactRound'] : this.cc.interpolateText('overlay_roundPicker_exactRounds', { rounds: this.durationRepeatCount.value });
    }
  }

  ngAfterViewInit() {
    const swiperConfig: SwiperOptions = {
      slidesPerView: 3,
      spaceBetween: 30,
      direction: 'vertical',
      navigation: false,
      pagination: false,
      speed: 50,
      centeredSlides: true,
      mousewheel: {
        invert: true,
        enabled: true,
        sensitivity: 2,
        thresholdDelta: 25,
        thresholdTime: 75,
      },
    }
    this.roundSwiper = new Swiper('.round-swiper', {
      ...swiperConfig,
      loop: true,
      on: {
        slideChangeTransitionEnd: () => {
          const realIndex = this.roundSwiper?.realIndex;
          if (realIndex >= 0) {
            this.durationRepeatCount.setValue(this.rounds[realIndex]);
          }
        },
      },
    });

    this.roundSwiper.slideToLoop(this.rounds.indexOf(this.durationRepeatCount.value), 0);
  }

  hasChanges(): boolean {
    return this.durationRepeatCount.value !== this.data.durationRepeatCount ||
      this.durationRepeatType.value !== this.data.durationRepeatType;
  }

  save() {
    this.dialogRef.close(
      {
        durationRepeatCount: Number(this.durationRepeatCount.value),
        durationRepeatType: this.durationRepeatType.value,
        type: 'UPDATE'
      });
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const confirmed = await this.alertService.confirm('Alert', 'You have unsaved changes. Do you really want to discard them?', 'Discard', 'Cancel');
      if (!confirmed) {
        return;
      }
      this.dialogRef.close();
    } else {
      this.dialogRef.close();
    }
  }

  ngOnDestroy() {
    this.unSubscribe.complete();
    this.unSubscribe.next();
  }
}

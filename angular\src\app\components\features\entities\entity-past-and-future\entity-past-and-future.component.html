<div class="top-section">
    <h4 class="heading mb-0">{{ config[mode()][activeEntity()]['name'] }}</h4>
    <div class="flex">
        <app-svg [name]="config[mode()][activeEntity()]['filter'] ? 'filterOutline' : 'filter'"
            class="filter-icon ri-me-6" role="button"
            (click)="config[mode()][activeEntity()]['filter'] = !config[mode()][activeEntity()]['filter']" *ngIf="isDataAvailable()"></app-svg>

        <app-svg name="more" class="more-icon" [matMenuTriggerFor]="infoMenu" role="button" [color]="cc.theme.color12"></app-svg>
        <mat-menu #infoMenu="matMenu" class="me-menu info-menu ri-w-350px">
            <button mat-menu-item (click)="$event.stopPropagation();">
                <div class="ri-me-6">
                    <span class="text-14-400 color-8">{{ cc.texts()['screen_common_groupBy'] }}</span>
                    <app-input-dropdown class="ri-pt-6px"
                        [signal]="viewSignals()[config[mode()][activeEntity()]['groupedViewType']]" [multiple]="true" (select)="updateEvent()"
                        [config]="vs.entityGroupedViewTypeConfig()" textClass="text-12-400" *ngIf="vs.type()[config[mode()][activeEntity()]['groupBy']]() !== 'none'" [hiddenValues]="config[mode()][activeEntity()]['hidddenGroupedViewTypeValues']"></app-input-dropdown>
                </div>
                <app-input-dropdown [signal]="viewSignals()[config[mode()][activeEntity()]['groupBy']]"
                    (select)="updateEvent()" [config]="vs.entityGroupByConfig()"
                    [hiddenValues]="config[mode()][activeEntity()]['hiddenGroupValues']" textClass="text-12-400 whitespace-nowrap"></app-input-dropdown>
            </button>
            <button mat-menu-item (click)="$event.stopPropagation()">
                <div class="ri-me-6">
                    <span class="text-14-400 color-8">{{ cc.texts()['screen_common_show'] }}</span>
                    <app-input-dropdown class="ri-pt-6px" [signal]="viewSignals()[config[mode()][activeEntity()]['show']]" [multiple]="true" (select)="updateEvent()"
                        [disabled]="viewSignals()[config[mode()][activeEntity()]['showType']]() === 'compact'"
                        [config]="entityShowConfig()" (selectNested)="updateEvent()"
                        [hiddenValues]="config[mode()][activeEntity()]['hiddenShowValues']" textClass="text-12-400"></app-input-dropdown>
                </div>
                <app-input-dropdown [signal]="viewSignals()[config[mode()][activeEntity()]['showType']]"
                    [config]="vs.showTypeConfig()" (select)="vs.updateShowType(config[mode()][activeEntity()]['showType'])" textClass="text-12-400 whitespace-nowrap"></app-input-dropdown>
            </button>
        </mat-menu>
    </div>
</div>

<mat-tab-group class="entity-past-future-tab" (selectedTabChange)="setTab($event.index)" disableRipple [ngClass]="{ 'hide-header': featureStore.activeEntities().length === 1, 'filter-applied': config[mode()][activeEntity()]['filter'] }">
    <mat-tab *ngFor="let entity of featureStore.activeEntities(); let i = index;">
        <ng-template mat-tab-label>
            <app-svg [name]="entity" [color]="cc.theme.color12" style="height: 18px;"></app-svg>
        </ng-template>
        <app-todo-tab *ngIf="entity === 'todo'" [mode]="mode" [show]="viewSignals()[config[mode()]['todo']['show']]()"
            [filter]="config[mode()]['todo']['filter']"
            [groupType]="viewSignals()[config[mode()]['todo']['groupBy']]()" [activeTab]="activeEntity()" [isDataAvailable]="isDataAvailable()" [groupedViewType]="viewSignals()[config[mode()]['todo']['groupedViewType']]()" [descriptionType]="viewSignals()[config[mode()]['todo']['descriptionType']]()"></app-todo-tab>
        <app-journal-tab *ngIf="entity === 'journal'" [mode]="mode"
            [show]="viewSignals()[config[mode()]['journal']['show']]()" [filter]="config[mode()]['journal']['filter']"
            [groupType]="viewSignals()[config[mode()]['journal']['groupBy']]()" [activeTab]="activeEntity()" [isDataAvailable]="isDataAvailable()" [groupedViewType]="viewSignals()[config[mode()]['journal']['groupedViewType']]()" [descriptionType]="viewSignals()[config[mode()]['journal']['descriptionType']]()"></app-journal-tab>
        <app-habit-tab *ngIf="entity === 'habit'" [mode]="mode"
            [show]="viewSignals()[config[mode()]['habit']['show']]()" [filter]="config[mode()]['habit']['filter']"
            [groupType]="viewSignals()[config[mode()]['habit']['groupBy']]()" [activeTab]="activeEntity()" [isDataAvailable]="isDataAvailable()" [groupedViewType]="viewSignals()[config[mode()]['habit']['groupedViewType']]()" [descriptionType]="viewSignals()[config[mode()]['habit']['descriptionType']]()"></app-habit-tab>
        <app-money-tracker-tab *ngIf="entity === 'moneyTracker'" [mode]="mode"
            [show]="viewSignals()[config[mode()]['moneyTracker']['show']]()"
            [filter]="config[mode()]['moneyTracker']['filter']"
            [groupType]="viewSignals()[config[mode()]['moneyTracker']['groupBy']]()"
            [activeTab]="activeEntity()" [isDataAvailable]="isDataAvailable()" [groupedViewType]="viewSignals()[config[mode()]['moneyTracker']['groupedViewType']]()" [descriptionType]="viewSignals()[config[mode()]['moneyTracker']['descriptionType']]()"></app-money-tracker-tab>

        <app-calendar-tab *ngIf="entity === 'calendarIntegration'" [mode]="mode"
            [show]="viewSignals()[config[mode()]['calendarIntegration']['show']]()"
            [filter]="config[mode()]['calendarIntegration']['filter']"
            [groupType]="viewSignals()[config[mode()]['calendarIntegration']['groupBy']]()"
            [activeTab]="activeEntity()" [isDataAvailable]="isDataAvailable()" [groupedViewType]="viewSignals()[config[mode()]['calendarIntegration']['groupedViewType']]()"></app-calendar-tab>
    </mat-tab>
</mat-tab-group>
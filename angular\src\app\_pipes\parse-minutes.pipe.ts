import { Pipe, PipeTransform } from '@angular/core';
import { CacheService } from '@app/_services/cache.service';

@Pipe({
    name: 'parseMinutes',
    standalone: true,
})

export class ParseMinutesPipe implements PipeTransform {

    constructor( public cc: CacheService ) { }

    transform(value: number): string {

        if (!value || value === 0) return this.cc.texts()['screen_common_noTime'];
        if (value < 0) {
            value = Math.abs(value);
        }

        const hour = Math.floor(value / 60);
        const minute = value % 60;

        let result = '';
        if (hour > 0) result += `${hour}h `;
        if (minute > 0 || hour === 0) result += `${minute}m`;

        return result.trim();
    }
}
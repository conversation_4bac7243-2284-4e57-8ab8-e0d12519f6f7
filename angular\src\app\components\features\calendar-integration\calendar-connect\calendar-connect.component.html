<div class="top-section">
  <h6 class="heading mb-0">{{ cc.texts()['screen_calendarAccount_title'] }}</h6>
  <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
</div>
<div class="connect-block ri-p-4 flex flex-col items-center justify-center">
    <img class="max-w-full h-auto" [src]="data.type === 'google' ? 'assets/imgs/google-calendar.svg' : 'assets/imgs/microsoft-calendar.svg'" alt="Calendar Icon">
    <p class="text-16-400 color-8 mb-0 ri-pt-4">{{ data.type === 'google' ? cc.texts()['screen_common_calendarGoogleTitle'] : cc.texts()['screen_common_calendarMicrosoftTitle'] }}</p>
</div>
<div class="bottom-section flex items-center justify-end ri-p-4 ri-bt-2">
  <button type="submit" class="btn btn-text color-1 p-0" (click)="data.type === 'google' ? connectGoogle() : connectMicrosoft()">{{ cc.texts()['screen_calendarAccount_connect'] }}</button>
</div>
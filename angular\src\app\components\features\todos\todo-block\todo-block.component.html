<div class="flex justify-between items-center" [ngClass]="blockClass" (click)="openTodo()" role="button">
    <div class="">
        <p class="ri-pb-2 mb-0" *ngIf="show.includes('label') && isLabel"><span class="entity-badge text-12-400 color-7" >{{ cc.texts()['screen_common_todo'] }}</span></p>
        <p class="text-16-400 color-8 mb-0">{{ todo().title }}</p>
        <p class="text-12-400 color-7 mb-0 ri-pt-2" *ngIf="descriptionType !== 'none' && todo().description">{{ todo().description | parseText: descriptionType === 'short' ? 'short' : 'full' }}</p>
        <div class="mb-0 ri-pt-2"
            *ngIf="(show.includes('time') || show.includes('reminder') || show.includes('duration')) && todo().startAt.timeString !== '00:00' || (show.includes('attachments') && todo().attachments.length > 0) || show.includes('date') || show.includes('repeat')">
            <div class="inline-flex items-center ri-pe-3" *ngIf="show.includes('date')">
                <app-svg name="calendar" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7">{{ todo().cloudUpdatedAt | date:'d MMM y' }}</span>
            </div>
            <div class="inline-flex items-center ri-pe-3" *ngIf="show.includes('time') && todo().startAt.timeString !== '00:00'">
                <app-svg name="clock" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7">{{ todo().startAt.timeString | parseTime }}</span>
            </div>
            <div class="inline-flex items-center ri-pe-3"
                *ngIf="show.includes('reminder') && todo().reminderAt.length > 0 && todo().startAt.timeString !== '00:00'">
                <app-svg name="bell" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7"><span *ngFor="let reminder of todo().reminderAt;let i = index;">{{ ds.getReminderText(reminder) }}<span *ngIf="i < todo().reminderAt.length - 1">, </span></span></span>
            </div>
            <div class="inline-flex items-center ri-pe-3" *ngIf="show.includes('duration') && todo().startAt.timeString !== '00:00'">
                <app-svg name="timer" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7">{{ todo().duration | parseMinutes }}</span>
            </div>
            <div class="inline-flex items-center ri-pe-3"
                *ngIf="show.includes('attachments') && todo().attachments.length > 0">
                <app-svg name="attachment" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7">{{ todo().attachments.length }}</span>
            </div>
            <div class="inline-flex items-center ri-pe-3" *ngIf="show.includes('repeat') && todo().repeat.length > 0">
                <app-svg name="repeat" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
                <span class="text-12-400 color-7">{{ todo().repeat[0] | parseRule }}</span>
            </div>
        </div>
        <p class="text-12-400 color-7 mb-0 ri-pt-2 hashtags-block" *ngIf="show.includes('hashtag') && todo().tags.length > 0">
            <span class="hashtag" *ngFor="let tagId of todo().tags">#{{userStore.tagMap()[tagId]}}</span>
        </p>
    </div>
    <div>
        <app-input-checkmark-advanced [inputId]="todo().id" [checked]="todo().actions.length > 0 && actionMap()[dateString] && actionMap()[dateString].isSkipped === false" [canceled]="todo().actions.length > 0 && actionMap()[dateString] && actionMap()[dateString].isSkipped === true" [invalid]="entity.status === 'missed'"(click)="$event.stopPropagation()" (change)="onActionChange()"></app-input-checkmark-advanced>
    </div>
</div>
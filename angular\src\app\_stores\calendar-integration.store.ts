import { signalStore, withComputed, withState, withMethods, withHooks, patchState } from "@ngrx/signals";
import { computed, inject } from "@angular/core";
import { UserStore } from "./user.store";
import { DependencyService } from "@app/_services/dependency.service";
import { UtilsService } from "@app/_services/utils.service";
import { IndexDbService } from "@app/_services/index-db.service";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { CalendarEvent, CalendarEventSetup, CalendarIntegration } from "@app/_interfaces/calendar-integration.interface";
import { CalendarEntitySetup, EntitySetup, FeatureSetup } from "@app/_interfaces/feature.interface";
import { CryptographyService } from "@app/_services/cryptography.service";
import { FirebaseFunctionService } from "@app/_services/firebase-function.service";
import { firstValueFrom } from "rxjs";
import { FirestoreCollection } from "@app/_enums/firestore-collection.enum";
import { DayCode } from "@app/_types/generic.type";
import { getDateString } from "@app/_utils/utils";

type CalendarState = {
    accounts: CalendarIntegration[];
    eventSetups: CalendarEventSetup[];
    events: CalendarEvent[];
    isLoading: boolean;
    filter: { date: Date };
};

const initialState: CalendarState = {
    accounts: [],
    eventSetups: [],
    events: [],
    isLoading: false,
    filter: { date: new Date() },
};

export const CalendarIntegrationStore = signalStore(
    { providedIn: 'root' },
    withState(initialState),

    withComputed((
        store,
        userStore = inject(UserStore),
        ds = inject(DependencyService)
    ) => ({
        activeAccounts: computed<CalendarIntegration[]>(() => {
            return store.accounts().filter(setup => !setup.deletedAt);
        }),
        enabledAccounts: computed<CalendarIntegration[]>(() => {
            return store.accounts().filter(setup => !setup.deletedAt && !setup.isPaused);
        }),
        activeSetups: computed<CalendarEventSetup[]>(() => {
            return store.eventSetups().filter(setup => {
                return !setup.deletedAt;
            });
        }),
        activeEvents: computed<CalendarEvent[]>(() => {
            return store.events().filter(event => !event.deletedAt);
        }),
        firstDateString: computed<string>(() => {
            let todayDateString = getDateString();
            const setups = store.eventSetups();
            if (!setups || setups.length === 0) {
                return todayDateString;
            }

            return setups.reduce(
                (acc, setup) => {
                    if (setup.deletedAt || !setup.startAt) return acc;
                    const dateStr = setup.startAt.dateString;

                    if (!acc || (acc && dateStr < acc)) {
                        acc = dateStr;
                    }

                    return acc;
                },
                todayDateString
            );
        }),
        lastDateString: computed<string | null>(() => {
            const setups = store.eventSetups();
            const todayDateString = getDateString();

            if (!setups || setups.length === 0) {
                return null;
            }

            let lastDate: string | null = null;

            for (const setup of setups) {
                const date = setup.endAt?.dateString ?? null;

                if (setup.deletedAt || !setup.startAt || (date && date > todayDateString)) continue;

                if (date === null) return null;

                if (!lastDate || date > lastDate) {
                    lastDate = date;
                }
            }

            return lastDate;
        }),
        idToAccount: computed<Record<string, CalendarIntegration>>(() => {
            const idToCalendarIntegration: Record<string, CalendarIntegration> = {};
            store.accounts().forEach(setup => {
                idToCalendarIntegration[setup.id] = setup;
            });
            return idToCalendarIntegration;
        }),
        calIdToAccount: computed<Record<string, CalendarIntegration>>(() => {
            const calIdToCalendarIntegration: Record<string, CalendarIntegration> = {};
            store.accounts().forEach(setup => {
                calIdToCalendarIntegration[`${setup.calendarType === 'google' ? 'g' : 'm'}_${setup.email}`] = setup;
            });
            return calIdToCalendarIntegration;
        }),
        idToEventSetup: computed<Record<string, CalendarEventSetup>>(() => {
            const idToCalendarEventSetup: Record<string, CalendarEventSetup> = {};
            store.eventSetups().forEach(setup => {
                idToCalendarEventSetup[setup.id] = setup;
            });
            return idToCalendarEventSetup;
        }),
        idToEvent: computed<Record<string, CalendarEvent>>(() => {
            const idToCalendarEvent: Record<string, CalendarEvent> = {};
            store.events().forEach(event => {
                idToCalendarEvent[event.id] = event;
            });
            return idToCalendarEvent;
        }),
        ruleMap: computed<{
            oneDay: { [key in string]: EntitySetup[] },
            daily: EntitySetup[],
            weekly: { [key in DayCode]: EntitySetup[] },
            monthly: { [key in string]: { [key in string]: EntitySetup[] } },
            ordinally: { [key in string]: { [key in DayCode]: EntitySetup[] } },
        }>(() => {
            const idToAccount: Record<string, CalendarIntegration> = {};
            store.accounts().forEach(account => {
                idToAccount[`${account.calendarType === 'google' ? 'g' : 'm'}_${account.email}`] = account;
            });
            const eventSetups = store.eventSetups().filter(setup => {
                const account = idToAccount[setup.calendarAccountId];
                return !setup.deletedAt && account && !account.deletedAt && !account.isPaused;
            });
            
            const { oneDay, daily, weekly, monthly, ordinally } = ds.computeRule(eventSetups, 'calendarIntegration');
            return { oneDay, daily, weekly, monthly, ordinally };
        }),
    })),
    withMethods((
        store,
        userStore = inject(UserStore),
        utilsService = inject(UtilsService),
        idbService = inject(IndexDbService),
        cryptoService = inject(CryptographyService),
        firebaseFunctionService = inject(FirebaseFunctionService),
        ds = inject(DependencyService)
    ) => ({

        updateCalendarIntegrations: async (calendarIntegrations: CalendarIntegration[]) => {
            const oldCalendarIntegrations = [];
            const newCalendarIntegrations = [];
            for (const calendarIntegration of calendarIntegrations) {
                const oldCalendarIntegration = await firstValueFrom(idbService.getEntityById('calendarIntegrations', calendarIntegration.id));
                const newCalendarIntegration = await idbService.update('calendarIntegrations', calendarIntegration.id, calendarIntegration);
                oldCalendarIntegration!.localUpdatedAt = new Date();
                newCalendarIntegration!.cloudUpdatedAt = null;
                oldCalendarIntegrations.push(oldCalendarIntegration);
                newCalendarIntegrations.push(newCalendarIntegration);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldCalendarIntegrations,
                newCalendarIntegrations,
                FirestoreCollection.CalendarIntegrations
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },

        getComputedEntities: (dateString: string, groupBy: 'date' = 'date'): { withTime: EntitySetup[], withoutTime: EntitySetup[], calenderEvents: CalendarEntitySetup[], groupedData: { [key in string]: EntitySetup[] } } => {
            const [day, weekday, nthWeek, month, isLastDay] = ds.parseDateInfo(dateString);
            let entityList: EntitySetup[] = [];
            const computedCalendarEvents = store.ruleMap();
            entityList = [...(computedCalendarEvents.oneDay[dateString] || []), ...computedCalendarEvents.daily, ...computedCalendarEvents.weekly[weekday], ...computedCalendarEvents.monthly[month][day], ...computedCalendarEvents.ordinally[nthWeek][weekday], ...(isLastDay ? computedCalendarEvents.monthly[month]['-1'] : [])];
            const entitiesWithTime: EntitySetup[] = [];
            const entitiesWithoutTime: EntitySetup[] = [];
            const calenderEvents: CalendarEntitySetup[] = [];
            const groupedData: { [key in string]: EntitySetup[] } = {};

            entityList.forEach(entity => {
                const startsBeforeOrOn = entity.startAt ? entity.startAt.dateString <= dateString : true;
                const endsAfterOrOn = entity.endAt ? entity.endAt.dateString >= dateString : true;
                const isInDateRange = startsBeforeOrOn && endsAfterOrOn;

                const calendarEvent = store.idToEventSetup()[entity.id];
                const calendarIntegration = store.calIdToAccount()[calendarEvent.calendarAccountId] || null;
                if (!calendarIntegration || calendarIntegration.isPaused) return;

                if (!isInDateRange) return;

                const hasTime = entity.startAt && entity.startAt.timeString !== "00:00";
                if (hasTime) {
                    entitiesWithTime.push(entity);
                    const startDate = new Date(`${dateString}T${entity.startAt.timeString}:00`);
                    calenderEvents.push({
                        id: entity.id,
                        start: startDate,
                        end: new Date(startDate.getTime() + entity.duration * 60 * 1000),
                        title: entity.title,
                        cssClass: 'none',
                        meta: {
                            entity: 'calendarIntegration',
                            startAt: entity.startAt,
                            endAt: entity.endAt,
                            invalid: entity.invalid
                        }
                    })
                } else {
                    entitiesWithoutTime.push(entity);
                }
                (groupedData[dateString] ??= []).push(entity);
            })
            return { withTime: entitiesWithTime, withoutTime: entitiesWithoutTime, calenderEvents, groupedData };
        },

        getCalendarFeature: (setup: CalendarIntegration): FeatureSetup => {
            return {
                id: setup.id,
                name: setup.calendarType,
                icon: 'calendarIntegration',
                entityName: 'calendarIntegration',
                description: setup.email,
                status: 'active',
                disabled: !setup.isPaused,
                isDefault: false,
                startAt: null,
                endAt: null,
                entity: setup
            };
        }
    })),

    withHooks({
        async onInit(
            store,
            idbService = inject(IndexDbService)
        ) {
            idbService.calendarIntegrations$.pipe(takeUntilDestroyed()).subscribe(data => {
                if (data) {
                    patchState(store, { accounts: data });
                    console.log("i am called subscribed calednar acc data", data);
                }
            });
            idbService.calendarEventSetups$.pipe(takeUntilDestroyed()).subscribe(data => {
                if (data) {
                    patchState(store, { eventSetups: data });
                    console.log("i am called subscribed calednar event setup data", data);
                }
            });
            idbService.calendarEvents$.pipe(takeUntilDestroyed()).subscribe(data => {
                if (data) {
                    patchState(store, { events: data });
                    console.log("i am called subscribed calednar event data", data);
                }
            });
        },
    }),
);

<div class="top-section">
    <h6 class="heading mb-0">Journal <span class="text-12-400 ri-ps-2">{{ data.dateString | date:'dd MMM yyyy, E' }}</span></h6>
    <div class="flex">
        <app-svg name="more" class="more-icon ri-me-6" [matMenuTriggerFor]="infoMenu" role="button" [color]="cc.theme.color12"></app-svg>
        <mat-menu #infoMenu="matMenu" class="me-menu info-menu ">
            <button mat-menu-item class="text-14-400 color-8" (click)="editSetup()">
                <span class="flex items-center"><app-svg name="edit" class="ri-pe-3" [color]="cc.theme.color35" role="button"></app-svg>{{ cc.texts()['dropdown_journalActionKebabMenu_editSetup'] }}</span>
            </button>
        </mat-menu>

        <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
    </div>
</div>
<div class="journal-form ri-p-4" id="journalContentBlock">
    <form [formGroup]="journalForm" #ntForm="ngForm">
        <div class="flex justify-between items-center">
            <p class="color-8 text-16-400 ri-pb-3 mb-0 w-full">{{ setup().title }}</p>
            <app-input-checkmark-advanced [inputId]="journalInitial.id" [disabled]="!hasValue()" [draft]="hasValue()" [checked]="journalForm.value.completedAt ? true : false"></app-input-checkmark-advanced>
        </div>
        <app-input-text-editor [control]="getFc('description')" name="meJournalDescription" [placeholder]="cc.texts()['screen_common_description']" [hideIcon]="true"></app-input-text-editor>
    </form>

    <div class="journal-addons">
        <app-input-mood class="ri-pt-3" *ngIf="journalForm.value.emotion || journalForm.value.emotion === 0"
            [control]="getFc('emotion')"></app-input-mood>
        <app-input-hashtag class="ri-pt-3" *ngIf="journalForm.value.tags && journalForm.value.tags.length > 0"
            [control]="getFc('tags')"></app-input-hashtag>
        <app-attachment-list [title]="setup().title" class="ri-pt-3" *ngIf="attachmentValues && attachmentValues.length > 0"
            [attachments]="attachmentValues" [basePath]="basePath" [entity]="data.value"
            (deleteEvent)="deleteAttachments($event)"></app-attachment-list>
    </div>
</div>
<div class="bottom-section flex items-center justify-between ri-p-4 text-end ri-bt-2">
    <div class="flex items-center">
        <app-input-mood-dropdown class="ri-pe-6" *ngIf="journalForm.value.emotion === null"
            [control]="getFc('emotion')"></app-input-mood-dropdown>
        <app-svg *ngIf="!journalForm.value.tags || journalForm.value.tags.length === 0" name="hashtag" [color]="cc.theme.color35"
            class="ri-pe-6" role="button" (click)="openHashtagsDialog()"></app-svg>
        <app-input-file [newAttachments]="newAttachments" [multiple]="true"></app-input-file>
    </div>
    <button type="submit" class="btn-text text-16-500 color-1" (click)="hasChanges() ? save() : closeDialog()">{{
        hasChanges() ? cc.texts()['screen_common_save'] : cc.texts()['screen_common_close'] }}</button>
</div>
import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { UserData } from '@app/_interfaces/user.interface';
import { CacheService } from '@app/_services/cache.service';
import { CryptographyService } from '@app/_services/cryptography.service';
import { FirebaseFunctionService } from '@app/_services/firebase-function.service';
import { SvgComponent } from '@app/components/shared/svg/svg.component';

@Component({
  selector: 'app-otp',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent
  ],
  templateUrl: './otp.component.html',
  styleUrl: './otp.component.scss'
})

export class OtpComponent implements OnInit {

  otp: string[] = [];
  timeLeft: number = 180;
  timerInterval: any;
  isOtpInvalid: boolean = false;
  otpVerifying: boolean = false;
  otpResending: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<OtpComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { email: string, refId: string },
    private fbfs: FirebaseFunctionService,
    private angularFireAuth: AngularFireAuth,
    public cc: CacheService,
    private cryptoService: CryptographyService
  ) {
  }

  ngOnInit() {
    this.startTimer();
  }

  startTimer() {
    this.timerInterval = setInterval(() => {
      this.timeLeft--;

      if (this.timeLeft <= 0) {
        clearInterval(this.timerInterval);
      }
    }, 1000);
  }

  restartTimer() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
    this.timeLeft = 180;
    this.startTimer();
  }

  stopTimer() {
    clearInterval(this.timerInterval);
  }

  addOtp(number: string) {
    if (this.otp.length === 4) {
      return;
    }
    this.otp.push(number);
    if (this.otp.length === 4 && this.data.refId && this.data.refId != '' && this.timeLeft > 0) {
      this.verifyOTP();
    }
  }

  removeOtp() {
    if (this.otp.length) {
      this.otp = this.otp.slice(0, -1);
    }
  }

  async reSendOTP() {
    this.otpResending = true;
    this.isOtpInvalid = false;
    this.otp = [];
    try {
      const response = await this.fbfs.sendOTP(this.data.email);
      this.data.refId = (response.data as any)['refId'];
      this.restartTimer();
    } catch (error) {
      console.log('error', error);
    } finally {
      this.otpResending = false;
    }
  }

  async verifyOTP() {
    this.otpVerifying = true;
    this.isOtpInvalid = false;
    try {
      const response = await this.fbfs.verifyOTP(this.data.refId, this.otp.join(''));
      const token = (response.data as any).token;
      const cred = await this.angularFireAuth.signInWithCustomToken(token);
      if (cred.user) {
        const userData: UserData = {
          uid: cred.user.uid,
          email: cred.user.email || '',
          name: cred.user.displayName || '',
          isNewUser: cred.additionalUserInfo?.isNewUser || false,
          hashedEmail: ''
        }
        this.dialogRef.close(userData);
      } else {
        this.otp = [];
        this.isOtpInvalid = true;
      }
    } catch (error) {
      this.isOtpInvalid = true;
      this.otp = [];
      console.log('error', error);
    } finally {
      this.otpVerifying = false;
    }
  }

  closeDialog() {
    this.dialogRef.close();
  }
}

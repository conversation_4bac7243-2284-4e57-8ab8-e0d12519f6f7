<div class="ri-p-4 ri-bb-2">
    <div class="btn-select-group">
        <button class="btn btn-select-date" (click)="openInputCalendar('start')">{{startDate() ? cc.getFormattedDate(startDate(), 'd MMM yyyy') :  cc.texts()['overlay_dateRangePicker_startDate'] }}</button>
        <app-svg name="arrowToRight" [color]="cc.theme.color35"></app-svg>
        <button class="btn btn-select-date" (click)="openInputCalendar('end')">{{endDate() ? cc.getFormattedDate(endDate(), 'd MMM yyyy') : cc.texts()['overlay_dateRangePicker_endDate'] }}</button>
    </div>
    <p class="text-14-400 color-8 mb-0 ri-pt-4 text-center ri-mx-6">{{ infoText() }} </p>
</div>
<div class="flex items-center justify-end ri-p-4 ">
    <button class="btn-text text-16-500 ri-ps-4 color-7" (click)="closeDialog()">{{ cc.texts()['screen_common_buttonCancel'] }}</button>
    <button class="btn-text text-16-500 ri-ps-4" [ngClass]="startDate() && endDate() ? 'color-35' : 'color-4'" (click)="applyFilter()"[disabled]="!startDate() || !endDate()">{{ cc.texts()['screen_common_apply'] }}</button>
</div>
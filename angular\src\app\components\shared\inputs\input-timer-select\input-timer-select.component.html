<div class="timer-picker">
    <div class="flex justify-between">
        <p class="text-16-500 color-8 mb-0">{{ cc.texts()['overlay_timeTypeGoal_title'] }}</p>
        <app-svg name="trash" [color]="cc.theme.color11" role="button" (click)="deleteTimer()"
            *ngIf="data.mode === 'edit'"></app-svg>
    </div>
    <div class="flex justify-between ri-pt-4">
        <p class="text-16-500 color-7 mb-0 text-center w-1/3">{{ cc.texts()['overlay_timeTypeGoal_hours'] }}</p>
        <p class="text-16-500 color-7 mb-0 text-center w-1/3">{{ cc.texts()['overlay_timeTypeGoal_minutes'] }}</p>
        <p class="text-16-500 color-7 mb-0 text-center w-1/3">{{ cc.texts()['overlay_timeTypeGoal_seconds'] }}</p>
    </div>
    <div class="flex justify-between ri-py-4">
        <div class="swiper hour-swiper">
            <div class="swiper-wrapper">
                <div class="swiper-slide" *ngFor="let h of hours">{{ h }}</div>
            </div>
        </div>
        <div class="divider-line"></div>
        <div class="swiper minute-swiper">
            <div class="swiper-wrapper">
                <div class="swiper-slide" *ngFor="let m of minutes">{{ m }}</div>
            </div>
        </div>
        <div class="divider-line"></div>
        <div class="swiper second-swiper">
            <div class="swiper-wrapper">
                <div class="swiper-slide" *ngFor="let second of seconds">{{ second }}</div>
            </div>
        </div>
    </div>
    <p class="text-16-500 color-8 mb-0 ri-pt-6 ri-pb-3 text-center" role="button" (click)="openRepeatSelectInput()">{{ getRepeatText() }}</p>
    <div class="timer-picker-footer flex justify-end">
        <button class="btn-text text-16-500 color-7 ri-me-4" (click)="closeDialog()">{{ cc.texts()['screen_common_buttonCancel'] }}</button>
        <button class="btn-text text-16-500 color-1 check-disabled" [disabled]="!hasChanges() && data.mode === 'edit'" (click)="save()">{{ cc.texts()['screen_common_save'] }}</button>
    </div>
</div>
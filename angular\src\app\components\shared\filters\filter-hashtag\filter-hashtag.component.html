<button class="btn btn-filter filter-hashtag h-full" (click)="openHashtagsDialog()" [ngClass]="{ 'active': signal().length > 0 }">
    <app-svg name="hashtag" [color]="signal().length > 0 ? cc.theme.color12 : cc.theme.color7" class="me-1" style="height: 15px; width: 15px;"></app-svg>
    <span>{{ getLabel() }}</span>
    <div class="hashtag-clear ms-2" *ngIf="signal().length > 0" role="button" (click)="signal.set([]);$event.stopPropagation()">
        <app-svg class="d-flex align-items-center justify-content-center" name="close" [color]="cc.theme.color1"></app-svg>
    </div>
</button>
<div class="top-section">
    <h6 class="heading mb-0">{{ cc.texts()['screen_common_setup'] }}</h6>
    <div class="flex">
        <app-svg name="more" class="more-icon ri-me-6" [matMenuTriggerFor]="infoMenu" role="button" *ngIf="data.mode === 'edit'" [color]="cc.theme.color12"></app-svg>
        <mat-menu #infoMenu="matMenu" class="me-menu info-menu ri-w-350px">
            <button mat-menu-item class="text-center text-14-400 color-8" (click)="addCollaborator()"><span class="w-full text-center">{{ cc.texts()['screen_common_dropdownCollaboration'] }}</span></button>
            <button mat-menu-item (click)="deleteMoneySetup()"><span class="text-center text-14-400 color-11 w-full">{{ cc.texts()['screen_common_delete'] }}</span></button>
        </mat-menu>

        <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
    </div>
</div>
<div class="money-setup-form ri-p-4">
    <form [formGroup]="moneySetupForm" #MtSetupForm="ngForm">
        <app-input-text class="ri-pb-3" [control]="getFc('title')" name="meMoneySetupTitle" [placeholder]="cc.texts()['screen_common_titlePlaceholder']" maxLength="120" [submitted]="submitted"></app-input-text>
        <app-input-text class="ri-pb-3" [control]="getFc('currency')" name="meMoneySetupCurrency" [placeholder]="cc.texts()['bottomSheet_moneyTrackerSettings_enterCurrencyPlaceholder']" maxLength="5" icon="moneyTracker" [noSpace]="true" [submitted]="submitted"></app-input-text>
    </form>

    <div class="money-setup-addons">
        <app-input-hashtag class="ri-pt-3" *ngIf="moneySetupForm.value.tags && moneySetupForm.value.tags.length > 0" [control]="getFc('tags')"></app-input-hashtag>
    </div>
    <!-- <pre class="color-8">{{moneySetupForm.value | json }}</pre> -->
</div>
<div class="bottom-section flex items-center justify-between ri-p-4 text-end ri-bt-2">
    <div class="flex items-center">
        <app-svg *ngIf="!moneySetupForm.value.tags || moneySetupForm.value.tags.length === 0" name="hashtag" [color]="cc.theme.color35" class="ri-pe-6" role="button" (click)="openHashtagsDialog()"></app-svg>
    </div>
    <button type="submit" class="btn-text text-16-500 color-1" (click)="hasChanges() ? save() : closeDialog()">{{ hasChanges() ? cc.texts()['screen_common_save'] : cc.texts()['screen_common_close'] }}</button>
</div>
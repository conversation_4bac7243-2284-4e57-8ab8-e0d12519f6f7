import { computed, Injectable, Signal } from '@angular/core';
import { CacheService } from './cache.service';
import { AttachmentType, EntityNameType, EntityStatus, FeatureNameType, FeatureStatus, RepeatFilterType, TransactionType } from '@app/_types/generic.type';

@Injectable({
    providedIn: 'root',
})

export class MapService {

    constructor(private cc: CacheService) {

    }

    attachmentTypeMap: Signal<Record<AttachmentType, string>> = computed(() => {
        return {
            image: 'Image',
            audio: 'Audio',
            video: 'Video',
            document: 'Document',
            txt: 'Text'
        };
    });

    timeTypeMap: Signal<Record<string, string>> = computed(() => {
        return {
            'true': 'Asia/Kolkata (IST)',
            'false': this.cc.texts()['bottomSheet_setTimezone_fixedtimeeverywhereTitle'],
        };
    });

    dayMap: Signal<Record<string, string>> = computed(() => {
        return {
            MO: this.cc.texts()['screen_common_dayMonday'],
            TU: this.cc.texts()['screen_common_dayTuesday'],
            WE: this.cc.texts()['screen_common_dayWednesday'],
            TH: this.cc.texts()['screen_common_dayThursday'],
            FR: this.cc.texts()['screen_common_dayFriday'],
            SA: this.cc.texts()['screen_common_daySaturday'],
            SU: this.cc.texts()['screen_common_daySunday'],
        };
    });

    ordinalMap: Signal<Record<string, string>> = computed(() => {
        return {
            '1': this.cc.texts()['bottomSheet_selectWeeks_1stWeek'],
            '2': this.cc.texts()['bottomSheet_selectWeeks_2ndWeek'],
            '3': this.cc.texts()['bottomSheet_selectWeeks_3rdWeek'],
            '4': this.cc.texts()['bottomSheet_selectWeeks_4thWeek'],
            '5': this.cc.texts()['bottomSheet_selectWeeks_5thWeek'],
            '-1': 'last',
        };
    });

    monthMap: Signal<Record<string, string>> = computed(() => {
        return {
            '1': this.cc.texts()['screen_common_monthJanuary'],
            '2': this.cc.texts()['screen_common_monthFebruary'],
            '3': this.cc.texts()['screen_common_monthMarch'],
            '4': this.cc.texts()['screen_common_monthApril'],
            '5': this.cc.texts()['screen_common_monthMay'],
            '6': this.cc.texts()['screen_common_monthJune'],
            '7': this.cc.texts()['screen_common_monthJuly'],
            '8': this.cc.texts()['screen_common_monthAugust'],
            '9': this.cc.texts()['screen_common_monthSeptember'],
            '10': this.cc.texts()['screen_common_monthOctober'],
            '11': this.cc.texts()['screen_common_monthNovember'],
            '12': this.cc.texts()['screen_common_monthDecember'],
        };
    });

    moodMap: Signal<Record<number, string>> = computed(() => {
        return {
            0: this.cc.texts()['screen_common_verySad'],
            1: this.cc.texts()['screen_common_sad'],
            2: this.cc.texts()['screen_common_meh'],
            3: this.cc.texts()['screen_common_happy'],
            4: this.cc.texts()['screen_common_veryHappy'],
        };
    });

    moodEmojiMap: Signal<Record<number, string>> = computed(() => {
        return {
            0: 'verySadEmoji',
            1: 'sadEmoji',
            2: 'mehEmoji',
            3: 'happyEmoji',
            4: 'veryHappyEmoji',
        };
    });

    transactionTypeMap: Signal<Record<TransactionType, string>> = computed(() => {
        return {
            income: this.cc.texts()['screen_common_income'],
            expense: this.cc.texts()['screen_common_expense'],
        };
    });

    featureNameMap: Signal<Record<FeatureNameType, string>> = computed(() => {
        return {
            todo: this.cc.texts()['screen_common_todos'],
            note: this.cc.texts()['screen_common_notes'],
            list: this.cc.texts()['screen_features_lists'],
            boolean: this.cc.texts()['screen_common_habitYesOrNo'],
            timer: this.cc.texts()['screen_common_habitTime'],
            numeric: this.cc.texts()['screen_common_habitNumber'],
            single: this.cc.texts()['screen_common_habitSingleChoice'],
            multiple: this.cc.texts()['screen_common_habitMultipleChoice'],
            journal: this.cc.texts()['screen_common_journals'],
            google: this.cc.texts()['screen_common_calendarGoogleTitle'],
            microsoft: this.cc.texts()['screen_common_calendarMicrosoftTitle'],
            moneyTracker: this.cc.texts()['screen_common_moneyTracker'],
        };
    });

    entityNameMap: Signal<Record<EntityNameType, string>> = computed(() => {
        return {
            todo: this.cc.texts()['screen_common_todo'],
            habit: this.cc.texts()['screen_common_habit'],
            journal: this.cc.texts()['screen_common_journal'],
            note: this.cc.texts()['screen_common_note'],
            list: this.cc.texts()['screen_common_list'],
            moneyTracker: this.cc.texts()['screen_common_moneyTracker'],
            calendarIntegration: this.cc.texts()['screen_common_calendarIntegration'],
        };
    });

    featureStatusMap: Signal<Record<FeatureStatus, string>> = computed(() => {
        return {
            active: this.cc.texts()['screen_common_active'],
            upcoming: this.cc.texts()['screen_common_upcoming'],
            completed: this.cc.texts()['screen_common_completed'],
        };
    });

    RepeatFilterTypeMap: Signal<Record<RepeatFilterType, string>> = computed(() => {
        return {
            all: this.cc.texts()['screen_common_all'],
            one_time: this.cc.texts()['bottomSheet_todoType_oneTime'],
            repeated: this.cc.texts()['bottomSheet_todoType_repeated'],
        };
    });

    entityStatusMap: Signal<Record<EntityStatus, string>> = computed(() => {
        return {
            all: this.cc.texts()['screen_common_all'],
            missed: this.cc.texts()['screen_common_missed'],
            overdue: this.cc.texts()['screen_common_overdue'],
            draft: this.cc.texts()['screen_common_journalDraft'],
            skipped: this.cc.texts()['screen_common_skipped'],
            incomplete: this.cc.texts()['screen_common_incomplete'],
            completed: this.cc.texts()['screen_common_completed'],
            none: this.cc.texts()['screen_common_none'],
        };
    });
}

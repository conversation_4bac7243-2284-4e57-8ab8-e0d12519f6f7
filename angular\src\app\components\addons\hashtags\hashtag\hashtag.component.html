<div class="top-section">
    <h6 class="heading mb-0">{{ cc.texts()['bottomSheet_publicHashtags_titleTopBar'] }}</h6>
    <div class="">
        <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
    </div>
</div>

<div class="body-section hashtag-block bg-3 position-relative">
    <div class="hashtag-item flex items-center justify-between ri-px-4 ri-py-4" *ngFor="let hashtag of userStore.hashtags(); let i = index;" role="button" (click)="clickHashtag(hashtag.id)">
        <div class="">
            <p class="text-16-400 color-8 mb-0 ri-pb-2">#{{ hashtag.tag }}</p>
        </div>
        <div class="flex justify-between ps-3">
            <app-svg name="tick" [color]="selectedHashtags.includes(hashtag.id) ? cc.theme.color35 : cc.theme.color10"></app-svg>
            <app-svg name="more" class="more-icon ri-ms-6" [matMenuTriggerFor]="infoMenu" role="button" (click)="$event.stopPropagation()" *ngIf="data.type === 'map'"></app-svg>
            <mat-menu #infoMenu="matMenu" class="ms-3 me-menu">
                <button mat-menu-item class="text-14-400 color-8" (click)="openHashtagForm('edit', hashtag)">
                    {{ cc.texts()['screen_common_edit'] }}
                </button>
                <button mat-menu-item class="text-14-400 color-8" (click)="userStore.deleteHashtag(hashtag)">
                    {{ cc.texts()['screen_common_delete'] }}
                </button>
            </mat-menu>
        </div>
    </div>
    <div class="no-result-block h-full flex items-center justify-center flex-col" *ngIf="userStore.hashtags().length === 0">
        <app-svg name='hashtagPlaceholder' [color]="cc.theme.color35"></app-svg>
        <p class="text-16-400 color-8 mb-0 ri-pt-4">{{ cc.texts()['bottomSheet_hashtagSelect_emptyScreenContent'] }}</p>
      </div>
    <div class="me-hashtags ri-px-4 ri-py-4" *ngIf="selectedHashtags.length > 0">
        <span class="hashtag flex items-center" *ngFor="let tagId of selectedHashtags">#{{userStore.tagMap()[tagId]}} <app-svg class="ri-ps-2" name="closeBold" [color]="cc.theme.color7" role="button" (click)="clickHashtag(tagId)"></app-svg></span>
    </div>
</div>
<div class="footer-section flex justify-between ri-px-4 ri-py-3">
    <div class="">
        <app-svg name="plus" role="button" [color]="cc.theme.color35" (click)="openHashtagForm('new')" *ngIf="data.type === 'map'"></app-svg>
    </div>
    <div class="">
        <span class="text-14-400 color-7">{{selectedHashtags.length}}/{{userStore.hashtags().length}}</span>
        <button class="btn-text text-16-500 ri-ps-4 color-1" role="button" (click)="save()">{{data.type === 'map' ? cc.texts()['screen_common_save'] : cc.texts()['screen_common_apply'] }}</button>
    </div>
</div>
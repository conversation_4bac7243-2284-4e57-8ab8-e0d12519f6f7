import { CommonModule } from '@angular/common';
import { Component, computed, inject, Input, OnChanges, signal, Signal, SimpleChanges, ViewChild, WritableSignal } from '@angular/core';
import { EntitySetup, EntityWithDateGroup } from '@app/_interfaces/feature.interface';
import { TodoStore, UserStore } from '@app/_stores';
import { TodoBlockComponent } from '../todo-block/todo-block.component';
import { FilterHashtagComponent } from '@app/components/shared/filters/filter-hashtag/filter-hashtag.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FilterDateRangeComponent } from '@app/components/shared/filters/filter-date-range/filter-date-range.component';
import { FilterAttachmentComponent } from '@app/components/shared/filters/filter-attachment/filter-attachment.component';
import { FilterSearchComponent } from '@app/components/shared/filters/filter-search/filter-search.component';
import { FilterTypeComponent } from '@app/components/shared/filters/filter-type/filter-type.component';
import { FilterStatusComponent } from '@app/components/shared/filters/filter-status/filter-status.component';
import { CdkVirtualScrollViewport, ScrollingModule } from '@angular/cdk/scrolling';
import { CacheService } from '@app/_services/cache.service';
import { AttachmentType, EntityStatus, RepeatFilterType } from '@app/_types/generic.type';
import { DragScrollDirective } from '@app/_directives/drag-scroll.directive';

@Component({
  selector: 'app-todo-tab',
  standalone: true,
  imports: [
    CommonModule,
    ScrollingModule,
    TodoBlockComponent,
    FilterHashtagComponent,
    SvgComponent,
    FormsModule,
    ReactiveFormsModule,
    FilterDateRangeComponent,
    FilterAttachmentComponent,
    FilterSearchComponent,
    FilterTypeComponent,
    FilterStatusComponent,
    DragScrollDirective
  ],
  templateUrl: './todo-tab.component.html',
  styleUrl: './todo-tab.component.scss'
})

export class TodoTabComponent implements OnChanges {

  @ViewChild(CdkVirtualScrollViewport) viewport!: CdkVirtualScrollViewport;

  readonly todoStore = inject(TodoStore);
  readonly userStore = inject(UserStore);
  @Input() mode: Signal<'PAST' | 'FUTURE'> = computed(() => 'PAST');
  @Input() show: string[] = [];
  @Input() groupedViewType: string[] = [];
  @Input() filter: boolean = false;
  @Input() groupType: string = 'date';
  @Input() activeTab: string = 'todo';
  groupTypeSignal: WritableSignal<string> = signal<string>('date');
  @Input() isDataAvailable: boolean = false;
  @Input() descriptionType: string = 'none';

  filterSearch = signal<string>('');
  filterType = signal<RepeatFilterType>('all');
  filterHashtag = signal<string[]>([]);
  filterAttachment = signal<AttachmentType[]>([]);
  filterStatus = signal<EntityStatus>('all');
  startDate = signal<Date | null>(null);
  endDate = signal<Date | null>(null);
  hiddenStatus: EntityStatus[] = ['missed', 'draft', 'incomplete', 'none'];
  showEmptyDays: WritableSignal<boolean> = signal<boolean>(false);
  scrollOffset: number = 0;

  todos: Signal<EntityWithDateGroup[]> = computed(() => {
    const firstDateString = this.todoStore.firstDateString();
    const firstDate = new Date(firstDateString || '');
    const groupedData = new Map<string, EntityWithDateGroup>();
    const groupType = this.groupTypeSignal();

    let dateStrings: string[] = [];
    switch (this.mode()) {
      case 'PAST':
        dateStrings = this.cc.getPastDates(this.startDate() || firstDate, this.endDate() ? this.endDate() : null);
        break;
      case 'FUTURE':
        dateStrings = this.endDate() === null && this.startDate() === null ? this.cc.futureDates() : this.cc.getFutureDates(this.endDate(), this.startDate());
        break;
    }

    dateStrings.forEach(dateString => {
      const entities = this.todoStore.getComputedEntities(dateString, false, groupType as 'date' | 'hashtag');
      let allEntities = [...entities.withoutTime, ...entities.withTime];
      const entityGroupData = entities.groupedData;

      switch (groupType) {
        case 'date':
          allEntities = this.applyFilter(allEntities);
          if (!groupedData.has(dateString)) {
            groupedData.set(dateString, {
              id: dateString,
              name: dateString,
              data: [],
              dateMap: {}
            });
          }
          groupedData.get(dateString)!.data.push(...allEntities);
          if (!groupedData.get(dateString)!.dateMap[dateString]) {
            groupedData.get(dateString)!.dateMap[dateString] = [];
          }
          groupedData.get(dateString)!.dateMap[dateString].push(...allEntities);
          break;
        case 'hashtag':
          const tagId = Object.keys(entityGroupData);
          tagId.forEach(key => {
            const entities = this.applyFilter(entityGroupData[key]);
            if (!groupedData.has(key)) {
              groupedData.set(key, {
                id: key,
                name: key,
                data: [],
                dateMap: {}
              });
            }
            groupedData.get(key)!.data.push(...entities);
            if (!groupedData.get(key)!.dateMap[dateString]) {
              groupedData.get(key)!.dateMap[dateString] = [];
            }
            groupedData.get(key)!.dateMap[dateString].push(...entities);
          });
          break;
      }
    });
    const showEmptyDays = this.showEmptyDays();
    const orderedKeys = [...groupedData.keys()];
    const result = orderedKeys
      .filter(key => key !== '' && (groupedData.get(key)!.data.length !== 0 || showEmptyDays))                     // all non-empty keys, in original order
      .map(key => groupedData.get(key)!)
      .concat(
        groupedData.has('')
          ? [groupedData.get('')!]                  // add the empty key group last, if it exists
          : []
      );

    return result;
  });

  constructor(public cc: CacheService) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['activeTab']) {
      this.viewport?.checkViewportSize();
      this.viewport?.scrollToOffset(this.scrollOffset);
    }

    if (changes['groupType']) {
      this.groupTypeSignal.set(this.groupType);
    }

    if (changes['show']) {
      this.showEmptyDays.set(this.show.includes('emptyDays'));
    }
  }

  getGroupName(group: EntityWithDateGroup): string {
    switch (this.groupTypeSignal()) {
      case 'date':
        return this.cc.getFormattedDate(group.name, 'd MMM y, EEE');
      case 'hashtag':
        return this.userStore.tagMap()[group.id] ? this.userStore.tagMap()[group.id] : this.cc.texts()['screen_common_groupByNoHashtags'];
      default:
        return group.name;
    }
  }

  onScrolledIndexChange(index: number) {
    this.scrollOffset = this.viewport?.measureScrollOffset() ?? 0;
  }

  applyFilter(entities: EntitySetup[]): EntitySetup[] {
    // Filters
    const search = this.filterSearch().toLowerCase();
    const type = this.filterType();
    const hashtags = this.filterHashtag();
    const attachments = this.filterAttachment();
    const status = this.filterStatus();
    return entities.filter(entity => {
      const todo = this.todoStore.idToTodo()[entity.id];
      const matchesSearch = !search || entity.title.toLowerCase().includes(search);
      const matchesType = type === 'all' || entity.repeatType === type;
      const matchesHashtag = hashtags.length === 0 || (todo.tags || []).some(tag => hashtags.includes(tag));
      const matchesAttachment = attachments.length === 0 || (todo.attachments || []).some(att => attachments.includes(att.fileType));
      const matchesStatus = status === 'all' || entity.status === status;

      return matchesSearch && matchesType && matchesHashtag && matchesAttachment && matchesStatus;
    });
  }

  getIsOpen() {
    return this.groupedViewType.includes('collapsedView') ? 'false' : 'true';
  }

  isFiltered() {
    return this.filterSearch() || this.filterType() !== 'all' || this.filterHashtag().length || this.filterAttachment().length || this.filterStatus() !== 'all' || this.startDate() || this.endDate();
  }

  clearFilter() {
    this.filterSearch.set('');
    this.filterType.set('all');
    this.filterHashtag.set([]);
    this.filterAttachment.set([]);
    this.filterStatus.set('all');
    this.startDate.set(null);
    this.endDate.set(null);
  }

  objectKeys(obj: { [key: string]: EntitySetup[] }) {
    return Object.keys(obj);
  }

  trackByForEntity(index: number, entity: EntitySetup) {
    return entity.id;
  }

  trackByForGroup(index: number, group: EntityWithDateGroup) {
    return group.id;
  }

  trackByForDateString(index: number, dateString: string) {
    return dateString;
  }

  toggleChildGroup(element: HTMLElement, groupId: string) {
    const isOpened = element.getAttribute('isOpen');
    const groupedElement = document.getElementById(groupId);
    if (!groupedElement) {
      return
    }
    if (isOpened === 'false') {
      groupedElement.setAttribute('isGroupOpened', 'true');
      element.setAttribute('isOpen', 'true');
    } else {
      groupedElement.setAttribute('isGroupOpened', 'false');
      element.setAttribute('isOpen', 'false');
    }
  }
}

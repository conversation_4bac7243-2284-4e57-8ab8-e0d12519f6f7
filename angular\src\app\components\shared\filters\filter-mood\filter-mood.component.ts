import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, Signal, signal, WritableSignal } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { moods } from '@app/_datas/const.data';
import { SvgComponent } from '../../svg/svg.component';
import { CacheService } from '@app/_services/cache.service';
import { MapService } from '@app/_services/map.service';

@Component({
  selector: 'app-filter-mood',
  standalone: true,
  imports: [
    CommonModule,
    MatMenuModule,
    SvgComponent
  ],
  templateUrl: './filter-mood.component.html',
  styleUrl: './filter-mood.component.scss'
})

export class FilterMoodComponent {

  @Input() signal: WritableSignal<number[]> = signal<number[]>([]);
  @Input() multiple: boolean = false;
  moods: number[] = moods.sort((a, b) => b - a);
  moodMap: Signal<Record<number, string>> = this.mapService.moodMap;
  moodEmojiMap: Signal<Record<number, string>> = this.mapService.moodEmojiMap;

  @Output() select = new EventEmitter<string>();

  constructor(private sanitizer: DomSanitizer, public cc: CacheService, private mapService: MapService) {

  }

  getLabel() {
    // {{signal().length ? signal().length === 1 ? moodMap()[signal()[0]] : signal().length + ' ' + 'Moods' : 'Mood' }}
    if (this.signal().length === 0) {
      return this.cc.texts()['screen_common_filterMoodDefault'];
    } else if (this.signal().length === 1) {
      return this.moodMap()[this.signal()[0]];
    } else {
      return this.cc.interpolateText('screen_common_filterMoodMultiple', { numberOfMoods: this.signal().length.toString() });
    }
  }

  getSanitizedSvg(svg: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(svg);
  }

  onMoodSelected(mood: number, event: MouseEvent): void {
    if (this.multiple) {
      const currentMoods = this.signal() || [];
      if (currentMoods.includes(mood)) {
        this.signal.set(currentMoods.filter((m: number) => m !== mood));
      } else {
        this.signal.set([...currentMoods, mood]);
      };
      this.select.emit();
      event.stopPropagation();
    } else {
      this.signal.set([mood]);
      this.select.emit();
    }
  }
}

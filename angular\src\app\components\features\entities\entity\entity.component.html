<div class="top-section">
    <h4 class="heading mb-0">{{ currentDate | relativeDate }}</h4>
    <div class="flex">
        <div class="flex ri-me-6 items-center">
            <app-svg class="ri-me-2" name="leftArrow" [color]="cc.theme.color12" role="button"
                (click)="navigateWithOffset(-1)"></app-svg>
            <app-svg class="ri-ms-2" name="rightArrow" [color]="cc.theme.color12" role="button"
                (click)="navigateWithOffset(1)"></app-svg>
        </div>
        <app-svg name="more" class="more-icon" [matMenuTriggerFor]="infoMenu" role="button" [color]="cc.theme.color12"></app-svg>
        <mat-menu #infoMenu="matMenu" class="me-menu info-menu ri-w-350px">
            <button mat-menu-item (click)="$event.stopPropagation();">
                <span class="ri-me-6 text-14-400 color-8">{{ cc.texts()['bottomSheet_todayKebabMenu_calendarView'] }}</span>
                <app-input-toggle [signal]="viewSignals()['showCalendarView']" (change)="vs.updateView()"></app-input-toggle>
            </button>
            <button mat-menu-item (click)="$event.stopPropagation();">
                <span class="ri-me-6 text-14-400 color-8">{{ cc.texts()['bottomSheet_todayKebabMenu_myGoal'] }}</span>
                <app-input-toggle [signal]="viewSignals()['showUserGoal']" (change)="vs.updateView()"></app-input-toggle>
            </button>
            <button mat-menu-item (click)="$event.stopPropagation();" [disabled]="viewSignals()['showCalendarView']()">
                <span class="ri-me-6 text-14-400" [ngClass]="viewSignals()['showCalendarView']() ? 'color-7' : 'color-8'">{{ cc.texts()['bottomSheet_todayKebabMenu_hideCompletedItems'] }}</span>
                <app-input-toggle [signal]="viewSignals()['hideCompletedItems']" (change)="vs.updateView()" [disabled]="viewSignals()['showCalendarView']()"></app-input-toggle>
            </button>
            <button mat-menu-item (click)="$event.stopPropagation();" [disabled]="viewSignals()['showCalendarView']() || viewSignals()['entityGroupBy']() === 'category'">
                <span class="ri-me-6 text-14-400" [ngClass]="viewSignals()['showCalendarView']() || viewSignals()['entityGroupBy']() === 'category' ? 'color-7' : 'color-8'">{{ cc.texts()['bottomSheet_todayKebabMenu_timeline'] }}</span>
                <app-input-toggle [signal]="viewSignals()['showTimebox']" (change)="vs.updateView()" [disabled]="viewSignals()['showCalendarView']() || viewSignals()['entityGroupBy']() === 'category'"></app-input-toggle>
            </button>
            <button mat-menu-item (click)="$event.stopPropagation();" [disabled]="viewSignals()['showCalendarView']()">
                <span class="ri-me-6 text-14-400" [ngClass]="viewSignals()['showCalendarView']() ? 'color-7' : 'color-8'">{{ cc.texts()['screen_common_groupBy'] }}</span>
                <app-input-dropdown [signal]="viewSignals()['entityGroupBy']" [config]="vs.entityGroupByConfig()" [hiddenValues]="['date', 'hashtag', 'mood', 'transactionType', 'setup']" (select)="vs.updateView()" textClass="text-12-400 whitespace-nowrap" [ngClass]="viewSignals()['showCalendarView']() ? 'opacity-50' : 'color-'"></app-input-dropdown>
            </button>
            <button mat-menu-item (click)="$event.stopPropagation()" [disabled]="viewSignals()['showCalendarView']()">
                <div class="ri-me-6">
                    <span class="text-14-400" [ngClass]="viewSignals()['showCalendarView']() ? 'color-7' : 'color-8'">{{ cc.texts()['screen_common_show'] }}</span>
                    <app-input-dropdown class="ri-pt-6px" textClass="text-12-400" [signal]="viewSignals()['entityShow']" [config]="vs.entityShowConfig()" [multiple]="true" (select)="vs.updateView()" (selectNested)="vs.updateView()" [disabled]="viewSignals()['entityShowType']() === 'compact'" [hiddenValues]="hiddenValues()" [notApplicableValues]="notApplicableValues()" [colorClass]="viewSignals()['showCalendarView']() ? 'color-10' : 'color-35'"></app-input-dropdown>
                </div>
                <app-input-dropdown [signal]="viewSignals()['entityShowType']" [config]="vs.showTypeConfig()" (select)="vs.updateShowType('entityShowType')" textClass="text-12-400 whitespace-nowrap" [ngClass]="viewSignals()['showCalendarView']() ? 'opacity-50' : 'color-'"></app-input-dropdown>
            </button>
        </mat-menu>
    </div>
</div>

<div class="flex justify-between items-center ri-px-4 ri-pb-2 bg-4 breadcrumb-block">
    <p class="text-14-400 color-12 mb-0" role="button" (click)="openInputCalendar()">{{ cc.getFormattedDate(dateString, 'd MMM y, EEE')}}
    </p>
    <button class="btn today-button text-14-400 color-8 mb-0 flex items-center" *ngIf="dateString !== todayDateString" [routerLink]="['/today']">
        <!-- <app-svg class="ri-me-1" name="leftArrow" [color]="cc.theme.color12" style="height: 18px; width: 18px;" *ngIf="mode === 'FUTURE'"></app-svg> -->
        <span>{{ cc.texts()['screen_common_today'] }}</span> 
        <app-svg class="ri-ms-1" [name]=" mode === 'FUTURE' ? 'leftArrow' : 'rightArrow'" [color]="cc.theme.color12" style="height: 18px; width: 18px;" *ngIf="mode === 'PAST' || mode === 'FUTURE'"></app-svg>
    </button>
</div>

<div class="body-section bg-3 position-relative">
    <div class="goal-block" *ngIf="viewSignals()['showUserGoal']()">
        <p class="text-16-400 color-1 mb-0 ri-p-4 ri-bb-2 text-center" role="button" (click)="openGoal()">{{ viewSignals()['userGoal']() || cc.texts()['bottomSheet_yourGoal_placeholder'] }}</p>
    </div>
    <div class="entities-block" [ngClass]="{'with-goal': viewSignals()['showUserGoal']()}">
        <ng-container *ngIf="!viewSignals()['showCalendarView']()">
            <ng-container
                *ngIf="viewSignals()['entityGroupBy']() === 'chronological' && (featureStore.entities().withTime.length !== 0 || featureStore.entities().withoutTime.length !== 0)">
                <div class="time-line time-line-bottom" *ngIf="userStore.viewSettings().featureSettings.showTimebox && featureStore.findClosestTime() === '00:00' && featureStore.entities().withTime.length !== 0 && featureStore.entities().withoutTime.length !== 0">
                    <span class="time-line-time">{{ cc.getFormattedDate(todayDate, 'hh:mm a') }}</span>
                </div>
                <ng-container *ngIf="featureStore.entities().withTime.length !== 0">
                    <ng-container
                        *ngTemplateOutlet="entitiesBlock; context: { $implicit: featureStore.entities().withTime, groupId: 'withTime' }"></ng-container>
                </ng-container>
                <ng-container *ngIf="featureStore.entities().withoutTime.length !== 0">
                    <ng-container
                        *ngTemplateOutlet="entitiesBlock; context: { $implicit: featureStore.entities().withoutTime, groupId: 'withoutTime' }"></ng-container>
                </ng-container>
            </ng-container>

            <ng-container
                *ngIf="viewSignals()['entityGroupBy']() === 'category' && (featureStore.entities().withTime.length !== 0 || featureStore.entities().withoutTime.length !== 0)">
                <ng-container *ngFor="let group of featureStore.groupByFeature();let i = index;">
                    <div class="text-16-400 color-1 ri-p-4 ri-bb-2 bg-2" [attr.isOpen]="true"
                        #tgroupHead>
                        {{ group.name }}
                    </div>
                    <ng-container
                        *ngTemplateOutlet="entitiesBlock; context: { $implicit: group.data, groupId: 'entityGroup' + i }"></ng-container>
                </ng-container>
            </ng-container>

            <ng-template #entitiesBlock let-element let-groupId="groupId">
                <ng-container *ngFor="let entity of element; let i = index;">
                    <ng-switch [ngSwitch]="entity.entityName">
                        <ng-container *ngSwitchCase="'todo'">
                            <app-todo-block class="ri-bb-2 entity-block" [blockClass]="'ri-px-4 ri-py-3'" [show]="viewSignals()['entityShow']()" [entity]="entity" [dateString]="dateString" [descriptionType]="viewSignals()['entityDescriptionType']()" [isLabel]="viewSignals()['entityGroupBy']() === 'chronological'"></app-todo-block>
                        </ng-container>
                        <ng-container *ngSwitchCase="'habit'">
                            <app-habit-block class="ri-bb-2 entity-block" [blockClass]="'ri-px-4 ri-py-3'" [show]="viewSignals()['entityShow']()" [dateString]="dateString" [entity]="entity" [descriptionType]="viewSignals()['entityDescriptionType']()" [isLabel]="viewSignals()['entityGroupBy']() === 'chronological'"></app-habit-block>
                        </ng-container>
                        <ng-container *ngSwitchCase="'journal'">
                            <app-journal-block class="ri-bb-2 entity-block" [blockClass]="'ri-px-4 ri-py-3'"
                                [show]="viewSignals()['entityShow']()"
                                [dateString]="dateString" [entity]="entity" [descriptionType]="viewSignals()['entityDescriptionType']()" [isLabel]="viewSignals()['entityGroupBy']() === 'chronological'"></app-journal-block>
                        </ng-container>
                        <ng-container *ngSwitchCase="'moneyTracker'">
                            <app-money-tracker-block class="ri-bb-2 entity-block" [blockClass]="'ri-px-4 ri-py-3'"  [show]="viewSignals()['entityShow']()"
                                [entity]="entity" [descriptionType]="viewSignals()['entityDescriptionType']()" [isSetup]="true" [isLabel]="viewSignals()['entityGroupBy']() === 'chronological'"></app-money-tracker-block>
                        </ng-container>
                        <ng-container *ngSwitchCase="'calendarIntegration'">
                            <app-calendar-event-block class="ri-bb-2 entity-block" [blockClass]="'ri-px-4 ri-py-3'" [entity]="entity" [show]="viewSignals()['entityShow']()"
                                [dateString]="dateString" [entity]="entity" [descriptionType]="viewSignals()['entityDescriptionType']()" [isLabel]="viewSignals()['entityGroupBy']() === 'chronological'"></app-calendar-event-block>
                        </ng-container>
                    </ng-switch>
                    <div class="time-line time-line-middle" *ngIf="viewSignals()['showTimebox']() && groupId === 'withTime' && featureStore.findClosestTime() === entity.startAt.timeString">
                        <span class="time-line-time">{{ cc.getFormattedDate(todayDate, 'hh:mm a') }}</span>
                    </div>
                </ng-container>
            </ng-template>

            <!-- <pre class="text-white">{{ journalStore.getCompletedJournals(dateString) | json }}</pre> -->
            <div class="no-result-block h-full flex items-center justify-center flex-col"
                *ngIf="featureStore.entities().withTime.length === 0 && featureStore.entities().withoutTime.length === 0">
                <app-svg name="planPlaceholder" [color]="cc.theme.color1"></app-svg>
                <p class="text-16-400 color-8 mb-0 ri-pt-4">{{ cc.texts()['tab_todayToday_emptyScreenContent'] }}</p>
            </div>
        </ng-container>

        <ng-container *ngIf="viewSignals()['showCalendarView']()">
            <app-entity-calendar [events]="featureStore.entities().calenderEvents"
                [withoutTimeEvents]="featureStore.entities().withoutTime" [viewDate]="currentDate"
                [dateString]="dateString"></app-entity-calendar>
        </ng-container>
        <div class="extra-block" *ngIf="featureStore.entities().withTime.length > 0 || featureStore.entities().withoutTime.length > 0"></div>
    </div>
</div>
<div class="add-button" *ngIf="vs.type()['showTodoFeature']() || moneyStore.enabledSetups().length > 0">
    <button class="btn" #addEntitiesTrigger="matMenuTrigger" (click)="openAddEntities(addEntitiesTrigger, $event)" [matMenuTriggerFor]="addEntitiesMenu"><app-svg name="plus"></app-svg></button>
</div>

<mat-menu #addEntitiesMenu="matMenu" class="me-menu">
    <button mat-menu-item class="text-14-400 color-8" (click)="openMoneyTracker('income')" *ngIf="moneyStore.enabledSetups().length > 0">{{ cc.texts()['screen_common_income'] }}</button>
    <button mat-menu-item class="text-14-400 color-8" (click)="openMoneyTracker('expense')" *ngIf="moneyStore.enabledSetups().length > 0">{{ cc.texts()['screen_common_expense'] }}</button>
    <button mat-menu-item class="text-14-400 color-8" (click)="openTodo()" *ngIf="vs.type()['showTodoFeature']()">{{ cc.texts()['screen_common_todo'] }}</button>
</mat-menu>
<div class="top-section">
    <h6 class="heading mb-0">{{ cc.texts()['screen_common_language'] }}</h6>
    <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
</div>
<div class="">
    <div *ngFor="let languageCode of cc.languages" class="text-16-400 mb-0 ri-px-4 ri-py-3 language-item flex tems-center justify-between" role="button" (click)="selectedLanguage.set(languageCode)">
        <span class="color-8">{{ cc.languageMap[languageCode].name }}</span> 
        <app-svg name="tick" *ngIf="selectedLanguage() === languageCode" [color]="cc.theme.color35" style="height: 18px"></app-svg>
    </div>
</div>
<div class="bottom-section flex items-center justify-between ri-px-4 ri-py-3 text-end ri-bt-2">
    <div class="flex items-center">

    </div>
    <button type="submit" class="btn btn-text text-16-500 color-35 check-disabled" (click)="save()" [disabled]="selectedLanguage() === cc.language()">
        {{ cc.texts()['screen_common_save'] }}
    </button>
</div>
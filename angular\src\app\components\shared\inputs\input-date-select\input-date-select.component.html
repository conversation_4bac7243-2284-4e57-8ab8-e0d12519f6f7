<p class="ri-p-4 text-16-500 color-8 text-center mb-0">{{ cc.texts()['overlay_repeatSelect_daySelectTitle'] }}</p>
<div class="ri-p-4 input-date-block">
    <span class="text-16-500 color-8 date-value" [ngClass]="{'selected' : selectedDates.includes(date) }" *ngFor="let date of dates" role="button" (click)="selectDate(date)"><span class="date-badge">{{ date }}</span></span>
</div>
<div class="bottom-section flex align-items-end justify-end ri-p-4 ri-bt-2">
    <button class="btn-text text-16-500 color-7 me-4" (click)="closeDialog()">{{ cc.texts()['screen_common_buttonCancel'] }}</button>
    <button type="submit" class="btn-text text-16-500 check-disabled color-35" (click)="save()" [disabled]="!hasChanges()">{{ cc.texts()['screen_common_save'] }}</button>
</div>
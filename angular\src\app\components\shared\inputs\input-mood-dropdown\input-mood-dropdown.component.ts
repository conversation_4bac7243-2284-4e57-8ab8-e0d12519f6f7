import { Component, Input, Signal } from '@angular/core';
import { SvgComponent } from '../../svg/svg.component';
import { CommonModule } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { moods } from '@app/_datas/const.data';
import { FormControl } from '@angular/forms';
import { CacheService } from '@app/_services/cache.service';
import { MapService } from '@app/_services/map.service';

@Component({
  selector: 'app-input-mood-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    MatMenuModule,
    SvgComponent
  ],
  templateUrl: './input-mood-dropdown.component.html',
  styleUrl: './input-mood-dropdown.component.scss'
})

export class InputMoodDropdownComponent {

  moods: number[] = [...moods].sort((a, b) => b - a);
  moodMap: Signal<Record<number, string>> = this.mapService.moodMap;
  moodEmojiMap: Signal<Record<number, string>> = this.mapService.moodEmojiMap;
  @Input() control: FormControl = new FormControl();
  @Input() disabled: boolean = false;
  @Input() multiple: boolean = false;

  constructor(private sanitizer: DomSanitizer, public cc: CacheService, private mapService: MapService) {

  }

  getSanitizedSvg(svg: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(svg);
  }

  onMoodSelected(mood: number, event: MouseEvent): void {
    if (this.multiple) {
      const currentMoods = this.control.value || [];
      if (currentMoods.includes(mood)) {
        this.control.setValue(currentMoods.filter((m: number) => m !== mood));
      } else {
        this.control.setValue([...currentMoods, mood]);
      };
      event.stopPropagation();
    } else {
      this.control.setValue(mood);
    }
  }

  trackByMood(index: number, mood: number): number {
    return mood;
  }
}

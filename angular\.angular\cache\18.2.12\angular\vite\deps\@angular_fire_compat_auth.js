import {
  Angular<PERSON><PERSON>Auth,
  AngularFireAuthModule,
  LANGUAGE_CODE,
  PERSISTENCE,
  SETTINGS,
  TENANT_ID,
  USE_DEVICE_LANGUAGE,
  USE_EMULATOR,
  ɵauthFactory
} from "./chunk-W33YBMLU.js";
import "./chunk-GIJXZGIF.js";
import "./chunk-AQKBYGKI.js";
import "./chunk-SPPKMTC7.js";
import "./chunk-L76LM2JH.js";
import "./chunk-GIJ3NXTY.js";
import "./chunk-LVBJCBTU.js";
import "./chunk-JO6TBDKX.js";
import "./chunk-J6PPDRZI.js";
import "./chunk-CB7IVAZV.js";
import "./chunk-2PRKVIQ6.js";
import "./chunk-S4OGKXCW.js";
import "./chunk-N25OJVE5.js";
import "./chunk-RXHPGQPJ.js";
export {
  AngularFireAuth,
  AngularF<PERSON>AuthModule,
  LANGUAGE_CODE,
  PERSISTENCE,
  SETTINGS,
  TENANT_ID,
  USE_DEVICE_LANGUAGE,
  USE_EMULATOR,
  ɵauthFactory
};
//# sourceMappingURL=@angular_fire_compat_auth.js.map

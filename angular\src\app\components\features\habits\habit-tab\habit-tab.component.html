<div class="filter-block" dragScroll [ngClass]="{'h-auto filtered': filter}">
    <app-filter-search [signal]="filterSearch" [name]="'habit'" class="me-2"></app-filter-search>
    <app-filter-hashtag [signal]="filterHashtag" class="me-2"></app-filter-hashtag>
    <app-filter-date-range [startDate]="startDate" [endDate]="endDate" class="me-2"></app-filter-date-range>
    <app-filter-type [value]="filterType" class="me-2"></app-filter-type>
    <app-filter-attachment [signal]="filterAttachment" [multiple]="true" class="me-2"></app-filter-attachment>
    <app-filter-status [value]="filterStatus" [hiddenValues]="hiddenStatus" class="me-2"></app-filter-status>
    <button class="btn btn-filter btn-toggle" (click)="clearFilter()" *ngIf="isFiltered()">{{ cc.texts()['screen_common_clearAll'] }}</button>
</div>

<div class="habit-tab-block" [ngClass]="{'filter-applied': filter}">
  <cdk-virtual-scroll-viewport itemSize="15" class="viewport" #viewport autoSize (scrolledIndexChange)="onScrolledIndexChange($event)" *ngIf="isDataAvailable">
  <ng-container *cdkVirtualFor="let group of habits(); let i = index;">
    <ng-container *ngIf="group.data.length > 0">
        <div class="group-title text-16-400 ri-p-4 ri-bb-2 flex justify-between items-center" #tgroupHead role="button" [attr.isOpen]="getIsOpen()" (click)="toggleChildGroup(tgroupHead,'habitEntityGroup' + i)" *ngIf="group.data.length !== 0 || (group.data.length === 0 && show.includes('emptyDays'))">
          <span [ngClass]="group.data.length === 0 ? 'color-4' : 'color-1'">{{ getGroupName(group) }}</span>
          <span class="group-count" *ngIf="groupedViewType.includes('showCounts') && group.data.length > 0">{{ group.data.length }}</span>
        </div>

        <div class="row-grouped" [attr.id]="'habitEntityGroup' + i" [attr.isGroupOpened]="getIsOpen()" *ngIf="group.data.length !== 0 || (group.data.length === 0 && show.includes('emptyDays'))">
            <ng-container *ngFor="let dateString of objectKeys(group.dateMap); let i = index; trackBy: trackByForDateString;">
                <app-habit-block class="ri-bb-2 entity-block"  *ngFor="let entity of group.dateMap[dateString]; let i = index; trackBy: trackByForEntity;" [blockClass]="'ri-px-4 ri-py-3'" [show]="show" [dateString]="dateString" [entity]="entity" [descriptionType]="descriptionType" [isLabel]="false"></app-habit-block>
            </ng-container>
        </div>
    </ng-container>
  </ng-container>
  </cdk-virtual-scroll-viewport>
  <div class="no-result-block h-full flex items-center justify-center flex-col" *ngIf="!isDataAvailable">
    <app-svg name="habitPlaceholder" [color]="cc.theme.color1"></app-svg>
    <p class="text-16-400 color-8 mb-0 ri-pt-4">{{ cc.texts()['screen_common_habitEmptyScreenContent'] }}</p>
  </div>
</div>

import { CommonModule } from '@angular/common';
import { Component, Input, Signal } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { SvgComponent } from '../../svg/svg.component';
import { moods } from '@app/_datas/const.data';
import { FormControl } from '@angular/forms';
import { CacheService } from '@app/_services/cache.service';
import { AlertService } from '@app/_services/alert.service';
import { MapService } from '@app/_services/map.service';

@Component({
  selector: 'app-input-mood',
  standalone: true,
  imports: [
    CommonModule,
    MatMenuModule,
    SvgComponent
  ],
  templateUrl: './input-mood.component.html',
  styleUrl: './input-mood.component.scss'
})

export class InputMoodComponent {

  moods: number[] = [...moods].sort((a, b) => a - b);
  moodMap: Signal<Record<number, string>> = this.mapService.moodMap;
  moodEmojiMap: Signal<Record<number, string>> = this.mapService.moodEmojiMap;
  @Input() control: FormControl = new FormControl(null);
  @Input() disabled: boolean = false;

  constructor(public cc: CacheService, private alertService: AlertService, private mapService: MapService) {
  }

  async selectMood(mood: number) {
    if(mood === this.control.value) {
      const confirmed = await this.alertService.confirm('Remove Mood Selector', 'This will remove the selected mood and other mood options', 'REMOVE', 'CANCEL', 'color-11');
      if (!confirmed) {
        return;
      }
      this.control.setValue(null);
    } else {
      this.control.setValue(mood);
    }
  }
}

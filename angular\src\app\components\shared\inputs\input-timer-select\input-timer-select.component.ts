import { CommonModule } from '@angular/common';
import { Component, Inject, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';
import { Subject, takeUntil } from 'rxjs';
import { SvgComponent } from '../../svg/svg.component';
import Swiper from 'swiper';
import { SwiperOptions } from 'swiper/types';
import { InputTimerRoundSelectComponent } from '../input-timer-round-select/input-timer-round-select.component';

@Component({
  selector: 'app-input-timer-select',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './input-timer-select.component.html',
  styleUrl: './input-timer-select.component.scss'
})

export class InputTimerSelectComponent {

  hours = Array.from({ length: 24 }, (_, i) => String(i).padStart(2, '0'));
  minutes = Array.from({ length: 60 }, (_, i) => String(i).padStart(2, '0'));
  seconds = Array.from({ length: 60 }, (_, i) => String(i).padStart(2, '0'));
  unSubscribe = new Subject<void>();
  hour: FormControl = new FormControl(null);
  minute: FormControl = new FormControl(null);
  second: FormControl = new FormControl(null);
  hourSwiper!: Swiper;
  minuteSwiper!: Swiper;
  secondSwiper!: Swiper;
  durationRepeatCount: FormControl = new FormControl(1);
  durationRepeatType: FormControl = new FormControl(1);

  constructor(
    public dialogRef: MatDialogRef<InputTimerSelectComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'new' | 'edit', hour: number, minute: number, second: number, durationRepeatCount: number, durationRepeatType: 0 | 1 },
    private alertService: AlertService,
    public cc: CacheService,
    private dialog: MatDialog
  ) {

    this.hour.setValue(data.hour);
    this.minute.setValue(data.minute);
    this.second.setValue(data.second);
    this.durationRepeatCount.setValue(data.durationRepeatCount);
    this.durationRepeatType.setValue(data.durationRepeatType);

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  getRepeatText(): string {
    if (this.durationRepeatType.value === 0) {
      return this.durationRepeatCount.value === 1 ? this.cc.texts()['overlay_roundPicker_previewMinimumRound'] : this.cc.interpolateText('overlay_roundPicker_previewMinimumRounds', { rounds: this.durationRepeatCount.value });
    } else {
      return this.durationRepeatCount.value === 1 ? this.cc.texts()['overlay_roundPicker_exactRound'] : this.cc.interpolateText('overlay_roundPicker_exactRounds', { rounds: this.durationRepeatCount.value });
    }
  }

  ngAfterViewInit() {
    const swiperConfig: SwiperOptions = {
      slidesPerView: 3,
      spaceBetween: 30,
      direction: 'vertical',
      navigation: false,
      pagination: false,
      speed: 50,
      centeredSlides: true,
      mousewheel: {
        invert: true,
        enabled: true,
        sensitivity: 2,
        thresholdDelta: 25,
        thresholdTime: 75,
      },
    }
    this.hourSwiper = new Swiper('.hour-swiper', {
      ...swiperConfig,
      loop: true,
      on: {
        slideChangeTransitionEnd: () => {
          const realIndex = this.hourSwiper?.realIndex;
          if (realIndex >= 0) {
            this.hour.setValue(this.hours[realIndex]);
          }
        },
      },
    });
    this.minuteSwiper = new Swiper('.minute-swiper', {
      ...swiperConfig,
      loop: true,
      on: {
        slideChangeTransitionEnd: () => {
          const realIndex = this.minuteSwiper?.realIndex;
          if (realIndex >= 0) {
            this.minute.setValue(this.minutes[realIndex]);
          }
        },
      },
    });
    this.secondSwiper = new Swiper('.second-swiper', {
      ...swiperConfig,
      loop: true,
      on: {
        slideChangeTransitionEnd: () => {
          const realIndex = this.secondSwiper?.realIndex;
          if (realIndex >= 0) {
            this.second.setValue(this.seconds[realIndex]);
          }
        },
      },
    });

    this.hourSwiper.slideToLoop(this.hours.indexOf(this.hour.value), 0);
    this.minuteSwiper.slideToLoop(this.minutes.indexOf(this.minute.value), 0);
    this.secondSwiper.slideToLoop(this.seconds.indexOf(this.second.value), 0);
  }

  async deleteTimer() {
    const confirmed = await this.alertService.confirm(this.cc.texts()['overlay_timeDelete_title'], this.cc.texts()['overlay_timeDelete_content'], this.cc.texts()['screen_common_delete'], this.cc.texts()['screen_common_buttonCancel'], 'color-11', 'color-7');
    if (!confirmed) {
      return;
    } else {
      this.dialogRef.close(
        {
          hour: null,
          minute: null,
          second: null,
          durationRepeatCount: 1,
          durationRepeatType: 1,
          type: 'DELETE'
        });
    }
  }

  openRepeatSelectInput() {
    const repeatSelectDialog = this.dialog.open(InputTimerRoundSelectComponent, {
      width: '100%',
      maxWidth: '360px',
      disableClose: true,
      data: {
        durationRepeatCount: this.durationRepeatCount.value.toString().padStart(1, '0'),
        durationRepeatType: this.durationRepeatType.value
      },
    });
    repeatSelectDialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.durationRepeatCount.setValue(result.durationRepeatCount);
        this.durationRepeatType.setValue(result.durationRepeatType);
      }
    });
  }

  hasChanges(): boolean {
    return this.hour.value !== this.data.hour ||
      this.minute.value !== this.data.minute ||
      this.second.value !== this.data.second ||
      this.durationRepeatCount.value !== this.data.durationRepeatCount ||
      this.durationRepeatType.value !== this.data.durationRepeatType;
  }

  save() {
    this.dialogRef.close(
      {
        hour: parseInt(this.hour.value, 10),
        minute: parseInt(this.minute.value, 10),
        second: parseInt(this.second.value, 10),
        durationRepeatCount: this.durationRepeatCount.value,
        durationRepeatType: this.durationRepeatType.value,
        type: 'EDIT'
      });
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
        this.dialogRef.close();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }

  ngOnDestroy() {
    this.unSubscribe.complete();
    this.unSubscribe.next();
  }
}

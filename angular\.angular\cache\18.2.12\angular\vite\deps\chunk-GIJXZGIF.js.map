{"version": 3, "sources": ["../../../../../../node_modules/@firebase/auth/dist/esm2017/internal.js", "../../../../../../node_modules/@firebase/auth-compat/dist/index.esm2017.js"], "sourcesContent": ["import { au as debugAssert, av as _isIOS, aw as _isAndroid, ax as _fail, ay as _getRedirectUrl, az as _getProjectConfig, aA as _isIOS7Or8, aB as _createError, aC as _assert, aD as AuthEventManager, aE as _getInstance, b as browserLocalPersistence, aF as _persistenceKeyName, a as browserSessionPersistence, aG as _getRedirectResult, aH as _overrideRedirectResult, aI as _clearRedirectOutcomes, aJ as _castAuth } from './index-68602d24.js';\nexport { A as ActionCodeOperation, ai as ActionCodeURL, L as AuthCredential, I as AuthErrorCodes, aL as AuthImpl, aO as AuthPopup, M as EmailAuthCredential, V as EmailAuthProvider, W as FacebookAuthProvider, F as FactorId, aP as FetchProvider, Y as GithubAuthProvider, X as GoogleAuthProvider, N as OAuthCredential, Z as OAuthProvider, O as OperationType, Q as PhoneAuthCredential, P as PhoneAuthProvider, m as PhoneMultiFactorGenerator, p as ProviderId, R as RecaptchaVerifier, aQ as SAMLAuthCredential, _ as SAMLAuthProvider, S as SignInMethod, T as TotpMultiFactorGenerator, n as TotpSecret, $ as TwitterAuthProvider, aK as UserImpl, aC as _assert, aJ as _castAuth, ax as _fail, aN as _generateEventId, aM as _getClientVersion, aE as _getInstance, aG as _getRedirectResult, aH as _overrideRedirectResult, aF as _persistenceKeyName, a7 as applyActionCode, x as beforeAuthStateChanged, b as browserLocalPersistence, k as browserPopupRedirectResolver, a as browserSessionPersistence, a8 as checkActionCode, a6 as confirmPasswordReset, K as connectAuthEmulator, aa as createUserWithEmailAndPassword, G as debugErrorMap, E as deleteUser, af as fetchSignInMethodsForEmail, aq as getAdditionalUserInfo, o as getAuth, an as getIdToken, ao as getIdTokenResult, as as getMultiFactorResolver, j as getRedirectResult, U as inMemoryPersistence, i as indexedDBLocalPersistence, J as initializeAuth, t as initializeRecaptchaConfig, ad as isSignInWithEmailLink, a2 as linkWithCredential, l as linkWithPhoneNumber, d as linkWithPopup, g as linkWithRedirect, at as multiFactor, y as onAuthStateChanged, w as onIdTokenChanged, aj as parseActionCodeURL, H as prodErrorMap, a3 as reauthenticateWithCredential, r as reauthenticateWithPhoneNumber, e as reauthenticateWithPopup, h as reauthenticateWithRedirect, ar as reload, D as revokeAccessToken, ag as sendEmailVerification, a5 as sendPasswordResetEmail, ac as sendSignInLinkToEmail, q as setPersistence, a0 as signInAnonymously, a1 as signInWithCredential, a4 as signInWithCustomToken, ab as signInWithEmailAndPassword, ae as signInWithEmailLink, s as signInWithPhoneNumber, c as signInWithPopup, f as signInWithRedirect, C as signOut, ap as unlink, B as updateCurrentUser, al as updateEmail, am as updatePassword, u as updatePhoneNumber, ak as updateProfile, z as useDeviceLanguage, v as validatePassword, ah as verifyBeforeUpdateEmail, a9 as verifyPasswordResetCode } from './index-68602d24.js';\nimport { querystringDecode } from '@firebase/util';\nimport '@firebase/app';\nimport '@firebase/logger';\nimport 'tslib';\nimport '@firebase/component';\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction _cordovaWindow() {\n  return window;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * How long to wait after the app comes back into focus before concluding that\r\n * the user closed the sign in tab.\r\n */\nconst REDIRECT_TIMEOUT_MS = 2000;\n/**\r\n * Generates the URL for the OAuth handler.\r\n */\nasync function _generateHandlerUrl(auth, event, provider) {\n  var _a;\n  // Get the cordova plugins\n  const {\n    BuildInfo\n  } = _cordovaWindow();\n  debugAssert(event.sessionId, 'AuthEvent did not contain a session ID');\n  const sessionDigest = await computeSha256(event.sessionId);\n  const additionalParams = {};\n  if (_isIOS()) {\n    // iOS app identifier\n    additionalParams['ibi'] = BuildInfo.packageName;\n  } else if (_isAndroid()) {\n    // Android app identifier\n    additionalParams['apn'] = BuildInfo.packageName;\n  } else {\n    _fail(auth, \"operation-not-supported-in-this-environment\" /* AuthErrorCode.OPERATION_NOT_SUPPORTED */);\n  }\n  // Add the display name if available\n  if (BuildInfo.displayName) {\n    additionalParams['appDisplayName'] = BuildInfo.displayName;\n  }\n  // Attached the hashed session ID\n  additionalParams['sessionId'] = sessionDigest;\n  return _getRedirectUrl(auth, provider, event.type, undefined, (_a = event.eventId) !== null && _a !== void 0 ? _a : undefined, additionalParams);\n}\n/**\r\n * Validates that this app is valid for this project configuration\r\n */\nasync function _validateOrigin(auth) {\n  const {\n    BuildInfo\n  } = _cordovaWindow();\n  const request = {};\n  if (_isIOS()) {\n    request.iosBundleId = BuildInfo.packageName;\n  } else if (_isAndroid()) {\n    request.androidPackageName = BuildInfo.packageName;\n  } else {\n    _fail(auth, \"operation-not-supported-in-this-environment\" /* AuthErrorCode.OPERATION_NOT_SUPPORTED */);\n  }\n  // Will fail automatically if package name is not authorized\n  await _getProjectConfig(auth, request);\n}\nfunction _performRedirect(handlerUrl) {\n  // Get the cordova plugins\n  const {\n    cordova\n  } = _cordovaWindow();\n  return new Promise(resolve => {\n    cordova.plugins.browsertab.isAvailable(browserTabIsAvailable => {\n      let iabRef = null;\n      if (browserTabIsAvailable) {\n        cordova.plugins.browsertab.openUrl(handlerUrl);\n      } else {\n        // TODO: Return the inappbrowser ref that's returned from the open call\n        iabRef = cordova.InAppBrowser.open(handlerUrl, _isIOS7Or8() ? '_blank' : '_system', 'location=yes');\n      }\n      resolve(iabRef);\n    });\n  });\n}\n/**\r\n * This function waits for app activity to be seen before resolving. It does\r\n * this by attaching listeners to various dom events. Once the app is determined\r\n * to be visible, this promise resolves. AFTER that resolution, the listeners\r\n * are detached and any browser tabs left open will be closed.\r\n */\nasync function _waitForAppResume(auth, eventListener, iabRef) {\n  // Get the cordova plugins\n  const {\n    cordova\n  } = _cordovaWindow();\n  let cleanup = () => {};\n  try {\n    await new Promise((resolve, reject) => {\n      let onCloseTimer = null;\n      // DEFINE ALL THE CALLBACKS =====\n      function authEventSeen() {\n        var _a;\n        // Auth event was detected. Resolve this promise and close the extra\n        // window if it's still open.\n        resolve();\n        const closeBrowserTab = (_a = cordova.plugins.browsertab) === null || _a === void 0 ? void 0 : _a.close;\n        if (typeof closeBrowserTab === 'function') {\n          closeBrowserTab();\n        }\n        // Close inappbrowser embedded webview in iOS7 and 8 case if still\n        // open.\n        if (typeof (iabRef === null || iabRef === void 0 ? void 0 : iabRef.close) === 'function') {\n          iabRef.close();\n        }\n      }\n      function resumed() {\n        if (onCloseTimer) {\n          // This code already ran; do not rerun.\n          return;\n        }\n        onCloseTimer = window.setTimeout(() => {\n          // Wait two seconds after resume then reject.\n          reject(_createError(auth, \"redirect-cancelled-by-user\" /* AuthErrorCode.REDIRECT_CANCELLED_BY_USER */));\n        }, REDIRECT_TIMEOUT_MS);\n      }\n      function visibilityChanged() {\n        if ((document === null || document === void 0 ? void 0 : document.visibilityState) === 'visible') {\n          resumed();\n        }\n      }\n      // ATTACH ALL THE LISTENERS =====\n      // Listen for the auth event\n      eventListener.addPassiveListener(authEventSeen);\n      // Listen for resume and visibility events\n      document.addEventListener('resume', resumed, false);\n      if (_isAndroid()) {\n        document.addEventListener('visibilitychange', visibilityChanged, false);\n      }\n      // SETUP THE CLEANUP FUNCTION =====\n      cleanup = () => {\n        eventListener.removePassiveListener(authEventSeen);\n        document.removeEventListener('resume', resumed, false);\n        document.removeEventListener('visibilitychange', visibilityChanged, false);\n        if (onCloseTimer) {\n          window.clearTimeout(onCloseTimer);\n        }\n      };\n    });\n  } finally {\n    cleanup();\n  }\n}\n/**\r\n * Checks the configuration of the Cordova environment. This has no side effect\r\n * if the configuration is correct; otherwise it throws an error with the\r\n * missing plugin.\r\n */\nfunction _checkCordovaConfiguration(auth) {\n  var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n  const win = _cordovaWindow();\n  // Check all dependencies installed.\n  // https://github.com/nordnet/cordova-universal-links-plugin\n  // Note that cordova-universal-links-plugin has been abandoned.\n  // A fork with latest fixes is available at:\n  // https://www.npmjs.com/package/cordova-universal-links-plugin-fix\n  _assert(typeof ((_a = win === null || win === void 0 ? void 0 : win.universalLinks) === null || _a === void 0 ? void 0 : _a.subscribe) === 'function', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\n    missingPlugin: 'cordova-universal-links-plugin-fix'\n  });\n  // https://www.npmjs.com/package/cordova-plugin-buildinfo\n  _assert(typeof ((_b = win === null || win === void 0 ? void 0 : win.BuildInfo) === null || _b === void 0 ? void 0 : _b.packageName) !== 'undefined', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\n    missingPlugin: 'cordova-plugin-buildInfo'\n  });\n  // https://github.com/google/cordova-plugin-browsertab\n  _assert(typeof ((_e = (_d = (_c = win === null || win === void 0 ? void 0 : win.cordova) === null || _c === void 0 ? void 0 : _c.plugins) === null || _d === void 0 ? void 0 : _d.browsertab) === null || _e === void 0 ? void 0 : _e.openUrl) === 'function', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\n    missingPlugin: 'cordova-plugin-browsertab'\n  });\n  _assert(typeof ((_h = (_g = (_f = win === null || win === void 0 ? void 0 : win.cordova) === null || _f === void 0 ? void 0 : _f.plugins) === null || _g === void 0 ? void 0 : _g.browsertab) === null || _h === void 0 ? void 0 : _h.isAvailable) === 'function', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\n    missingPlugin: 'cordova-plugin-browsertab'\n  });\n  // https://cordova.apache.org/docs/en/latest/reference/cordova-plugin-inappbrowser/\n  _assert(typeof ((_k = (_j = win === null || win === void 0 ? void 0 : win.cordova) === null || _j === void 0 ? void 0 : _j.InAppBrowser) === null || _k === void 0 ? void 0 : _k.open) === 'function', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\n    missingPlugin: 'cordova-plugin-inappbrowser'\n  });\n}\n/**\r\n * Computes the SHA-256 of a session ID. The SubtleCrypto interface is only\r\n * available in \"secure\" contexts, which covers Cordova (which is served on a file\r\n * protocol).\r\n */\nasync function computeSha256(sessionId) {\n  const bytes = stringToArrayBuffer(sessionId);\n  // TODO: For IE11 crypto has a different name and this operation comes back\n  //       as an object, not a promise. This is the old proposed standard that\n  //       is used by IE11:\n  // https://www.w3.org/TR/2013/WD-WebCryptoAPI-20130108/#cryptooperation-interface\n  const buf = await crypto.subtle.digest('SHA-256', bytes);\n  const arr = Array.from(new Uint8Array(buf));\n  return arr.map(num => num.toString(16).padStart(2, '0')).join('');\n}\nfunction stringToArrayBuffer(str) {\n  // This function is only meant to deal with an ASCII charset and makes\n  // certain simplifying assumptions.\n  debugAssert(/[0-9a-zA-Z]+/.test(str), 'Can only convert alpha-numeric strings');\n  if (typeof TextEncoder !== 'undefined') {\n    return new TextEncoder().encode(str);\n  }\n  const buff = new ArrayBuffer(str.length);\n  const view = new Uint8Array(buff);\n  for (let i = 0; i < str.length; i++) {\n    view[i] = str.charCodeAt(i);\n  }\n  return view;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst SESSION_ID_LENGTH = 20;\n/** Custom AuthEventManager that adds passive listeners to events */\nclass CordovaAuthEventManager extends AuthEventManager {\n  constructor() {\n    super(...arguments);\n    this.passiveListeners = new Set();\n    this.initPromise = new Promise(resolve => {\n      this.resolveInitialized = resolve;\n    });\n  }\n  addPassiveListener(cb) {\n    this.passiveListeners.add(cb);\n  }\n  removePassiveListener(cb) {\n    this.passiveListeners.delete(cb);\n  }\n  // In a Cordova environment, this manager can live through multiple redirect\n  // operations\n  resetRedirect() {\n    this.queuedRedirectEvent = null;\n    this.hasHandledPotentialRedirect = false;\n  }\n  /** Override the onEvent method */\n  onEvent(event) {\n    this.resolveInitialized();\n    this.passiveListeners.forEach(cb => cb(event));\n    return super.onEvent(event);\n  }\n  async initialized() {\n    await this.initPromise;\n  }\n}\n/**\r\n * Generates a (partial) {@link AuthEvent}.\r\n */\nfunction _generateNewEvent(auth, type, eventId = null) {\n  return {\n    type,\n    eventId,\n    urlResponse: null,\n    sessionId: generateSessionId(),\n    postBody: null,\n    tenantId: auth.tenantId,\n    error: _createError(auth, \"no-auth-event\" /* AuthErrorCode.NO_AUTH_EVENT */)\n  };\n}\nfunction _savePartialEvent(auth, event) {\n  return storage()._set(persistenceKey(auth), event);\n}\nasync function _getAndRemoveEvent(auth) {\n  const event = await storage()._get(persistenceKey(auth));\n  if (event) {\n    await storage()._remove(persistenceKey(auth));\n  }\n  return event;\n}\nfunction _eventFromPartialAndUrl(partialEvent, url) {\n  var _a, _b;\n  // Parse the deep link within the dynamic link URL.\n  const callbackUrl = _getDeepLinkFromCallback(url);\n  // Confirm it is actually a callback URL.\n  // Currently the universal link will be of this format:\n  // https://<AUTH_DOMAIN>/__/auth/callback<OAUTH_RESPONSE>\n  // This is a fake URL but is not intended to take the user anywhere\n  // and just redirect to the app.\n  if (callbackUrl.includes('/__/auth/callback')) {\n    // Check if there is an error in the URL.\n    // This mechanism is also used to pass errors back to the app:\n    // https://<AUTH_DOMAIN>/__/auth/callback?firebaseError=<STRINGIFIED_ERROR>\n    const params = searchParamsOrEmpty(callbackUrl);\n    // Get the error object corresponding to the stringified error if found.\n    const errorObject = params['firebaseError'] ? parseJsonOrNull(decodeURIComponent(params['firebaseError'])) : null;\n    const code = (_b = (_a = errorObject === null || errorObject === void 0 ? void 0 : errorObject['code']) === null || _a === void 0 ? void 0 : _a.split('auth/')) === null || _b === void 0 ? void 0 : _b[1];\n    const error = code ? _createError(code) : null;\n    if (error) {\n      return {\n        type: partialEvent.type,\n        eventId: partialEvent.eventId,\n        tenantId: partialEvent.tenantId,\n        error,\n        urlResponse: null,\n        sessionId: null,\n        postBody: null\n      };\n    } else {\n      return {\n        type: partialEvent.type,\n        eventId: partialEvent.eventId,\n        tenantId: partialEvent.tenantId,\n        sessionId: partialEvent.sessionId,\n        urlResponse: callbackUrl,\n        postBody: null\n      };\n    }\n  }\n  return null;\n}\nfunction generateSessionId() {\n  const chars = [];\n  const allowedChars = '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\n  for (let i = 0; i < SESSION_ID_LENGTH; i++) {\n    const idx = Math.floor(Math.random() * allowedChars.length);\n    chars.push(allowedChars.charAt(idx));\n  }\n  return chars.join('');\n}\nfunction storage() {\n  return _getInstance(browserLocalPersistence);\n}\nfunction persistenceKey(auth) {\n  return _persistenceKeyName(\"authEvent\" /* KeyName.AUTH_EVENT */, auth.config.apiKey, auth.name);\n}\nfunction parseJsonOrNull(json) {\n  try {\n    return JSON.parse(json);\n  } catch (e) {\n    return null;\n  }\n}\n// Exported for testing\nfunction _getDeepLinkFromCallback(url) {\n  const params = searchParamsOrEmpty(url);\n  const link = params['link'] ? decodeURIComponent(params['link']) : undefined;\n  // Double link case (automatic redirect)\n  const doubleDeepLink = searchParamsOrEmpty(link)['link'];\n  // iOS custom scheme links.\n  const iOSDeepLink = params['deep_link_id'] ? decodeURIComponent(params['deep_link_id']) : undefined;\n  const iOSDoubleDeepLink = searchParamsOrEmpty(iOSDeepLink)['link'];\n  return iOSDoubleDeepLink || iOSDeepLink || doubleDeepLink || link || url;\n}\n/**\r\n * Optimistically tries to get search params from a string, or else returns an\r\n * empty search params object.\r\n */\nfunction searchParamsOrEmpty(url) {\n  if (!(url === null || url === void 0 ? void 0 : url.includes('?'))) {\n    return {};\n  }\n  const [_, ...rest] = url.split('?');\n  return querystringDecode(rest.join('?'));\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * How long to wait for the initial auth event before concluding no\r\n * redirect pending\r\n */\nconst INITIAL_EVENT_TIMEOUT_MS = 500;\nclass CordovaPopupRedirectResolver {\n  constructor() {\n    this._redirectPersistence = browserSessionPersistence;\n    this._shouldInitProactively = true; // This is lightweight for Cordova\n    this.eventManagers = new Map();\n    this.originValidationPromises = {};\n    this._completeRedirectFn = _getRedirectResult;\n    this._overrideRedirectResult = _overrideRedirectResult;\n  }\n  async _initialize(auth) {\n    const key = auth._key();\n    let manager = this.eventManagers.get(key);\n    if (!manager) {\n      manager = new CordovaAuthEventManager(auth);\n      this.eventManagers.set(key, manager);\n      this.attachCallbackListeners(auth, manager);\n    }\n    return manager;\n  }\n  _openPopup(auth) {\n    _fail(auth, \"operation-not-supported-in-this-environment\" /* AuthErrorCode.OPERATION_NOT_SUPPORTED */);\n  }\n  async _openRedirect(auth, provider, authType, eventId) {\n    _checkCordovaConfiguration(auth);\n    const manager = await this._initialize(auth);\n    await manager.initialized();\n    // Reset the persisted redirect states. This does not matter on Web where\n    // the redirect always blows away application state entirely. On Cordova,\n    // the app maintains control flow through the redirect.\n    manager.resetRedirect();\n    _clearRedirectOutcomes();\n    await this._originValidation(auth);\n    const event = _generateNewEvent(auth, authType, eventId);\n    await _savePartialEvent(auth, event);\n    const url = await _generateHandlerUrl(auth, event, provider);\n    const iabRef = await _performRedirect(url);\n    return _waitForAppResume(auth, manager, iabRef);\n  }\n  _isIframeWebStorageSupported(_auth, _cb) {\n    throw new Error('Method not implemented.');\n  }\n  _originValidation(auth) {\n    const key = auth._key();\n    if (!this.originValidationPromises[key]) {\n      this.originValidationPromises[key] = _validateOrigin(auth);\n    }\n    return this.originValidationPromises[key];\n  }\n  attachCallbackListeners(auth, manager) {\n    // Get the global plugins\n    const {\n      universalLinks,\n      handleOpenURL,\n      BuildInfo\n    } = _cordovaWindow();\n    const noEventTimeout = setTimeout(async () => {\n      // We didn't see that initial event. Clear any pending object and\n      // dispatch no event\n      await _getAndRemoveEvent(auth);\n      manager.onEvent(generateNoEvent());\n    }, INITIAL_EVENT_TIMEOUT_MS);\n    const universalLinksCb = async eventData => {\n      // We have an event so we can clear the no event timeout\n      clearTimeout(noEventTimeout);\n      const partialEvent = await _getAndRemoveEvent(auth);\n      let finalEvent = null;\n      if (partialEvent && (eventData === null || eventData === void 0 ? void 0 : eventData['url'])) {\n        finalEvent = _eventFromPartialAndUrl(partialEvent, eventData['url']);\n      }\n      // If finalEvent is never filled, trigger with no event\n      manager.onEvent(finalEvent || generateNoEvent());\n    };\n    // Universal links subscriber doesn't exist for iOS, so we need to check\n    if (typeof universalLinks !== 'undefined' && typeof universalLinks.subscribe === 'function') {\n      universalLinks.subscribe(null, universalLinksCb);\n    }\n    // iOS 7 or 8 custom URL schemes.\n    // This is also the current default behavior for iOS 9+.\n    // For this to work, cordova-plugin-customurlscheme needs to be installed.\n    // https://github.com/EddyVerbruggen/Custom-URL-scheme\n    // Do not overwrite the existing developer's URL handler.\n    const existingHandleOpenURL = handleOpenURL;\n    const packagePrefix = `${BuildInfo.packageName.toLowerCase()}://`;\n    _cordovaWindow().handleOpenURL = async url => {\n      if (url.toLowerCase().startsWith(packagePrefix)) {\n        // We want this intentionally to float\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        universalLinksCb({\n          url\n        });\n      }\n      // Call the developer's handler if it is present.\n      if (typeof existingHandleOpenURL === 'function') {\n        try {\n          existingHandleOpenURL(url);\n        } catch (e) {\n          // This is a developer error. Don't stop the flow of the SDK.\n          console.error(e);\n        }\n      }\n    };\n  }\n}\n/**\r\n * An implementation of {@link PopupRedirectResolver} suitable for Cordova\r\n * based applications.\r\n *\r\n * @public\r\n */\nconst cordovaPopupRedirectResolver = CordovaPopupRedirectResolver;\nfunction generateNoEvent() {\n  return {\n    type: \"unknown\" /* AuthEventType.UNKNOWN */,\n    eventId: null,\n    sessionId: null,\n    urlResponse: null,\n    postBody: null,\n    tenantId: null,\n    error: _createError(\"no-auth-event\" /* AuthErrorCode.NO_AUTH_EVENT */)\n  };\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n// This function should only be called by frameworks (e.g. FirebaseUI-web) to log their usage.\n// It is not intended for direct use by developer apps. NO jsdoc here to intentionally leave it out\n// of autogenerated documentation pages to reduce accidental misuse.\nfunction addFrameworkForLogging(auth, framework) {\n  _castAuth(auth)._logFramework(framework);\n}\nexport { addFrameworkForLogging, cordovaPopupRedirectResolver };\n", "import firebase from '@firebase/app-compat';\nimport * as exp from '@firebase/auth/internal';\nimport { Component } from '@firebase/component';\nimport { isBrowserExtension, getUA, isReactNative, isNode, isIndexedDBAvailable, isIE, FirebaseError } from '@firebase/util';\nvar name = \"@firebase/auth-compat\";\nvar version = \"0.5.14\";\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst CORDOVA_ONDEVICEREADY_TIMEOUT_MS = 1000;\nfunction _getCurrentScheme() {\n  var _a;\n  return ((_a = self === null || self === void 0 ? void 0 : self.location) === null || _a === void 0 ? void 0 : _a.protocol) || null;\n}\n/**\r\n * @return {boolean} Whether the current environment is http or https.\r\n */\nfunction _isHttpOrHttps() {\n  return _getCurrentScheme() === 'http:' || _getCurrentScheme() === 'https:';\n}\n/**\r\n * @param {?string=} ua The user agent.\r\n * @return {boolean} Whether the app is rendered in a mobile iOS or Android\r\n *     Cordova environment.\r\n */\nfunction _isAndroidOrIosCordovaScheme(ua = getUA()) {\n  return !!((_getCurrentScheme() === 'file:' || _getCurrentScheme() === 'ionic:' || _getCurrentScheme() === 'capacitor:') && ua.toLowerCase().match(/iphone|ipad|ipod|android/));\n}\n/**\r\n * @return {boolean} Whether the environment is a native environment, where\r\n *     CORS checks do not apply.\r\n */\nfunction _isNativeEnvironment() {\n  return isReactNative() || isNode();\n}\n/**\r\n * Checks whether the user agent is IE11.\r\n * @return {boolean} True if it is IE11.\r\n */\nfunction _isIe11() {\n  return isIE() && (document === null || document === void 0 ? void 0 : document.documentMode) === 11;\n}\n/**\r\n * Checks whether the user agent is Edge.\r\n * @param {string} userAgent The browser user agent string.\r\n * @return {boolean} True if it is Edge.\r\n */\nfunction _isEdge(ua = getUA()) {\n  return /Edge\\/\\d+/.test(ua);\n}\n/**\r\n * @param {?string=} opt_userAgent The navigator user agent.\r\n * @return {boolean} Whether local storage is not synchronized between an iframe\r\n *     and a popup of the same domain.\r\n */\nfunction _isLocalStorageNotSynchronized(ua = getUA()) {\n  return _isIe11() || _isEdge(ua);\n}\n/** @return {boolean} Whether web storage is supported. */\nfunction _isWebStorageSupported() {\n  try {\n    const storage = self.localStorage;\n    const key = exp._generateEventId();\n    if (storage) {\n      // setItem will throw an exception if we cannot access WebStorage (e.g.,\n      // Safari in private mode).\n      storage['setItem'](key, '1');\n      storage['removeItem'](key);\n      // For browsers where iframe web storage does not synchronize with a popup\n      // of the same domain, indexedDB is used for persistent storage. These\n      // browsers include IE11 and Edge.\n      // Make sure it is supported (IE11 and Edge private mode does not support\n      // that).\n      if (_isLocalStorageNotSynchronized()) {\n        // In such browsers, if indexedDB is not supported, an iframe cannot be\n        // notified of the popup sign in result.\n        return isIndexedDBAvailable();\n      }\n      return true;\n    }\n  } catch (e) {\n    // localStorage is not available from a worker. Test availability of\n    // indexedDB.\n    return _isWorker() && isIndexedDBAvailable();\n  }\n  return false;\n}\n/**\r\n * @param {?Object=} global The optional global scope.\r\n * @return {boolean} Whether current environment is a worker.\r\n */\nfunction _isWorker() {\n  // WorkerGlobalScope only defined in worker environment.\n  return typeof global !== 'undefined' && 'WorkerGlobalScope' in global && 'importScripts' in global;\n}\nfunction _isPopupRedirectSupported() {\n  return (_isHttpOrHttps() || isBrowserExtension() || _isAndroidOrIosCordovaScheme()) &&\n  // React Native with remote debugging reports its location.protocol as\n  // http.\n  !_isNativeEnvironment() &&\n  // Local storage has to be supported for browser popup and redirect\n  // operations to work.\n  _isWebStorageSupported() &&\n  // DOM, popups and redirects are not supported within a worker.\n  !_isWorker();\n}\n/** Quick check that indicates the platform *may* be Cordova */\nfunction _isLikelyCordova() {\n  return _isAndroidOrIosCordovaScheme() && typeof document !== 'undefined';\n}\nasync function _isCordova() {\n  if (!_isLikelyCordova()) {\n    return false;\n  }\n  return new Promise(resolve => {\n    const timeoutId = setTimeout(() => {\n      // We've waited long enough; the telltale Cordova event didn't happen\n      resolve(false);\n    }, CORDOVA_ONDEVICEREADY_TIMEOUT_MS);\n    document.addEventListener('deviceready', () => {\n      clearTimeout(timeoutId);\n      resolve(true);\n    });\n  });\n}\nfunction _getSelfWindow() {\n  return typeof window !== 'undefined' ? window : null;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst Persistence = {\n  LOCAL: 'local',\n  NONE: 'none',\n  SESSION: 'session'\n};\nconst _assert$3 = exp._assert;\nconst PERSISTENCE_KEY = 'persistence';\n/**\r\n * Validates that an argument is a valid persistence value. If an invalid type\r\n * is specified, an error is thrown synchronously.\r\n */\nfunction _validatePersistenceArgument(auth, persistence) {\n  _assert$3(Object.values(Persistence).includes(persistence), auth, \"invalid-persistence-type\" /* exp.AuthErrorCode.INVALID_PERSISTENCE */);\n  // Validate if the specified type is supported in the current environment.\n  if (isReactNative()) {\n    // This is only supported in a browser.\n    _assert$3(persistence !== Persistence.SESSION, auth, \"unsupported-persistence-type\" /* exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE */);\n    return;\n  }\n  if (isNode()) {\n    // Only none is supported in Node.js.\n    _assert$3(persistence === Persistence.NONE, auth, \"unsupported-persistence-type\" /* exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE */);\n    return;\n  }\n  if (_isWorker()) {\n    // In a worker environment, either LOCAL or NONE are supported.\n    // If indexedDB not supported and LOCAL provided, throw an error\n    _assert$3(persistence === Persistence.NONE || persistence === Persistence.LOCAL && isIndexedDBAvailable(), auth, \"unsupported-persistence-type\" /* exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE */);\n    return;\n  }\n  // This is restricted by what the browser supports.\n  _assert$3(persistence === Persistence.NONE || _isWebStorageSupported(), auth, \"unsupported-persistence-type\" /* exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE */);\n}\nasync function _savePersistenceForRedirect(auth) {\n  await auth._initializationPromise;\n  const session = getSessionStorageIfAvailable();\n  const key = exp._persistenceKeyName(PERSISTENCE_KEY, auth.config.apiKey, auth.name);\n  if (session) {\n    session.setItem(key, auth._getPersistence());\n  }\n}\nfunction _getPersistencesFromRedirect(apiKey, appName) {\n  const session = getSessionStorageIfAvailable();\n  if (!session) {\n    return [];\n  }\n  const key = exp._persistenceKeyName(PERSISTENCE_KEY, apiKey, appName);\n  const persistence = session.getItem(key);\n  switch (persistence) {\n    case Persistence.NONE:\n      return [exp.inMemoryPersistence];\n    case Persistence.LOCAL:\n      return [exp.indexedDBLocalPersistence, exp.browserSessionPersistence];\n    case Persistence.SESSION:\n      return [exp.browserSessionPersistence];\n    default:\n      return [];\n  }\n}\n/** Returns session storage, or null if the property access errors */\nfunction getSessionStorageIfAvailable() {\n  var _a;\n  try {\n    return ((_a = _getSelfWindow()) === null || _a === void 0 ? void 0 : _a.sessionStorage) || null;\n  } catch (e) {\n    return null;\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst _assert$2 = exp._assert;\n/** Platform-agnostic popup-redirect resolver */\nclass CompatPopupRedirectResolver {\n  constructor() {\n    // Create both resolvers for dynamic resolution later\n    this.browserResolver = exp._getInstance(exp.browserPopupRedirectResolver);\n    this.cordovaResolver = exp._getInstance(exp.cordovaPopupRedirectResolver);\n    // The actual resolver in use: either browserResolver or cordovaResolver.\n    this.underlyingResolver = null;\n    this._redirectPersistence = exp.browserSessionPersistence;\n    this._completeRedirectFn = exp._getRedirectResult;\n    this._overrideRedirectResult = exp._overrideRedirectResult;\n  }\n  async _initialize(auth) {\n    await this.selectUnderlyingResolver();\n    return this.assertedUnderlyingResolver._initialize(auth);\n  }\n  async _openPopup(auth, provider, authType, eventId) {\n    await this.selectUnderlyingResolver();\n    return this.assertedUnderlyingResolver._openPopup(auth, provider, authType, eventId);\n  }\n  async _openRedirect(auth, provider, authType, eventId) {\n    await this.selectUnderlyingResolver();\n    return this.assertedUnderlyingResolver._openRedirect(auth, provider, authType, eventId);\n  }\n  _isIframeWebStorageSupported(auth, cb) {\n    this.assertedUnderlyingResolver._isIframeWebStorageSupported(auth, cb);\n  }\n  _originValidation(auth) {\n    return this.assertedUnderlyingResolver._originValidation(auth);\n  }\n  get _shouldInitProactively() {\n    return _isLikelyCordova() || this.browserResolver._shouldInitProactively;\n  }\n  get assertedUnderlyingResolver() {\n    _assert$2(this.underlyingResolver, \"internal-error\" /* exp.AuthErrorCode.INTERNAL_ERROR */);\n    return this.underlyingResolver;\n  }\n  async selectUnderlyingResolver() {\n    if (this.underlyingResolver) {\n      return;\n    }\n    // We haven't yet determined whether or not we're in Cordova; go ahead\n    // and determine that state now.\n    const isCordova = await _isCordova();\n    this.underlyingResolver = isCordova ? this.cordovaResolver : this.browserResolver;\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction unwrap(object) {\n  return object.unwrap();\n}\nfunction wrapped(object) {\n  return object.wrapped();\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction credentialFromResponse(userCredential) {\n  return credentialFromObject(userCredential);\n}\nfunction attachExtraErrorFields(auth, e) {\n  var _a;\n  // The response contains all fields from the server which may or may not\n  // actually match the underlying type\n  const response = (_a = e.customData) === null || _a === void 0 ? void 0 : _a._tokenResponse;\n  if ((e === null || e === void 0 ? void 0 : e.code) === 'auth/multi-factor-auth-required') {\n    const mfaErr = e;\n    mfaErr.resolver = new MultiFactorResolver(auth, exp.getMultiFactorResolver(auth, e));\n  } else if (response) {\n    const credential = credentialFromObject(e);\n    const credErr = e;\n    if (credential) {\n      credErr.credential = credential;\n      credErr.tenantId = response.tenantId || undefined;\n      credErr.email = response.email || undefined;\n      credErr.phoneNumber = response.phoneNumber || undefined;\n    }\n  }\n}\nfunction credentialFromObject(object) {\n  const {\n    _tokenResponse\n  } = object instanceof FirebaseError ? object.customData : object;\n  if (!_tokenResponse) {\n    return null;\n  }\n  // Handle phone Auth credential responses, as they have a different format\n  // from other backend responses (i.e. no providerId). This is also only the\n  // case for user credentials (does not work for errors).\n  if (!(object instanceof FirebaseError)) {\n    if ('temporaryProof' in _tokenResponse && 'phoneNumber' in _tokenResponse) {\n      return exp.PhoneAuthProvider.credentialFromResult(object);\n    }\n  }\n  const providerId = _tokenResponse.providerId;\n  // Email and password is not supported as there is no situation where the\n  // server would return the password to the client.\n  if (!providerId || providerId === exp.ProviderId.PASSWORD) {\n    return null;\n  }\n  let provider;\n  switch (providerId) {\n    case exp.ProviderId.GOOGLE:\n      provider = exp.GoogleAuthProvider;\n      break;\n    case exp.ProviderId.FACEBOOK:\n      provider = exp.FacebookAuthProvider;\n      break;\n    case exp.ProviderId.GITHUB:\n      provider = exp.GithubAuthProvider;\n      break;\n    case exp.ProviderId.TWITTER:\n      provider = exp.TwitterAuthProvider;\n      break;\n    default:\n      const {\n        oauthIdToken,\n        oauthAccessToken,\n        oauthTokenSecret,\n        pendingToken,\n        nonce\n      } = _tokenResponse;\n      if (!oauthAccessToken && !oauthTokenSecret && !oauthIdToken && !pendingToken) {\n        return null;\n      }\n      // TODO(avolkovi): uncomment this and get it working with SAML & OIDC\n      if (pendingToken) {\n        if (providerId.startsWith('saml.')) {\n          return exp.SAMLAuthCredential._create(providerId, pendingToken);\n        } else {\n          // OIDC and non-default providers excluding Twitter.\n          return exp.OAuthCredential._fromParams({\n            providerId,\n            signInMethod: providerId,\n            pendingToken,\n            idToken: oauthIdToken,\n            accessToken: oauthAccessToken\n          });\n        }\n      }\n      return new exp.OAuthProvider(providerId).credential({\n        idToken: oauthIdToken,\n        accessToken: oauthAccessToken,\n        rawNonce: nonce\n      });\n  }\n  return object instanceof FirebaseError ? provider.credentialFromError(object) : provider.credentialFromResult(object);\n}\nfunction convertCredential(auth, credentialPromise) {\n  return credentialPromise.catch(e => {\n    if (e instanceof FirebaseError) {\n      attachExtraErrorFields(auth, e);\n    }\n    throw e;\n  }).then(credential => {\n    const operationType = credential.operationType;\n    const user = credential.user;\n    return {\n      operationType,\n      credential: credentialFromResponse(credential),\n      additionalUserInfo: exp.getAdditionalUserInfo(credential),\n      user: User.getOrCreate(user)\n    };\n  });\n}\nasync function convertConfirmationResult(auth, confirmationResultPromise) {\n  const confirmationResultExp = await confirmationResultPromise;\n  return {\n    verificationId: confirmationResultExp.verificationId,\n    confirm: verificationCode => convertCredential(auth, confirmationResultExp.confirm(verificationCode))\n  };\n}\nclass MultiFactorResolver {\n  constructor(auth, resolver) {\n    this.resolver = resolver;\n    this.auth = wrapped(auth);\n  }\n  get session() {\n    return this.resolver.session;\n  }\n  get hints() {\n    return this.resolver.hints;\n  }\n  resolveSignIn(assertion) {\n    return convertCredential(unwrap(this.auth), this.resolver.resolveSignIn(assertion));\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass User {\n  constructor(_delegate) {\n    this._delegate = _delegate;\n    this.multiFactor = exp.multiFactor(_delegate);\n  }\n  static getOrCreate(user) {\n    if (!User.USER_MAP.has(user)) {\n      User.USER_MAP.set(user, new User(user));\n    }\n    return User.USER_MAP.get(user);\n  }\n  delete() {\n    return this._delegate.delete();\n  }\n  reload() {\n    return this._delegate.reload();\n  }\n  toJSON() {\n    return this._delegate.toJSON();\n  }\n  getIdTokenResult(forceRefresh) {\n    return this._delegate.getIdTokenResult(forceRefresh);\n  }\n  getIdToken(forceRefresh) {\n    return this._delegate.getIdToken(forceRefresh);\n  }\n  linkAndRetrieveDataWithCredential(credential) {\n    return this.linkWithCredential(credential);\n  }\n  async linkWithCredential(credential) {\n    return convertCredential(this.auth, exp.linkWithCredential(this._delegate, credential));\n  }\n  async linkWithPhoneNumber(phoneNumber, applicationVerifier) {\n    return convertConfirmationResult(this.auth, exp.linkWithPhoneNumber(this._delegate, phoneNumber, applicationVerifier));\n  }\n  async linkWithPopup(provider) {\n    return convertCredential(this.auth, exp.linkWithPopup(this._delegate, provider, CompatPopupRedirectResolver));\n  }\n  async linkWithRedirect(provider) {\n    await _savePersistenceForRedirect(exp._castAuth(this.auth));\n    return exp.linkWithRedirect(this._delegate, provider, CompatPopupRedirectResolver);\n  }\n  reauthenticateAndRetrieveDataWithCredential(credential) {\n    return this.reauthenticateWithCredential(credential);\n  }\n  async reauthenticateWithCredential(credential) {\n    return convertCredential(this.auth, exp.reauthenticateWithCredential(this._delegate, credential));\n  }\n  reauthenticateWithPhoneNumber(phoneNumber, applicationVerifier) {\n    return convertConfirmationResult(this.auth, exp.reauthenticateWithPhoneNumber(this._delegate, phoneNumber, applicationVerifier));\n  }\n  reauthenticateWithPopup(provider) {\n    return convertCredential(this.auth, exp.reauthenticateWithPopup(this._delegate, provider, CompatPopupRedirectResolver));\n  }\n  async reauthenticateWithRedirect(provider) {\n    await _savePersistenceForRedirect(exp._castAuth(this.auth));\n    return exp.reauthenticateWithRedirect(this._delegate, provider, CompatPopupRedirectResolver);\n  }\n  sendEmailVerification(actionCodeSettings) {\n    return exp.sendEmailVerification(this._delegate, actionCodeSettings);\n  }\n  async unlink(providerId) {\n    await exp.unlink(this._delegate, providerId);\n    return this;\n  }\n  updateEmail(newEmail) {\n    return exp.updateEmail(this._delegate, newEmail);\n  }\n  updatePassword(newPassword) {\n    return exp.updatePassword(this._delegate, newPassword);\n  }\n  updatePhoneNumber(phoneCredential) {\n    return exp.updatePhoneNumber(this._delegate, phoneCredential);\n  }\n  updateProfile(profile) {\n    return exp.updateProfile(this._delegate, profile);\n  }\n  verifyBeforeUpdateEmail(newEmail, actionCodeSettings) {\n    return exp.verifyBeforeUpdateEmail(this._delegate, newEmail, actionCodeSettings);\n  }\n  get emailVerified() {\n    return this._delegate.emailVerified;\n  }\n  get isAnonymous() {\n    return this._delegate.isAnonymous;\n  }\n  get metadata() {\n    return this._delegate.metadata;\n  }\n  get phoneNumber() {\n    return this._delegate.phoneNumber;\n  }\n  get providerData() {\n    return this._delegate.providerData;\n  }\n  get refreshToken() {\n    return this._delegate.refreshToken;\n  }\n  get tenantId() {\n    return this._delegate.tenantId;\n  }\n  get displayName() {\n    return this._delegate.displayName;\n  }\n  get email() {\n    return this._delegate.email;\n  }\n  get photoURL() {\n    return this._delegate.photoURL;\n  }\n  get providerId() {\n    return this._delegate.providerId;\n  }\n  get uid() {\n    return this._delegate.uid;\n  }\n  get auth() {\n    return this._delegate.auth;\n  }\n}\n// Maintain a map so that there's always a 1:1 mapping between new User and\n// legacy compat users\nUser.USER_MAP = new WeakMap();\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst _assert$1 = exp._assert;\nclass Auth {\n  constructor(app, provider) {\n    this.app = app;\n    if (provider.isInitialized()) {\n      this._delegate = provider.getImmediate();\n      this.linkUnderlyingAuth();\n      return;\n    }\n    const {\n      apiKey\n    } = app.options;\n    // TODO: platform needs to be determined using heuristics\n    _assert$1(apiKey, \"invalid-api-key\" /* exp.AuthErrorCode.INVALID_API_KEY */, {\n      appName: app.name\n    });\n    // TODO: platform needs to be determined using heuristics\n    _assert$1(apiKey, \"invalid-api-key\" /* exp.AuthErrorCode.INVALID_API_KEY */, {\n      appName: app.name\n    });\n    // Only use a popup/redirect resolver in browser environments\n    const resolver = typeof window !== 'undefined' ? CompatPopupRedirectResolver : undefined;\n    this._delegate = provider.initialize({\n      options: {\n        persistence: buildPersistenceHierarchy(apiKey, app.name),\n        popupRedirectResolver: resolver\n      }\n    });\n    this._delegate._updateErrorMap(exp.debugErrorMap);\n    this.linkUnderlyingAuth();\n  }\n  get emulatorConfig() {\n    return this._delegate.emulatorConfig;\n  }\n  get currentUser() {\n    if (!this._delegate.currentUser) {\n      return null;\n    }\n    return User.getOrCreate(this._delegate.currentUser);\n  }\n  get languageCode() {\n    return this._delegate.languageCode;\n  }\n  set languageCode(languageCode) {\n    this._delegate.languageCode = languageCode;\n  }\n  get settings() {\n    return this._delegate.settings;\n  }\n  get tenantId() {\n    return this._delegate.tenantId;\n  }\n  set tenantId(tid) {\n    this._delegate.tenantId = tid;\n  }\n  useDeviceLanguage() {\n    this._delegate.useDeviceLanguage();\n  }\n  signOut() {\n    return this._delegate.signOut();\n  }\n  useEmulator(url, options) {\n    exp.connectAuthEmulator(this._delegate, url, options);\n  }\n  applyActionCode(code) {\n    return exp.applyActionCode(this._delegate, code);\n  }\n  checkActionCode(code) {\n    return exp.checkActionCode(this._delegate, code);\n  }\n  confirmPasswordReset(code, newPassword) {\n    return exp.confirmPasswordReset(this._delegate, code, newPassword);\n  }\n  async createUserWithEmailAndPassword(email, password) {\n    return convertCredential(this._delegate, exp.createUserWithEmailAndPassword(this._delegate, email, password));\n  }\n  fetchProvidersForEmail(email) {\n    return this.fetchSignInMethodsForEmail(email);\n  }\n  fetchSignInMethodsForEmail(email) {\n    return exp.fetchSignInMethodsForEmail(this._delegate, email);\n  }\n  isSignInWithEmailLink(emailLink) {\n    return exp.isSignInWithEmailLink(this._delegate, emailLink);\n  }\n  async getRedirectResult() {\n    _assert$1(_isPopupRedirectSupported(), this._delegate, \"operation-not-supported-in-this-environment\" /* exp.AuthErrorCode.OPERATION_NOT_SUPPORTED */);\n    const credential = await exp.getRedirectResult(this._delegate, CompatPopupRedirectResolver);\n    if (!credential) {\n      return {\n        credential: null,\n        user: null\n      };\n    }\n    return convertCredential(this._delegate, Promise.resolve(credential));\n  }\n  // This function should only be called by frameworks (e.g. FirebaseUI-web) to log their usage.\n  // It is not intended for direct use by developer apps. NO jsdoc here to intentionally leave it\n  // out of autogenerated documentation pages to reduce accidental misuse.\n  addFrameworkForLogging(framework) {\n    exp.addFrameworkForLogging(this._delegate, framework);\n  }\n  onAuthStateChanged(nextOrObserver, errorFn, completed) {\n    const {\n      next,\n      error,\n      complete\n    } = wrapObservers(nextOrObserver, errorFn, completed);\n    return this._delegate.onAuthStateChanged(next, error, complete);\n  }\n  onIdTokenChanged(nextOrObserver, errorFn, completed) {\n    const {\n      next,\n      error,\n      complete\n    } = wrapObservers(nextOrObserver, errorFn, completed);\n    return this._delegate.onIdTokenChanged(next, error, complete);\n  }\n  sendSignInLinkToEmail(email, actionCodeSettings) {\n    return exp.sendSignInLinkToEmail(this._delegate, email, actionCodeSettings);\n  }\n  sendPasswordResetEmail(email, actionCodeSettings) {\n    return exp.sendPasswordResetEmail(this._delegate, email, actionCodeSettings || undefined);\n  }\n  async setPersistence(persistence) {\n    _validatePersistenceArgument(this._delegate, persistence);\n    let converted;\n    switch (persistence) {\n      case Persistence.SESSION:\n        converted = exp.browserSessionPersistence;\n        break;\n      case Persistence.LOCAL:\n        // Not using isIndexedDBAvailable() since it only checks if indexedDB is defined.\n        const isIndexedDBFullySupported = await exp._getInstance(exp.indexedDBLocalPersistence)._isAvailable();\n        converted = isIndexedDBFullySupported ? exp.indexedDBLocalPersistence : exp.browserLocalPersistence;\n        break;\n      case Persistence.NONE:\n        converted = exp.inMemoryPersistence;\n        break;\n      default:\n        return exp._fail(\"argument-error\" /* exp.AuthErrorCode.ARGUMENT_ERROR */, {\n          appName: this._delegate.name\n        });\n    }\n    return this._delegate.setPersistence(converted);\n  }\n  signInAndRetrieveDataWithCredential(credential) {\n    return this.signInWithCredential(credential);\n  }\n  signInAnonymously() {\n    return convertCredential(this._delegate, exp.signInAnonymously(this._delegate));\n  }\n  signInWithCredential(credential) {\n    return convertCredential(this._delegate, exp.signInWithCredential(this._delegate, credential));\n  }\n  signInWithCustomToken(token) {\n    return convertCredential(this._delegate, exp.signInWithCustomToken(this._delegate, token));\n  }\n  signInWithEmailAndPassword(email, password) {\n    return convertCredential(this._delegate, exp.signInWithEmailAndPassword(this._delegate, email, password));\n  }\n  signInWithEmailLink(email, emailLink) {\n    return convertCredential(this._delegate, exp.signInWithEmailLink(this._delegate, email, emailLink));\n  }\n  signInWithPhoneNumber(phoneNumber, applicationVerifier) {\n    return convertConfirmationResult(this._delegate, exp.signInWithPhoneNumber(this._delegate, phoneNumber, applicationVerifier));\n  }\n  async signInWithPopup(provider) {\n    _assert$1(_isPopupRedirectSupported(), this._delegate, \"operation-not-supported-in-this-environment\" /* exp.AuthErrorCode.OPERATION_NOT_SUPPORTED */);\n    return convertCredential(this._delegate, exp.signInWithPopup(this._delegate, provider, CompatPopupRedirectResolver));\n  }\n  async signInWithRedirect(provider) {\n    _assert$1(_isPopupRedirectSupported(), this._delegate, \"operation-not-supported-in-this-environment\" /* exp.AuthErrorCode.OPERATION_NOT_SUPPORTED */);\n    await _savePersistenceForRedirect(this._delegate);\n    return exp.signInWithRedirect(this._delegate, provider, CompatPopupRedirectResolver);\n  }\n  updateCurrentUser(user) {\n    // remove ts-ignore once overloads are defined for exp functions to accept compat objects\n    // @ts-ignore\n    return this._delegate.updateCurrentUser(user);\n  }\n  verifyPasswordResetCode(code) {\n    return exp.verifyPasswordResetCode(this._delegate, code);\n  }\n  unwrap() {\n    return this._delegate;\n  }\n  _delete() {\n    return this._delegate._delete();\n  }\n  linkUnderlyingAuth() {\n    this._delegate.wrapped = () => this;\n  }\n}\nAuth.Persistence = Persistence;\nfunction wrapObservers(nextOrObserver, error, complete) {\n  let next = nextOrObserver;\n  if (typeof nextOrObserver !== 'function') {\n    ({\n      next,\n      error,\n      complete\n    } = nextOrObserver);\n  }\n  // We know 'next' is now a function\n  const oldNext = next;\n  const newNext = user => oldNext(user && User.getOrCreate(user));\n  return {\n    next: newNext,\n    error: error,\n    complete\n  };\n}\nfunction buildPersistenceHierarchy(apiKey, appName) {\n  // Note this is slightly different behavior: in this case, the stored\n  // persistence is checked *first* rather than last. This is because we want\n  // to prefer stored persistence type in the hierarchy. This is an empty\n  // array if window is not available or there is no pending redirect\n  const persistences = _getPersistencesFromRedirect(apiKey, appName);\n  // If \"self\" is available, add indexedDB\n  if (typeof self !== 'undefined' && !persistences.includes(exp.indexedDBLocalPersistence)) {\n    persistences.push(exp.indexedDBLocalPersistence);\n  }\n  // If \"window\" is available, add HTML Storage persistences\n  if (typeof window !== 'undefined') {\n    for (const persistence of [exp.browserLocalPersistence, exp.browserSessionPersistence]) {\n      if (!persistences.includes(persistence)) {\n        persistences.push(persistence);\n      }\n    }\n  }\n  // Add in-memory as a final fallback\n  if (!persistences.includes(exp.inMemoryPersistence)) {\n    persistences.push(exp.inMemoryPersistence);\n  }\n  return persistences;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass PhoneAuthProvider {\n  constructor() {\n    this.providerId = 'phone';\n    // TODO: remove ts-ignore when moving types from auth-types to auth-compat\n    // @ts-ignore\n    this._delegate = new exp.PhoneAuthProvider(unwrap(firebase.auth()));\n  }\n  static credential(verificationId, verificationCode) {\n    return exp.PhoneAuthProvider.credential(verificationId, verificationCode);\n  }\n  verifyPhoneNumber(phoneInfoOptions, applicationVerifier) {\n    return this._delegate.verifyPhoneNumber(\n    // The implementation matches but the types are subtly incompatible\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    phoneInfoOptions, applicationVerifier);\n  }\n  unwrap() {\n    return this._delegate;\n  }\n}\nPhoneAuthProvider.PHONE_SIGN_IN_METHOD = exp.PhoneAuthProvider.PHONE_SIGN_IN_METHOD;\nPhoneAuthProvider.PROVIDER_ID = exp.PhoneAuthProvider.PROVIDER_ID;\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst _assert = exp._assert;\nclass RecaptchaVerifier {\n  constructor(container, parameters, app = firebase.app()) {\n    var _a;\n    // API key is required for web client RPC calls.\n    _assert((_a = app.options) === null || _a === void 0 ? void 0 : _a.apiKey, \"invalid-api-key\" /* exp.AuthErrorCode.INVALID_API_KEY */, {\n      appName: app.name\n    });\n    this._delegate = new exp.RecaptchaVerifier(\n    // TODO: remove ts-ignore when moving types from auth-types to auth-compat\n    // @ts-ignore\n    app.auth(), container,\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    parameters);\n    this.type = this._delegate.type;\n  }\n  clear() {\n    this._delegate.clear();\n  }\n  render() {\n    return this._delegate.render();\n  }\n  verify() {\n    return this._delegate.verify();\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst AUTH_TYPE = 'auth-compat';\n// Create auth components to register with firebase.\n// Provides Auth public APIs.\nfunction registerAuthCompat(instance) {\n  instance.INTERNAL.registerComponent(new Component(AUTH_TYPE, container => {\n    // getImmediate for FirebaseApp will always succeed\n    const app = container.getProvider('app-compat').getImmediate();\n    const authProvider = container.getProvider('auth');\n    return new Auth(app, authProvider);\n  }, \"PUBLIC\" /* ComponentType.PUBLIC */).setServiceProps({\n    ActionCodeInfo: {\n      Operation: {\n        EMAIL_SIGNIN: exp.ActionCodeOperation.EMAIL_SIGNIN,\n        PASSWORD_RESET: exp.ActionCodeOperation.PASSWORD_RESET,\n        RECOVER_EMAIL: exp.ActionCodeOperation.RECOVER_EMAIL,\n        REVERT_SECOND_FACTOR_ADDITION: exp.ActionCodeOperation.REVERT_SECOND_FACTOR_ADDITION,\n        VERIFY_AND_CHANGE_EMAIL: exp.ActionCodeOperation.VERIFY_AND_CHANGE_EMAIL,\n        VERIFY_EMAIL: exp.ActionCodeOperation.VERIFY_EMAIL\n      }\n    },\n    EmailAuthProvider: exp.EmailAuthProvider,\n    FacebookAuthProvider: exp.FacebookAuthProvider,\n    GithubAuthProvider: exp.GithubAuthProvider,\n    GoogleAuthProvider: exp.GoogleAuthProvider,\n    OAuthProvider: exp.OAuthProvider,\n    SAMLAuthProvider: exp.SAMLAuthProvider,\n    PhoneAuthProvider: PhoneAuthProvider,\n    PhoneMultiFactorGenerator: exp.PhoneMultiFactorGenerator,\n    RecaptchaVerifier: RecaptchaVerifier,\n    TwitterAuthProvider: exp.TwitterAuthProvider,\n    Auth,\n    AuthCredential: exp.AuthCredential,\n    Error: FirebaseError\n  }).setInstantiationMode(\"LAZY\" /* InstantiationMode.LAZY */).setMultipleInstances(false));\n  instance.registerVersion(name, version);\n}\nregisterAuthCompat(firebase);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,SAAS,iBAAiB;AACxB,SAAO;AACT;AAsBA,IAAM,sBAAsB;AAI5B,SAAe,oBAAoB,MAAM,OAAO,UAAU;AAAA;AACxD,QAAI;AAEJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,eAAe;AACnB,gBAAY,MAAM,WAAW,wCAAwC;AACrE,UAAM,gBAAgB,MAAM,cAAc,MAAM,SAAS;AACzD,UAAM,mBAAmB,CAAC;AAC1B,QAAI,OAAO,GAAG;AAEZ,uBAAiB,KAAK,IAAI,UAAU;AAAA,IACtC,WAAW,WAAW,GAAG;AAEvB,uBAAiB,KAAK,IAAI,UAAU;AAAA,IACtC,OAAO;AACL;AAAA,QAAM;AAAA,QAAM;AAAA;AAAA,MAAyF;AAAA,IACvG;AAEA,QAAI,UAAU,aAAa;AACzB,uBAAiB,gBAAgB,IAAI,UAAU;AAAA,IACjD;AAEA,qBAAiB,WAAW,IAAI;AAChC,WAAO,gBAAgB,MAAM,UAAU,MAAM,MAAM,SAAY,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,KAAK,QAAW,gBAAgB;AAAA,EACjJ;AAAA;AAIA,SAAe,gBAAgB,MAAM;AAAA;AACnC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,eAAe;AACnB,UAAM,UAAU,CAAC;AACjB,QAAI,OAAO,GAAG;AACZ,cAAQ,cAAc,UAAU;AAAA,IAClC,WAAW,WAAW,GAAG;AACvB,cAAQ,qBAAqB,UAAU;AAAA,IACzC,OAAO;AACL;AAAA,QAAM;AAAA,QAAM;AAAA;AAAA,MAAyF;AAAA,IACvG;AAEA,UAAM,kBAAkB,MAAM,OAAO;AAAA,EACvC;AAAA;AACA,SAAS,iBAAiB,YAAY;AAEpC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe;AACnB,SAAO,IAAI,QAAQ,aAAW;AAC5B,YAAQ,QAAQ,WAAW,YAAY,2BAAyB;AAC9D,UAAI,SAAS;AACb,UAAI,uBAAuB;AACzB,gBAAQ,QAAQ,WAAW,QAAQ,UAAU;AAAA,MAC/C,OAAO;AAEL,iBAAS,QAAQ,aAAa,KAAK,YAAY,WAAW,IAAI,WAAW,WAAW,cAAc;AAAA,MACpG;AACA,cAAQ,MAAM;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH;AAOA,SAAe,kBAAkB,MAAM,eAAe,QAAQ;AAAA;AAE5D,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,eAAe;AACnB,QAAI,UAAU,MAAM;AAAA,IAAC;AACrB,QAAI;AACF,YAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACrC,YAAI,eAAe;AAEnB,iBAAS,gBAAgB;AACvB,cAAI;AAGJ,kBAAQ;AACR,gBAAM,mBAAmB,KAAK,QAAQ,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAClG,cAAI,OAAO,oBAAoB,YAAY;AACzC,4BAAgB;AAAA,UAClB;AAGA,cAAI,QAAQ,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,WAAW,YAAY;AACxF,mBAAO,MAAM;AAAA,UACf;AAAA,QACF;AACA,iBAAS,UAAU;AACjB,cAAI,cAAc;AAEhB;AAAA,UACF;AACA,yBAAe,OAAO,WAAW,MAAM;AAErC,mBAAO;AAAA,cAAa;AAAA,cAAM;AAAA;AAAA,YAA2E,CAAC;AAAA,UACxG,GAAG,mBAAmB;AAAA,QACxB;AACA,iBAAS,oBAAoB;AAC3B,eAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,qBAAqB,WAAW;AAChG,oBAAQ;AAAA,UACV;AAAA,QACF;AAGA,sBAAc,mBAAmB,aAAa;AAE9C,iBAAS,iBAAiB,UAAU,SAAS,KAAK;AAClD,YAAI,WAAW,GAAG;AAChB,mBAAS,iBAAiB,oBAAoB,mBAAmB,KAAK;AAAA,QACxE;AAEA,kBAAU,MAAM;AACd,wBAAc,sBAAsB,aAAa;AACjD,mBAAS,oBAAoB,UAAU,SAAS,KAAK;AACrD,mBAAS,oBAAoB,oBAAoB,mBAAmB,KAAK;AACzE,cAAI,cAAc;AAChB,mBAAO,aAAa,YAAY;AAAA,UAClC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,UAAE;AACA,cAAQ;AAAA,IACV;AAAA,EACF;AAAA;AAMA,SAAS,2BAA2B,MAAM;AACxC,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxC,QAAM,MAAM,eAAe;AAM3B,UAAQ,SAAS,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,YAAY,MAAM,iCAAmF;AAAA,IAC9O,eAAe;AAAA,EACjB,CAAC;AAED,UAAQ,SAAS,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,iBAAiB,aAAa,MAAM,iCAAmF;AAAA,IAC5O,eAAe;AAAA,EACjB,CAAC;AAED,UAAQ,SAAS,MAAM,MAAM,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,YAAY,MAAM,iCAAmF;AAAA,IACtV,eAAe;AAAA,EACjB,CAAC;AACD,UAAQ,SAAS,MAAM,MAAM,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,iBAAiB,YAAY,MAAM,iCAAmF;AAAA,IAC1V,eAAe;AAAA,EACjB,CAAC;AAED,UAAQ,SAAS,MAAM,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,kBAAkB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY,MAAM,iCAAmF;AAAA,IAC9R,eAAe;AAAA,EACjB,CAAC;AACH;AAMA,SAAe,cAAc,WAAW;AAAA;AACtC,UAAM,QAAQ,oBAAoB,SAAS;AAK3C,UAAM,MAAM,MAAM,OAAO,OAAO,OAAO,WAAW,KAAK;AACvD,UAAM,MAAM,MAAM,KAAK,IAAI,WAAW,GAAG,CAAC;AAC1C,WAAO,IAAI,IAAI,SAAO,IAAI,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE;AAAA,EAClE;AAAA;AACA,SAAS,oBAAoB,KAAK;AAGhC,cAAY,eAAe,KAAK,GAAG,GAAG,wCAAwC;AAC9E,MAAI,OAAO,gBAAgB,aAAa;AACtC,WAAO,IAAI,YAAY,EAAE,OAAO,GAAG;AAAA,EACrC;AACA,QAAM,OAAO,IAAI,YAAY,IAAI,MAAM;AACvC,QAAM,OAAO,IAAI,WAAW,IAAI;AAChC,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,SAAK,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,EAC5B;AACA,SAAO;AACT;AAkBA,IAAM,oBAAoB;AAE1B,IAAM,0BAAN,cAAsC,iBAAiB;AAAA,EACrD,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,mBAAmB,oBAAI,IAAI;AAChC,SAAK,cAAc,IAAI,QAAQ,aAAW;AACxC,WAAK,qBAAqB;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,IAAI;AACrB,SAAK,iBAAiB,IAAI,EAAE;AAAA,EAC9B;AAAA,EACA,sBAAsB,IAAI;AACxB,SAAK,iBAAiB,OAAO,EAAE;AAAA,EACjC;AAAA;AAAA;AAAA,EAGA,gBAAgB;AACd,SAAK,sBAAsB;AAC3B,SAAK,8BAA8B;AAAA,EACrC;AAAA;AAAA,EAEA,QAAQ,OAAO;AACb,SAAK,mBAAmB;AACxB,SAAK,iBAAiB,QAAQ,QAAM,GAAG,KAAK,CAAC;AAC7C,WAAO,MAAM,QAAQ,KAAK;AAAA,EAC5B;AAAA,EACM,cAAc;AAAA;AAClB,YAAM,KAAK;AAAA,IACb;AAAA;AACF;AAIA,SAAS,kBAAkB,MAAM,MAAM,UAAU,MAAM;AACrD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb,WAAW,kBAAkB;AAAA,IAC7B,UAAU;AAAA,IACV,UAAU,KAAK;AAAA,IACf,OAAO;AAAA,MAAa;AAAA,MAAM;AAAA;AAAA,IAAiD;AAAA,EAC7E;AACF;AACA,SAAS,kBAAkB,MAAM,OAAO;AACtC,SAAO,QAAQ,EAAE,KAAK,eAAe,IAAI,GAAG,KAAK;AACnD;AACA,SAAe,mBAAmB,MAAM;AAAA;AACtC,UAAM,QAAQ,MAAM,QAAQ,EAAE,KAAK,eAAe,IAAI,CAAC;AACvD,QAAI,OAAO;AACT,YAAM,QAAQ,EAAE,QAAQ,eAAe,IAAI,CAAC;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AAAA;AACA,SAAS,wBAAwB,cAAc,KAAK;AAClD,MAAI,IAAI;AAER,QAAM,cAAc,yBAAyB,GAAG;AAMhD,MAAI,YAAY,SAAS,mBAAmB,GAAG;AAI7C,UAAM,SAAS,oBAAoB,WAAW;AAE9C,UAAM,cAAc,OAAO,eAAe,IAAI,gBAAgB,mBAAmB,OAAO,eAAe,CAAC,CAAC,IAAI;AAC7G,UAAM,QAAQ,MAAM,KAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,MAAM,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,OAAO,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC;AACzM,UAAM,QAAQ,OAAO,aAAa,IAAI,IAAI;AAC1C,QAAI,OAAO;AACT,aAAO;AAAA,QACL,MAAM,aAAa;AAAA,QACnB,SAAS,aAAa;AAAA,QACtB,UAAU,aAAa;AAAA,QACvB;AAAA,QACA,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,MAAM,aAAa;AAAA,QACnB,SAAS,aAAa;AAAA,QACtB,UAAU,aAAa;AAAA,QACvB,WAAW,aAAa;AAAA,QACxB,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,oBAAoB;AAC3B,QAAM,QAAQ,CAAC;AACf,QAAM,eAAe;AACrB,WAAS,IAAI,GAAG,IAAI,mBAAmB,KAAK;AAC1C,UAAM,MAAM,KAAK,MAAM,KAAK,OAAO,IAAI,aAAa,MAAM;AAC1D,UAAM,KAAK,aAAa,OAAO,GAAG,CAAC;AAAA,EACrC;AACA,SAAO,MAAM,KAAK,EAAE;AACtB;AACA,SAAS,UAAU;AACjB,SAAO,aAAa,uBAAuB;AAC7C;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,oBAAoB,aAAsC,KAAK,OAAO,QAAQ,KAAK,IAAI;AAChG;AACA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AACF,WAAO,KAAK,MAAM,IAAI;AAAA,EACxB,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AAEA,SAAS,yBAAyB,KAAK;AACrC,QAAM,SAAS,oBAAoB,GAAG;AACtC,QAAM,OAAO,OAAO,MAAM,IAAI,mBAAmB,OAAO,MAAM,CAAC,IAAI;AAEnE,QAAM,iBAAiB,oBAAoB,IAAI,EAAE,MAAM;AAEvD,QAAM,cAAc,OAAO,cAAc,IAAI,mBAAmB,OAAO,cAAc,CAAC,IAAI;AAC1F,QAAM,oBAAoB,oBAAoB,WAAW,EAAE,MAAM;AACjE,SAAO,qBAAqB,eAAe,kBAAkB,QAAQ;AACvE;AAKA,SAAS,oBAAoB,KAAK;AAChC,MAAI,EAAE,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,SAAS,GAAG,IAAI;AAClE,WAAO,CAAC;AAAA,EACV;AACA,QAAM,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,MAAM,GAAG;AAClC,SAAO,kBAAkB,KAAK,KAAK,GAAG,CAAC;AACzC;AAsBA,IAAM,2BAA2B;AACjC,IAAM,+BAAN,MAAmC;AAAA,EACjC,cAAc;AACZ,SAAK,uBAAuB;AAC5B,SAAK,yBAAyB;AAC9B,SAAK,gBAAgB,oBAAI,IAAI;AAC7B,SAAK,2BAA2B,CAAC;AACjC,SAAK,sBAAsB;AAC3B,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACM,YAAY,MAAM;AAAA;AACtB,YAAM,MAAM,KAAK,KAAK;AACtB,UAAI,UAAU,KAAK,cAAc,IAAI,GAAG;AACxC,UAAI,CAAC,SAAS;AACZ,kBAAU,IAAI,wBAAwB,IAAI;AAC1C,aAAK,cAAc,IAAI,KAAK,OAAO;AACnC,aAAK,wBAAwB,MAAM,OAAO;AAAA,MAC5C;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACA,WAAW,MAAM;AACf;AAAA,MAAM;AAAA,MAAM;AAAA;AAAA,IAAyF;AAAA,EACvG;AAAA,EACM,cAAc,MAAM,UAAU,UAAU,SAAS;AAAA;AACrD,iCAA2B,IAAI;AAC/B,YAAM,UAAU,MAAM,KAAK,YAAY,IAAI;AAC3C,YAAM,QAAQ,YAAY;AAI1B,cAAQ,cAAc;AACtB,6BAAuB;AACvB,YAAM,KAAK,kBAAkB,IAAI;AACjC,YAAM,QAAQ,kBAAkB,MAAM,UAAU,OAAO;AACvD,YAAM,kBAAkB,MAAM,KAAK;AACnC,YAAM,MAAM,MAAM,oBAAoB,MAAM,OAAO,QAAQ;AAC3D,YAAM,SAAS,MAAM,iBAAiB,GAAG;AACzC,aAAO,kBAAkB,MAAM,SAAS,MAAM;AAAA,IAChD;AAAA;AAAA,EACA,6BAA6B,OAAO,KAAK;AACvC,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAAA,EACA,kBAAkB,MAAM;AACtB,UAAM,MAAM,KAAK,KAAK;AACtB,QAAI,CAAC,KAAK,yBAAyB,GAAG,GAAG;AACvC,WAAK,yBAAyB,GAAG,IAAI,gBAAgB,IAAI;AAAA,IAC3D;AACA,WAAO,KAAK,yBAAyB,GAAG;AAAA,EAC1C;AAAA,EACA,wBAAwB,MAAM,SAAS;AAErC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,eAAe;AACnB,UAAM,iBAAiB,WAAW,MAAY;AAG5C,YAAM,mBAAmB,IAAI;AAC7B,cAAQ,QAAQ,gBAAgB,CAAC;AAAA,IACnC,IAAG,wBAAwB;AAC3B,UAAM,mBAAmB,CAAM,cAAa;AAE1C,mBAAa,cAAc;AAC3B,YAAM,eAAe,MAAM,mBAAmB,IAAI;AAClD,UAAI,aAAa;AACjB,UAAI,iBAAiB,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,KAAK,IAAI;AAC5F,qBAAa,wBAAwB,cAAc,UAAU,KAAK,CAAC;AAAA,MACrE;AAEA,cAAQ,QAAQ,cAAc,gBAAgB,CAAC;AAAA,IACjD;AAEA,QAAI,OAAO,mBAAmB,eAAe,OAAO,eAAe,cAAc,YAAY;AAC3F,qBAAe,UAAU,MAAM,gBAAgB;AAAA,IACjD;AAMA,UAAM,wBAAwB;AAC9B,UAAM,gBAAgB,GAAG,UAAU,YAAY,YAAY,CAAC;AAC5D,mBAAe,EAAE,gBAAgB,CAAM,QAAO;AAC5C,UAAI,IAAI,YAAY,EAAE,WAAW,aAAa,GAAG;AAG/C,yBAAiB;AAAA,UACf;AAAA,QACF,CAAC;AAAA,MACH;AAEA,UAAI,OAAO,0BAA0B,YAAY;AAC/C,YAAI;AACF,gCAAsB,GAAG;AAAA,QAC3B,SAAS,GAAG;AAEV,kBAAQ,MAAM,CAAC;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAOA,IAAM,+BAA+B;AACrC,SAAS,kBAAkB;AACzB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,IACV,OAAO;AAAA,MAAa;AAAA;AAAA,IAAiD;AAAA,EACvE;AACF;AAqBA,SAAS,uBAAuB,MAAM,WAAW;AAC/C,YAAU,IAAI,EAAE,cAAc,SAAS;AACzC;;;ACljBA,IAAI,OAAO;AACX,IAAI,UAAU;AAkBd,IAAM,mCAAmC;AACzC,SAAS,oBAAoB;AAC3B,MAAI;AACJ,WAAS,KAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa;AAChI;AAIA,SAAS,iBAAiB;AACxB,SAAO,kBAAkB,MAAM,WAAW,kBAAkB,MAAM;AACpE;AAMA,SAAS,6BAA6B,KAAK,MAAM,GAAG;AAClD,SAAO,CAAC,GAAG,kBAAkB,MAAM,WAAW,kBAAkB,MAAM,YAAY,kBAAkB,MAAM,iBAAiB,GAAG,YAAY,EAAE,MAAM,0BAA0B;AAC9K;AAKA,SAAS,uBAAuB;AAC9B,SAAO,cAAc,KAAK,OAAO;AACnC;AAKA,SAAS,UAAU;AACjB,SAAO,KAAK,MAAM,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,kBAAkB;AACnG;AAMA,SAAS,QAAQ,KAAK,MAAM,GAAG;AAC7B,SAAO,YAAY,KAAK,EAAE;AAC5B;AAMA,SAAS,+BAA+B,KAAK,MAAM,GAAG;AACpD,SAAO,QAAQ,KAAK,QAAQ,EAAE;AAChC;AAEA,SAAS,yBAAyB;AAChC,MAAI;AACF,UAAMA,WAAU,KAAK;AACrB,UAAM,MAAU,iBAAiB;AACjC,QAAIA,UAAS;AAGX,MAAAA,SAAQ,SAAS,EAAE,KAAK,GAAG;AAC3B,MAAAA,SAAQ,YAAY,EAAE,GAAG;AAMzB,UAAI,+BAA+B,GAAG;AAGpC,eAAO,qBAAqB;AAAA,MAC9B;AACA,aAAO;AAAA,IACT;AAAA,EACF,SAAS,GAAG;AAGV,WAAO,UAAU,KAAK,qBAAqB;AAAA,EAC7C;AACA,SAAO;AACT;AAKA,SAAS,YAAY;AAEnB,SAAO,OAAO,WAAW,eAAe,uBAAuB,UAAU,mBAAmB;AAC9F;AACA,SAAS,4BAA4B;AACnC,UAAQ,eAAe,KAAK,mBAAmB,KAAK,6BAA6B;AAAA;AAAA,EAGjF,CAAC,qBAAqB;AAAA;AAAA,EAGtB,uBAAuB;AAAA,EAEvB,CAAC,UAAU;AACb;AAEA,SAAS,mBAAmB;AAC1B,SAAO,6BAA6B,KAAK,OAAO,aAAa;AAC/D;AACA,SAAe,aAAa;AAAA;AAC1B,QAAI,CAAC,iBAAiB,GAAG;AACvB,aAAO;AAAA,IACT;AACA,WAAO,IAAI,QAAQ,aAAW;AAC5B,YAAM,YAAY,WAAW,MAAM;AAEjC,gBAAQ,KAAK;AAAA,MACf,GAAG,gCAAgC;AACnC,eAAS,iBAAiB,eAAe,MAAM;AAC7C,qBAAa,SAAS;AACtB,gBAAQ,IAAI;AAAA,MACd,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AACA,SAAS,iBAAiB;AACxB,SAAO,OAAO,WAAW,cAAc,SAAS;AAClD;AAkBA,IAAM,cAAc;AAAA,EAClB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,YAAgB;AACtB,IAAM,kBAAkB;AAKxB,SAAS,6BAA6B,MAAM,aAAa;AACvD;AAAA,IAAU,OAAO,OAAO,WAAW,EAAE,SAAS,WAAW;AAAA,IAAG;AAAA,IAAM;AAAA;AAAA,EAAsE;AAExI,MAAI,cAAc,GAAG;AAEnB;AAAA,MAAU,gBAAgB,YAAY;AAAA,MAAS;AAAA,MAAM;AAAA;AAAA,IAA8E;AACnI;AAAA,EACF;AACA,MAAI,OAAO,GAAG;AAEZ;AAAA,MAAU,gBAAgB,YAAY;AAAA,MAAM;AAAA,MAAM;AAAA;AAAA,IAA8E;AAChI;AAAA,EACF;AACA,MAAI,UAAU,GAAG;AAGf;AAAA,MAAU,gBAAgB,YAAY,QAAQ,gBAAgB,YAAY,SAAS,qBAAqB;AAAA,MAAG;AAAA,MAAM;AAAA;AAAA,IAA8E;AAC/L;AAAA,EACF;AAEA;AAAA,IAAU,gBAAgB,YAAY,QAAQ,uBAAuB;AAAA,IAAG;AAAA,IAAM;AAAA;AAAA,EAA8E;AAC9J;AACA,SAAe,4BAA4B,MAAM;AAAA;AAC/C,UAAM,KAAK;AACX,UAAM,UAAU,6BAA6B;AAC7C,UAAM,MAAU,oBAAoB,iBAAiB,KAAK,OAAO,QAAQ,KAAK,IAAI;AAClF,QAAI,SAAS;AACX,cAAQ,QAAQ,KAAK,KAAK,gBAAgB,CAAC;AAAA,IAC7C;AAAA,EACF;AAAA;AACA,SAAS,6BAA6B,QAAQ,SAAS;AACrD,QAAM,UAAU,6BAA6B;AAC7C,MAAI,CAAC,SAAS;AACZ,WAAO,CAAC;AAAA,EACV;AACA,QAAM,MAAU,oBAAoB,iBAAiB,QAAQ,OAAO;AACpE,QAAM,cAAc,QAAQ,QAAQ,GAAG;AACvC,UAAQ,aAAa;AAAA,IACnB,KAAK,YAAY;AACf,aAAO,CAAK,mBAAmB;AAAA,IACjC,KAAK,YAAY;AACf,aAAO,CAAK,2BAA+B,yBAAyB;AAAA,IACtE,KAAK,YAAY;AACf,aAAO,CAAK,yBAAyB;AAAA,IACvC;AACE,aAAO,CAAC;AAAA,EACZ;AACF;AAEA,SAAS,+BAA+B;AACtC,MAAI;AACJ,MAAI;AACF,aAAS,KAAK,eAAe,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,mBAAmB;AAAA,EAC7F,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AAkBA,IAAM,YAAgB;AAEtB,IAAM,8BAAN,MAAkC;AAAA,EAChC,cAAc;AAEZ,SAAK,kBAAsB,aAAiB,4BAA4B;AACxE,SAAK,kBAAsB,aAAiB,4BAA4B;AAExE,SAAK,qBAAqB;AAC1B,SAAK,uBAA2B;AAChC,SAAK,sBAA0B;AAC/B,SAAK,0BAA8B;AAAA,EACrC;AAAA,EACM,YAAY,MAAM;AAAA;AACtB,YAAM,KAAK,yBAAyB;AACpC,aAAO,KAAK,2BAA2B,YAAY,IAAI;AAAA,IACzD;AAAA;AAAA,EACM,WAAW,MAAM,UAAU,UAAU,SAAS;AAAA;AAClD,YAAM,KAAK,yBAAyB;AACpC,aAAO,KAAK,2BAA2B,WAAW,MAAM,UAAU,UAAU,OAAO;AAAA,IACrF;AAAA;AAAA,EACM,cAAc,MAAM,UAAU,UAAU,SAAS;AAAA;AACrD,YAAM,KAAK,yBAAyB;AACpC,aAAO,KAAK,2BAA2B,cAAc,MAAM,UAAU,UAAU,OAAO;AAAA,IACxF;AAAA;AAAA,EACA,6BAA6B,MAAM,IAAI;AACrC,SAAK,2BAA2B,6BAA6B,MAAM,EAAE;AAAA,EACvE;AAAA,EACA,kBAAkB,MAAM;AACtB,WAAO,KAAK,2BAA2B,kBAAkB,IAAI;AAAA,EAC/D;AAAA,EACA,IAAI,yBAAyB;AAC3B,WAAO,iBAAiB,KAAK,KAAK,gBAAgB;AAAA,EACpD;AAAA,EACA,IAAI,6BAA6B;AAC/B;AAAA,MAAU,KAAK;AAAA,MAAoB;AAAA;AAAA,IAAuD;AAC1F,WAAO,KAAK;AAAA,EACd;AAAA,EACM,2BAA2B;AAAA;AAC/B,UAAI,KAAK,oBAAoB;AAC3B;AAAA,MACF;AAGA,YAAM,YAAY,MAAM,WAAW;AACnC,WAAK,qBAAqB,YAAY,KAAK,kBAAkB,KAAK;AAAA,IACpE;AAAA;AACF;AAkBA,SAAS,OAAO,QAAQ;AACtB,SAAO,OAAO,OAAO;AACvB;AACA,SAAS,QAAQ,QAAQ;AACvB,SAAO,OAAO,QAAQ;AACxB;AAkBA,SAAS,uBAAuB,gBAAgB;AAC9C,SAAO,qBAAqB,cAAc;AAC5C;AACA,SAAS,uBAAuB,MAAM,GAAG;AACvC,MAAI;AAGJ,QAAM,YAAY,KAAK,EAAE,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC7E,OAAK,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE,UAAU,mCAAmC;AACxF,UAAM,SAAS;AACf,WAAO,WAAW,IAAI,oBAAoB,MAAU,uBAAuB,MAAM,CAAC,CAAC;AAAA,EACrF,WAAW,UAAU;AACnB,UAAM,aAAa,qBAAqB,CAAC;AACzC,UAAM,UAAU;AAChB,QAAI,YAAY;AACd,cAAQ,aAAa;AACrB,cAAQ,WAAW,SAAS,YAAY;AACxC,cAAQ,QAAQ,SAAS,SAAS;AAClC,cAAQ,cAAc,SAAS,eAAe;AAAA,IAChD;AAAA,EACF;AACF;AACA,SAAS,qBAAqB,QAAQ;AACpC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,kBAAkB,gBAAgB,OAAO,aAAa;AAC1D,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AAIA,MAAI,EAAE,kBAAkB,gBAAgB;AACtC,QAAI,oBAAoB,kBAAkB,iBAAiB,gBAAgB;AACzE,aAAW,kBAAkB,qBAAqB,MAAM;AAAA,IAC1D;AAAA,EACF;AACA,QAAM,aAAa,eAAe;AAGlC,MAAI,CAAC,cAAc,eAAmB,WAAW,UAAU;AACzD,WAAO;AAAA,EACT;AACA,MAAI;AACJ,UAAQ,YAAY;AAAA,IAClB,KAAS,WAAW;AAClB,iBAAe;AACf;AAAA,IACF,KAAS,WAAW;AAClB,iBAAe;AACf;AAAA,IACF,KAAS,WAAW;AAClB,iBAAe;AACf;AAAA,IACF,KAAS,WAAW;AAClB,iBAAe;AACf;AAAA,IACF;AACE,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,cAAc;AAC5E,eAAO;AAAA,MACT;AAEA,UAAI,cAAc;AAChB,YAAI,WAAW,WAAW,OAAO,GAAG;AAClC,iBAAW,mBAAmB,QAAQ,YAAY,YAAY;AAAA,QAChE,OAAO;AAEL,iBAAW,gBAAgB,YAAY;AAAA,YACrC;AAAA,YACA,cAAc;AAAA,YACd;AAAA,YACA,SAAS;AAAA,YACT,aAAa;AAAA,UACf,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO,IAAQ,cAAc,UAAU,EAAE,WAAW;AAAA,QAClD,SAAS;AAAA,QACT,aAAa;AAAA,QACb,UAAU;AAAA,MACZ,CAAC;AAAA,EACL;AACA,SAAO,kBAAkB,gBAAgB,SAAS,oBAAoB,MAAM,IAAI,SAAS,qBAAqB,MAAM;AACtH;AACA,SAAS,kBAAkB,MAAM,mBAAmB;AAClD,SAAO,kBAAkB,MAAM,OAAK;AAClC,QAAI,aAAa,eAAe;AAC9B,6BAAuB,MAAM,CAAC;AAAA,IAChC;AACA,UAAM;AAAA,EACR,CAAC,EAAE,KAAK,gBAAc;AACpB,UAAM,gBAAgB,WAAW;AACjC,UAAM,OAAO,WAAW;AACxB,WAAO;AAAA,MACL;AAAA,MACA,YAAY,uBAAuB,UAAU;AAAA,MAC7C,oBAAwB,sBAAsB,UAAU;AAAA,MACxD,MAAM,KAAK,YAAY,IAAI;AAAA,IAC7B;AAAA,EACF,CAAC;AACH;AACA,SAAe,0BAA0B,MAAM,2BAA2B;AAAA;AACxE,UAAM,wBAAwB,MAAM;AACpC,WAAO;AAAA,MACL,gBAAgB,sBAAsB;AAAA,MACtC,SAAS,sBAAoB,kBAAkB,MAAM,sBAAsB,QAAQ,gBAAgB,CAAC;AAAA,IACtG;AAAA,EACF;AAAA;AACA,IAAM,sBAAN,MAA0B;AAAA,EACxB,YAAY,MAAM,UAAU;AAC1B,SAAK,WAAW;AAChB,SAAK,OAAO,QAAQ,IAAI;AAAA,EAC1B;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,cAAc,WAAW;AACvB,WAAO,kBAAkB,OAAO,KAAK,IAAI,GAAG,KAAK,SAAS,cAAc,SAAS,CAAC;AAAA,EACpF;AACF;AAkBA,IAAM,OAAN,MAAM,MAAK;AAAA,EACT,YAAY,WAAW;AACrB,SAAK,YAAY;AACjB,SAAK,cAAkB,YAAY,SAAS;AAAA,EAC9C;AAAA,EACA,OAAO,YAAY,MAAM;AACvB,QAAI,CAAC,MAAK,SAAS,IAAI,IAAI,GAAG;AAC5B,YAAK,SAAS,IAAI,MAAM,IAAI,MAAK,IAAI,CAAC;AAAA,IACxC;AACA,WAAO,MAAK,SAAS,IAAI,IAAI;AAAA,EAC/B;AAAA,EACA,SAAS;AACP,WAAO,KAAK,UAAU,OAAO;AAAA,EAC/B;AAAA,EACA,SAAS;AACP,WAAO,KAAK,UAAU,OAAO;AAAA,EAC/B;AAAA,EACA,SAAS;AACP,WAAO,KAAK,UAAU,OAAO;AAAA,EAC/B;AAAA,EACA,iBAAiB,cAAc;AAC7B,WAAO,KAAK,UAAU,iBAAiB,YAAY;AAAA,EACrD;AAAA,EACA,WAAW,cAAc;AACvB,WAAO,KAAK,UAAU,WAAW,YAAY;AAAA,EAC/C;AAAA,EACA,kCAAkC,YAAY;AAC5C,WAAO,KAAK,mBAAmB,UAAU;AAAA,EAC3C;AAAA,EACM,mBAAmB,YAAY;AAAA;AACnC,aAAO,kBAAkB,KAAK,MAAU,mBAAmB,KAAK,WAAW,UAAU,CAAC;AAAA,IACxF;AAAA;AAAA,EACM,oBAAoB,aAAa,qBAAqB;AAAA;AAC1D,aAAO,0BAA0B,KAAK,MAAU,oBAAoB,KAAK,WAAW,aAAa,mBAAmB,CAAC;AAAA,IACvH;AAAA;AAAA,EACM,cAAc,UAAU;AAAA;AAC5B,aAAO,kBAAkB,KAAK,MAAU,cAAc,KAAK,WAAW,UAAU,2BAA2B,CAAC;AAAA,IAC9G;AAAA;AAAA,EACM,iBAAiB,UAAU;AAAA;AAC/B,YAAM,4BAAgC,UAAU,KAAK,IAAI,CAAC;AAC1D,aAAW,iBAAiB,KAAK,WAAW,UAAU,2BAA2B;AAAA,IACnF;AAAA;AAAA,EACA,4CAA4C,YAAY;AACtD,WAAO,KAAK,6BAA6B,UAAU;AAAA,EACrD;AAAA,EACM,6BAA6B,YAAY;AAAA;AAC7C,aAAO,kBAAkB,KAAK,MAAU,6BAA6B,KAAK,WAAW,UAAU,CAAC;AAAA,IAClG;AAAA;AAAA,EACA,8BAA8B,aAAa,qBAAqB;AAC9D,WAAO,0BAA0B,KAAK,MAAU,8BAA8B,KAAK,WAAW,aAAa,mBAAmB,CAAC;AAAA,EACjI;AAAA,EACA,wBAAwB,UAAU;AAChC,WAAO,kBAAkB,KAAK,MAAU,wBAAwB,KAAK,WAAW,UAAU,2BAA2B,CAAC;AAAA,EACxH;AAAA,EACM,2BAA2B,UAAU;AAAA;AACzC,YAAM,4BAAgC,UAAU,KAAK,IAAI,CAAC;AAC1D,aAAW,2BAA2B,KAAK,WAAW,UAAU,2BAA2B;AAAA,IAC7F;AAAA;AAAA,EACA,sBAAsB,oBAAoB;AACxC,WAAW,sBAAsB,KAAK,WAAW,kBAAkB;AAAA,EACrE;AAAA,EACM,OAAO,YAAY;AAAA;AACvB,YAAU,OAAO,KAAK,WAAW,UAAU;AAC3C,aAAO;AAAA,IACT;AAAA;AAAA,EACA,YAAY,UAAU;AACpB,WAAW,YAAY,KAAK,WAAW,QAAQ;AAAA,EACjD;AAAA,EACA,eAAe,aAAa;AAC1B,WAAW,eAAe,KAAK,WAAW,WAAW;AAAA,EACvD;AAAA,EACA,kBAAkB,iBAAiB;AACjC,WAAW,kBAAkB,KAAK,WAAW,eAAe;AAAA,EAC9D;AAAA,EACA,cAAc,SAAS;AACrB,WAAW,cAAc,KAAK,WAAW,OAAO;AAAA,EAClD;AAAA,EACA,wBAAwB,UAAU,oBAAoB;AACpD,WAAW,wBAAwB,KAAK,WAAW,UAAU,kBAAkB;AAAA,EACjF;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,UAAU;AAAA,EACxB;AACF;AAGA,KAAK,WAAW,oBAAI,QAAQ;AAkB5B,IAAM,YAAgB;AACtB,IAAM,OAAN,MAAW;AAAA,EACT,YAAY,KAAK,UAAU;AACzB,SAAK,MAAM;AACX,QAAI,SAAS,cAAc,GAAG;AAC5B,WAAK,YAAY,SAAS,aAAa;AACvC,WAAK,mBAAmB;AACxB;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,IAAI;AAER,cAAU,QAAQ,mBAA2D;AAAA,MAC3E,SAAS,IAAI;AAAA,IACf,CAAC;AAED,cAAU,QAAQ,mBAA2D;AAAA,MAC3E,SAAS,IAAI;AAAA,IACf,CAAC;AAED,UAAM,WAAW,OAAO,WAAW,cAAc,8BAA8B;AAC/E,SAAK,YAAY,SAAS,WAAW;AAAA,MACnC,SAAS;AAAA,QACP,aAAa,0BAA0B,QAAQ,IAAI,IAAI;AAAA,QACvD,uBAAuB;AAAA,MACzB;AAAA,IACF,CAAC;AACD,SAAK,UAAU,gBAAoB,aAAa;AAChD,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,cAAc;AAChB,QAAI,CAAC,KAAK,UAAU,aAAa;AAC/B,aAAO;AAAA,IACT;AACA,WAAO,KAAK,YAAY,KAAK,UAAU,WAAW;AAAA,EACpD;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,aAAa,cAAc;AAC7B,SAAK,UAAU,eAAe;AAAA,EAChC;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,SAAK,UAAU,WAAW;AAAA,EAC5B;AAAA,EACA,oBAAoB;AAClB,SAAK,UAAU,kBAAkB;AAAA,EACnC;AAAA,EACA,UAAU;AACR,WAAO,KAAK,UAAU,QAAQ;AAAA,EAChC;AAAA,EACA,YAAY,KAAK,SAAS;AACxB,IAAI,oBAAoB,KAAK,WAAW,KAAK,OAAO;AAAA,EACtD;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAW,gBAAgB,KAAK,WAAW,IAAI;AAAA,EACjD;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAW,gBAAgB,KAAK,WAAW,IAAI;AAAA,EACjD;AAAA,EACA,qBAAqB,MAAM,aAAa;AACtC,WAAW,qBAAqB,KAAK,WAAW,MAAM,WAAW;AAAA,EACnE;AAAA,EACM,+BAA+B,OAAO,UAAU;AAAA;AACpD,aAAO,kBAAkB,KAAK,WAAe,+BAA+B,KAAK,WAAW,OAAO,QAAQ,CAAC;AAAA,IAC9G;AAAA;AAAA,EACA,uBAAuB,OAAO;AAC5B,WAAO,KAAK,2BAA2B,KAAK;AAAA,EAC9C;AAAA,EACA,2BAA2B,OAAO;AAChC,WAAW,2BAA2B,KAAK,WAAW,KAAK;AAAA,EAC7D;AAAA,EACA,sBAAsB,WAAW;AAC/B,WAAW,sBAAsB,KAAK,WAAW,SAAS;AAAA,EAC5D;AAAA,EACM,oBAAoB;AAAA;AACxB;AAAA,QAAU,0BAA0B;AAAA,QAAG,KAAK;AAAA,QAAW;AAAA;AAAA,MAA6F;AACpJ,YAAM,aAAa,MAAU,kBAAkB,KAAK,WAAW,2BAA2B;AAC1F,UAAI,CAAC,YAAY;AACf,eAAO;AAAA,UACL,YAAY;AAAA,UACZ,MAAM;AAAA,QACR;AAAA,MACF;AACA,aAAO,kBAAkB,KAAK,WAAW,QAAQ,QAAQ,UAAU,CAAC;AAAA,IACtE;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB,WAAW;AAChC,IAAI,uBAAuB,KAAK,WAAW,SAAS;AAAA,EACtD;AAAA,EACA,mBAAmB,gBAAgB,SAAS,WAAW;AACrD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,cAAc,gBAAgB,SAAS,SAAS;AACpD,WAAO,KAAK,UAAU,mBAAmB,MAAM,OAAO,QAAQ;AAAA,EAChE;AAAA,EACA,iBAAiB,gBAAgB,SAAS,WAAW;AACnD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,cAAc,gBAAgB,SAAS,SAAS;AACpD,WAAO,KAAK,UAAU,iBAAiB,MAAM,OAAO,QAAQ;AAAA,EAC9D;AAAA,EACA,sBAAsB,OAAO,oBAAoB;AAC/C,WAAW,sBAAsB,KAAK,WAAW,OAAO,kBAAkB;AAAA,EAC5E;AAAA,EACA,uBAAuB,OAAO,oBAAoB;AAChD,WAAW,uBAAuB,KAAK,WAAW,OAAO,sBAAsB,MAAS;AAAA,EAC1F;AAAA,EACM,eAAe,aAAa;AAAA;AAChC,mCAA6B,KAAK,WAAW,WAAW;AACxD,UAAI;AACJ,cAAQ,aAAa;AAAA,QACnB,KAAK,YAAY;AACf,sBAAgB;AAChB;AAAA,QACF,KAAK,YAAY;AAEf,gBAAM,4BAA4B,MAAU,aAAiB,yBAAyB,EAAE,aAAa;AACrG,sBAAY,4BAAgC,4BAAgC;AAC5E;AAAA,QACF,KAAK,YAAY;AACf,sBAAgB;AAChB;AAAA,QACF;AACE,iBAAW,MAAM,kBAAyD;AAAA,YACxE,SAAS,KAAK,UAAU;AAAA,UAC1B,CAAC;AAAA,MACL;AACA,aAAO,KAAK,UAAU,eAAe,SAAS;AAAA,IAChD;AAAA;AAAA,EACA,oCAAoC,YAAY;AAC9C,WAAO,KAAK,qBAAqB,UAAU;AAAA,EAC7C;AAAA,EACA,oBAAoB;AAClB,WAAO,kBAAkB,KAAK,WAAe,kBAAkB,KAAK,SAAS,CAAC;AAAA,EAChF;AAAA,EACA,qBAAqB,YAAY;AAC/B,WAAO,kBAAkB,KAAK,WAAe,qBAAqB,KAAK,WAAW,UAAU,CAAC;AAAA,EAC/F;AAAA,EACA,sBAAsB,OAAO;AAC3B,WAAO,kBAAkB,KAAK,WAAe,sBAAsB,KAAK,WAAW,KAAK,CAAC;AAAA,EAC3F;AAAA,EACA,2BAA2B,OAAO,UAAU;AAC1C,WAAO,kBAAkB,KAAK,WAAe,2BAA2B,KAAK,WAAW,OAAO,QAAQ,CAAC;AAAA,EAC1G;AAAA,EACA,oBAAoB,OAAO,WAAW;AACpC,WAAO,kBAAkB,KAAK,WAAe,oBAAoB,KAAK,WAAW,OAAO,SAAS,CAAC;AAAA,EACpG;AAAA,EACA,sBAAsB,aAAa,qBAAqB;AACtD,WAAO,0BAA0B,KAAK,WAAe,sBAAsB,KAAK,WAAW,aAAa,mBAAmB,CAAC;AAAA,EAC9H;AAAA,EACM,gBAAgB,UAAU;AAAA;AAC9B;AAAA,QAAU,0BAA0B;AAAA,QAAG,KAAK;AAAA,QAAW;AAAA;AAAA,MAA6F;AACpJ,aAAO,kBAAkB,KAAK,WAAe,gBAAgB,KAAK,WAAW,UAAU,2BAA2B,CAAC;AAAA,IACrH;AAAA;AAAA,EACM,mBAAmB,UAAU;AAAA;AACjC;AAAA,QAAU,0BAA0B;AAAA,QAAG,KAAK;AAAA,QAAW;AAAA;AAAA,MAA6F;AACpJ,YAAM,4BAA4B,KAAK,SAAS;AAChD,aAAW,mBAAmB,KAAK,WAAW,UAAU,2BAA2B;AAAA,IACrF;AAAA;AAAA,EACA,kBAAkB,MAAM;AAGtB,WAAO,KAAK,UAAU,kBAAkB,IAAI;AAAA,EAC9C;AAAA,EACA,wBAAwB,MAAM;AAC5B,WAAW,wBAAwB,KAAK,WAAW,IAAI;AAAA,EACzD;AAAA,EACA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA,EACA,UAAU;AACR,WAAO,KAAK,UAAU,QAAQ;AAAA,EAChC;AAAA,EACA,qBAAqB;AACnB,SAAK,UAAU,UAAU,MAAM;AAAA,EACjC;AACF;AACA,KAAK,cAAc;AACnB,SAAS,cAAc,gBAAgB,OAAO,UAAU;AACtD,MAAI,OAAO;AACX,MAAI,OAAO,mBAAmB,YAAY;AACxC,KAAC;AAAA,MACC;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAAA,EACN;AAEA,QAAM,UAAU;AAChB,QAAM,UAAU,UAAQ,QAAQ,QAAQ,KAAK,YAAY,IAAI,CAAC;AAC9D,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,0BAA0B,QAAQ,SAAS;AAKlD,QAAM,eAAe,6BAA6B,QAAQ,OAAO;AAEjE,MAAI,OAAO,SAAS,eAAe,CAAC,aAAa,SAAa,yBAAyB,GAAG;AACxF,iBAAa,KAAS,yBAAyB;AAAA,EACjD;AAEA,MAAI,OAAO,WAAW,aAAa;AACjC,eAAW,eAAe,CAAK,yBAA6B,yBAAyB,GAAG;AACtF,UAAI,CAAC,aAAa,SAAS,WAAW,GAAG;AACvC,qBAAa,KAAK,WAAW;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,aAAa,SAAa,mBAAmB,GAAG;AACnD,iBAAa,KAAS,mBAAmB;AAAA,EAC3C;AACA,SAAO;AACT;AAkBA,IAAMC,qBAAN,MAAwB;AAAA,EACtB,cAAc;AACZ,SAAK,aAAa;AAGlB,SAAK,YAAY,IAAQ,kBAAkB,OAAO,SAAS,KAAK,CAAC,CAAC;AAAA,EACpE;AAAA,EACA,OAAO,WAAW,gBAAgB,kBAAkB;AAClD,WAAW,kBAAkB,WAAW,gBAAgB,gBAAgB;AAAA,EAC1E;AAAA,EACA,kBAAkB,kBAAkB,qBAAqB;AACvD,WAAO,KAAK,UAAU;AAAA;AAAA;AAAA,MAGtB;AAAA,MAAkB;AAAA,IAAmB;AAAA,EACvC;AAAA,EACA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AACF;AACAA,mBAAkB,uBAA2B,kBAAkB;AAC/DA,mBAAkB,cAAkB,kBAAkB;AAkBtD,IAAMC,WAAc;AACpB,IAAMC,qBAAN,MAAwB;AAAA,EACtB,YAAY,WAAW,YAAY,MAAM,SAAS,IAAI,GAAG;AACvD,QAAI;AAEJ,IAAAD,UAAS,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,mBAA2D;AAAA,MACpI,SAAS,IAAI;AAAA,IACf,CAAC;AACD,SAAK,YAAY,IAAQ;AAAA;AAAA;AAAA,MAGzB,IAAI,KAAK;AAAA,MAAG;AAAA;AAAA,MAEZ;AAAA,IAAU;AACV,SAAK,OAAO,KAAK,UAAU;AAAA,EAC7B;AAAA,EACA,QAAQ;AACN,SAAK,UAAU,MAAM;AAAA,EACvB;AAAA,EACA,SAAS;AACP,WAAO,KAAK,UAAU,OAAO;AAAA,EAC/B;AAAA,EACA,SAAS;AACP,WAAO,KAAK,UAAU,OAAO;AAAA,EAC/B;AACF;AAkBA,IAAM,YAAY;AAGlB,SAAS,mBAAmB,UAAU;AACpC,WAAS,SAAS,kBAAkB,IAAI;AAAA,IAAU;AAAA,IAAW,eAAa;AAExE,YAAM,MAAM,UAAU,YAAY,YAAY,EAAE,aAAa;AAC7D,YAAM,eAAe,UAAU,YAAY,MAAM;AACjD,aAAO,IAAI,KAAK,KAAK,YAAY;AAAA,IACnC;AAAA,IAAG;AAAA;AAAA,EAAmC,EAAE,gBAAgB;AAAA,IACtD,gBAAgB;AAAA,MACd,WAAW;AAAA,QACT,cAAkB,oBAAoB;AAAA,QACtC,gBAAoB,oBAAoB;AAAA,QACxC,eAAmB,oBAAoB;AAAA,QACvC,+BAAmC,oBAAoB;AAAA,QACvD,yBAA6B,oBAAoB;AAAA,QACjD,cAAkB,oBAAoB;AAAA,MACxC;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,mBAAmBD;AAAA,IACnB;AAAA,IACA,mBAAmBE;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT,CAAC,EAAE;AAAA,IAAqB;AAAA;AAAA,EAAmC,EAAE,qBAAqB,KAAK,CAAC;AACxF,WAAS,gBAAgB,MAAM,OAAO;AACxC;AACA,mBAAmB,QAAQ;", "names": ["storage", "PhoneAuthProvider", "_assert", "RecaptchaVerifier"]}
import { CommonModule } from '@angular/common';
import { Component, computed, Inject, Signal, signal, WritableSignal } from '@angular/core';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { CustomDateAdapter, MY_DATE_FORMATS } from '@app/_configs/mat-date.config';
import { InputDropdownComponent } from '../input-dropdown/input-dropdown.component';
import { CustomDate, InputDropdownConfig } from '@app/_interfaces/generic.interface';
import { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { UtilsService } from '@app/_services/utils.service';
import { Subject, takeUntil } from 'rxjs';
import { ParseRulePipe } from '@app/_pipes/parse-rule.pipe';
import { DependencyService } from '@app/_services/dependency.service';
import { AlertService } from '@app/_services/alert.service';
import * as _ from 'lodash';
import { InputCalendarComponent, InputCalendarModel } from '../input-calendar/input-calendar.component';
import { EndDateRepeatMode, RepeatType } from '@app/_types/generic.type';
import { DayConfig, OrdinalConfig, RepeatTypeConfig } from '@app/_datas/generic-config.data';
import { InputDateSelectComponent } from '../input-date-select/input-date-select.component';
import { InputMonthSelectComponent } from '../input-month-select/input-month-select.component';
import { OnlyNumbersDirective } from '@app/_directives/only-numbers.directive';
import { CacheService } from '@app/_services/cache.service';

@Component({
  selector: 'app-input-date-period-picker',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatDialogModule,
    InputDropdownComponent,
    ParseRulePipe,
    OnlyNumbersDirective
  ],
  providers: [
    { provide: DateAdapter, useClass: CustomDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS }
  ],
  templateUrl: './input-date-period-picker.component.html',
  styleUrl: './input-date-period-picker.component.scss'
})

export class InputDatePeriodPickerComponent {

  minDate: string = '2000-01-01';
  maxDate: string = '2100-01-01';
  startDate: CustomDate | null;
  endDate: CustomDate | null;
  selectedDate: Date | null = null;
  endDateRepeatModeConfig: Signal<Record<string, InputDropdownConfig>> = computed(() => {
    return {
      until: {
        name: this.cc.texts()['bottomSheet_repeatEndOptions_until'],
        // description: 'End at specific date',
      },
      occurrence: {
        name: this.cc.texts()['bottomSheet_repeatEndOptions_occurrence'],
        // description: 'Ends after no. of occurrences',
      }
    };
  });
  endDateRepeatMode: WritableSignal<EndDateRepeatMode> = signal('until');
  unSubscribe = new Subject<void>();
  initialRuleRecord: Record<string, string> = {};
  ruleRecord: Record<string, string> = {};
  occuranceCount: FormControl = new FormControl(null, [Validators.min(1), Validators.minLength(1), Validators.required, Validators.maxLength(5)]);

  repeatType: WritableSignal<RepeatType> = signal<RepeatType>('DAILY');
  daySignal: WritableSignal<string[]> = signal<string[]>([]);
  weekSignal: WritableSignal<string[]> = signal<string[]>([]);
  dateControl: FormControl = new FormControl([]);
  monthControl: FormControl = new FormControl([]);

  repeatTypeConfig: { [key: string]: InputDropdownConfig } = RepeatTypeConfig;
  dayConfig: { [key: string]: InputDropdownConfig } = DayConfig;
  ordinalConfig: { [key: string]: InputDropdownConfig } = OrdinalConfig;
  ruleString: string;
  initialRepeatValue: string[] = [];

  constructor(
    public dialogRef: MatDialogRef<InputDatePeriodPickerComponent>,
    @Inject(MAT_DIALOG_DATA) public data: InputDatePeriodPickerModel,
    private utilsService: UtilsService,
    private dialog: MatDialog,
    private ds: DependencyService,
    private alertService: AlertService,
    public cc: CacheService,
  ) {

    this.ruleString = this.data.repeatControl.value[0] || '';
    this.initialRuleRecord = this.ds.toRRuleRecord(this.ruleString);
    this.initialRepeatValue = this.data.repeatControl.value || [];
    this.ruleRecord = { ...this.initialRuleRecord };
    const repeatType = this.ds.getRepeatTypeValue(this.ruleString);
    this.repeatType.set(repeatType);
    this.repeatTypeChange(true);

    if (this.ruleRecord['COUNT']) {
      this.endDateRepeatMode.set('occurrence');
      this.occuranceCount.setValue(this.ruleRecord['COUNT']);
    } else {
      this.endDateRepeatMode.set('until');
    }

    this.startDate = data.startDate;
    this.endDate = data.endDate;
    this.selectedDate = data.startDate ? data.startDate.dateTime : null;

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  startDateSelected(event: any) {
    this.selectedDate = new Date(new Date(event._d).getFullYear(), new Date(event._d).getMonth(), new Date(event._d).getDate());
    this.startDate = this.utilsService.getCustomDate(this.selectedDate);
  }

  repeatTypeChange(init: boolean = false) {
    const currentMonth = (new Date().getMonth() + 1).toString();
    const initMonth = this.initialRuleRecord['BYMONTH']?.split(',') || [currentMonth];
    const selectedMonth = init ? initMonth : [currentMonth];

    const defaultDays = ['MO', 'TU', 'WE'];
    const repeatType = this.repeatType();
    this.ruleRecord = {};
    switch (repeatType) {
      case 'DAILY':
        this.ruleRecord['FREQ'] = 'DAILY';
        break;

      case 'WEEK_WORKING_DAY':
        const weekDays = ['MO', 'TU', 'WE', 'TH', 'FR'];
        const initWeekDays = this.initialRuleRecord['BYDAY']?.split(',');
        const selectedWeekDays = init ? initWeekDays : weekDays;
        this.ruleRecord['FREQ'] = 'WEEKLY';
        this.ruleRecord['BYDAY'] = selectedWeekDays.join(',');
        this.daySignal.set(selectedWeekDays);
        break;

      case 'WEEKLY':
        const initByDays = this.initialRuleRecord['BYDAY']?.split(',');
        const selectedByDays = init ? initByDays : defaultDays;
        this.ruleRecord['FREQ'] = 'WEEKLY';
        this.ruleRecord['BYDAY'] = selectedByDays.join(',');
        this.daySignal.set(selectedByDays);
        break;

      case 'MONTHLY':
        const defaultDates = ['5', '6', '7'];
        const initDates = this.initialRuleRecord['BYDAY']?.split(',');
        const selectedDates = init ? initDates : defaultDates;

        this.ruleRecord['FREQ'] = 'MONTHLY';
        this.ruleRecord['BYMONTHDAY'] = selectedDates.join(',');
        this.ruleRecord['BYMONTH'] = selectedMonth.join(',');
        this.dateControl.setValue(selectedDates);
        this.monthControl.setValue(selectedMonth);
        break;

      case 'MONTHLY_NTH_DAY':
        const defaultOrdinals = ['2', '3'];
        const [selectedOrdinals, selectedDays] = init ? this.extractOrdinalsAndDays(this.initialRuleRecord['BYDAY']) : [defaultOrdinals, defaultDays]

        this.ruleRecord['FREQ'] = 'MONTHLY';
        this.ruleRecord['BYDAY'] = this.generateByDay(selectedOrdinals, selectedDays);
        this.daySignal.set(selectedDays);
        this.weekSignal.set(selectedOrdinals);
        break;

      case 'MONTHLY_LAST_DAY':
        this.ruleRecord['FREQ'] = 'MONTHLY';
        this.ruleRecord['BYMONTHDAY'] = '-1';
        this.ruleRecord['BYMONTH'] = selectedMonth.join(',');
        this.monthControl.setValue(selectedMonth);
        break;

      case 'YEARLY':
        const defaultYDate = ['5'];
        const initYDate = this.initialRuleRecord['BYMONTHDAY']?.split(',') || defaultYDate;
        const selectedYDate = init ? initYDate : defaultYDate;
        this.ruleRecord['FREQ'] = 'YEARLY';
        this.ruleRecord['BYMONTHDAY'] = selectedYDate.join(',');
        this.ruleRecord['BYMONTH'] = selectedMonth.join(',');
        this.dateControl.setValue(selectedYDate);
        this.monthControl.setValue(selectedMonth);
        break;

      default:
        this.ruleRecord = {};
        break;
    };
    if (this.initialRuleRecord['COUNT']) {
      this.ruleRecord = Object.fromEntries([
        Object.entries(this.ruleRecord)[0],
        ['COUNT', this.initialRuleRecord['COUNT']],
        ...Object.entries(this.ruleRecord).slice(1)
      ]);
    }
    this.ruleString = this.ds.toRRuleString(this.ruleRecord);
  }

  selectDate() {
    const dateSeletDialog = this.dialog.open(InputDateSelectComponent, {
      width: '100%',
      maxWidth: '330px',
      disableClose: true,
      data: {
        value: this.dateControl.value,
        min: 1
      },
    });
    dateSeletDialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.dateControl.setValue(result);
      }
    });
  }

  selectMonth() {
    const dateSeletDialog = this.dialog.open(InputMonthSelectComponent, {
      width: '100%',
      maxWidth: '330px',
      disableClose: true,
      data: {
        value: this.monthControl.value,
        min: 1
      },
    });
    dateSeletDialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.monthControl.setValue(result);
      }
    });
  }

  updateEndMode() {
    const endDateMode = this.endDateRepeatMode();
    if (endDateMode === 'until') {
      if (this.ruleRecord['COUNT']) {
        delete this.ruleRecord['COUNT'];
      }
    } else {
      this.occuranceCount.setValue('10');
      this.updateOccuranceCount();
    }
  }

  updateOccuranceCount() {
    this.ruleRecord['COUNT'] = this.occuranceCount.value;
  }

  updateDate() {
    this.ruleRecord['BYMONTHDAY'] = this.dateControl.value?.join(',');
    this.ruleString = this.ds.toRRuleString(this.ruleRecord);
  }

  updateDay() {
    if (this.repeatType() === 'MONTHLY_NTH_DAY') {
      this.ruleRecord['BYDAY'] = this.generateByDay(this.weekSignal(), this.daySignal());
      this.ruleString = this.ds.toRRuleString(this.ruleRecord);
    } else {
      this.ruleRecord['BYDAY'] = this.daySignal()?.join(',');
      this.ruleString = this.ds.toRRuleString(this.ruleRecord);
    }
  }

  updateWeek() {
    this.ruleRecord['BYDAY'] = this.generateByDay(this.weekSignal(), this.daySignal());

    console.log("i am called bdaayy", this.ruleRecord)
    this.ruleString = this.ds.toRRuleString(this.ruleRecord);
  }

  updateMonth() {
    this.ruleRecord['BYMONTH'] = this.monthControl.value?.join(',');
    this.ruleString = this.ds.toRRuleString(this.ruleRecord);
  }

  hasChanges(): boolean {
    const initial = _.cloneDeep(this.initialRuleRecord);
    const current = _.cloneDeep(this.ruleRecord);

    return !_.isEqual(initial, current) || !_.isEqual(this.data.startDate, this.startDate) || !_.isEqual(this.data.endDate, this.endDate);
  }

  openInputCalendar() {
    const minDate = this.data.startDate ? new Date(new Date(this.data.startDate.dateTime).getTime() + 24 * 60 * 60 * 1000) : null;

    const dialogRef = this.dialog.open(InputCalendarComponent, {
      maxWidth: '350px',
      width: '100%',
      minWidth: "300px",
      data: new InputCalendarModel(this.data.endDate ? this.data.endDate.dateTime : null, minDate)
    });

    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult) {
        this.endDate = this.utilsService.getCustomDate(dialogResult);
      }
    });
  }

  save() {
    const ruleString = this.ds.toRRuleString(this.ruleRecord);
    this.dialogRef.close({
      startDate: this.startDate,
      endDate: this.endDate,
      repeat: ruleString ? [ruleString] : []
    });
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
        this.dialogRef.close();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }

  generateByDay(weeks: string[], days: string[]): string {
    const combinations = days.flatMap(day => weeks.map(week => `${week}${day}`));
    return combinations.join(',');
  }

  extractOrdinalsAndDays(input: string): [string[], string[]] {
    const items = input.split(',');
    const daySet = new Set<string>();
    const ordinalSet = new Set<string>();

    for (const item of items) {
      const match = item.match(/^(\d+)([A-Z]+)$/);
      if (match) {
        const [, ordinalStr, dayStr] = match;
        ordinalSet.add(ordinalStr);
        daySet.add(dayStr);
      }
    }
    return [Array.from(ordinalSet), Array.from(daySet)];
  }
}

export class InputDatePeriodPickerModel {
  constructor(public startDate: CustomDate | null, public endDate: CustomDate | null, public repeatControl: FormControl, public hiddenValues: string[] = []) {
  }
}
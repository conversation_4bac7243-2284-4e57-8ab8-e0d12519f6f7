import { CustomDate } from "./generic.interface";
import { JsEntity } from "./entity.interface";
import { EntityName, EntityNameType, EntityStatus, FeatureNameType, FeatureStatus } from "@app/_types/generic.type";

export interface FeatureSetup {
    id: string;
    name: FeatureNameType;
    entityName: EntityNameType;
    icon: string;
    description: string;
    status: FeatureStatus;
    disabled: boolean;
    isDefault: boolean;
    startAt: CustomDate | null,
    endAt: CustomDate | null,
    entity: JsEntity | null
}

export interface Feature {
    id: string,
    featureName: FeatureNameType;
    icon: string;
    title: string;
    hashtags: string[];
    relatedEntry: Record<string, JsEntity>;
    setup: JsEntity
}

export interface EntitySetup {
    id: string;
    entityName: EntityName;
    startAt: CustomDate;
    endAt: CustomDate | null;
    title: string;
    duration: number;
    invalid: boolean;
    status: EntityStatus;
    tags: string[];
    percentage: number;
    repeatType: 'all' | 'one_time' | 'repeated';
}

export interface EntityValue {
    id: string;
    repeat: string[];
    startAt: CustomDate;
    endAt: CustomDate | null;
    title: string;
    duration: number;
    tags?: string[];
    deletedAt?: Date | null;
}

export interface CalendarEntitySetup {
    id: string;
    start: Date;
    end?: Date;
    title: string;
    cssClass: EntityStatus;
    meta: {
        entity: EntityName;
        startAt: CustomDate;
        endAt: CustomDate | null;
        invalid: boolean;
    }
}

export interface CalendarEventEntity {
    id: string;
    data: any;
    localUpdatedAt?: Date;
    updateType: 'raw' | 'processed';
    isCreate: boolean;
}

export interface EntityGroup {
    id: string;
    name: string;
    data: EntitySetup[];
}

export interface EntityWithDateGroup {
    id: string;
    name: string;
    data: EntitySetup[];
    dateMap: { [dateString: string]: EntitySetup[] };
}

export interface EntityFlatenedGroup {
    id: string;
    name: string;
    type: 'group' | 'entry';
    dateString?: string;
    entity?: EntitySetup;
}
<div class="top-section">
    <h6 class="heading mb-0">{{ cc.texts()['screen_calendarAccount_title'] }}</h6>
    <div class="flex">
        <app-svg name="more" class="more-icon ri-me-6" [matMenuTriggerFor]="infoMenu" role="button" *ngIf="data.mode === 'edit'" [color]="cc.theme.color12"></app-svg>
        <mat-menu #infoMenu="matMenu" class="me-menu info-menu ri-w-350px">
            <button mat-menu-item class="text-center text-14-400 color-8" (click)="save(true)"><span class="w-full text-center">{{ cc.texts()['dropdown_calendarAccount_resyncCalendar'] }}</span></button>
            <button mat-menu-item (click)="deleteCalendar()"><span class="text-center text-14-400 color-11 w-full">{{ cc.texts()['dropdown_calendarAccount_deleteAccount'] }}</span></button>
        </mat-menu>

        <app-svg name="close" role="button" (click)="closeDialog()"></app-svg>
    </div>
</div>
<div class="calendar-account-form ri-p-4">
    <form [formGroup]="calendarAccountForm" #caForm="ngForm">
        <div class="flex items-start ri-pb-4">
            <app-svg name="user" [color]="cc.theme.color35"></app-svg>
            <div class="ri-ps-3 w-full">
                <p class="text-16-400 color-8 mb-0 ri-pb-2">{{ calendarAccountInitial.name }}</p>
                <p class="text-12-400 color-7 mb-0">{{ calendarAccountInitial.email }}</p>
            </div>
        </div>
        <div class="flex items-start ri-pb-4">
            <app-svg name="mevolveToCalendar" [color]="cc.theme.color35"></app-svg>
            <div class="ri-ps-3 w-full">
                <p class="text-16-400 color-8 mb-0 ri-pb-2">{{ cc.texts()['screen_calendarAccount_mevolveToCalendar'] }}</p>
                <p class="text-12-400 color-7 mb-0 ri-pb-2">{{ cc.texts()['screen_calendarAccount_mevolveContent'] }}</p>
                <div class="flex justify-between items-center ri-pb-2">
                    <p class="text-16-400 color-8 mb-0 flex items-center">{{ cc.texts()['screen_common_todos'] }} <app-svg *ngIf="syncConfig['syncTodos']()" [name]="calendarAccountForm.value.syncTodos === 'syncNotifications' ? 'notification' : 'noNotification'" [color]="calendarConfig['syncTodos'] === 'syncNotifications' ? cc.theme.color35 : cc.theme.color7" class="ri-ms-2" (click)="syncNotification('syncTodos')" role="button"></app-svg></p>
                    <app-input-toggle [signal]="syncConfig['syncTodos']" (change)="syncChange('syncTodos')"></app-input-toggle>
                </div>
                <div class="flex justify-between items-center ri-pb-2">
                    <p class="text-16-400 color-8 mb-0 flex items-center">{{ cc.texts()['screen_common_habits'] }} <app-svg *ngIf="syncConfig['syncHabits']()" [name]="calendarAccountForm.value.syncHabits === 'syncNotifications' ? 'notification' : 'noNotification'" [color]="calendarConfig['syncHabits'] === 'syncNotifications' ? cc.theme.color35 : cc.theme.color7" class="ri-ms-2" (click)="syncNotification('syncHabits')" role="button"></app-svg></p>
                    <app-input-toggle [signal]="syncConfig['syncHabits']" (change)="syncChange('syncHabits')"></app-input-toggle>
                </div>
                <div class="flex justify-between items-center ri-pb-2">
                    <p class="text-16-400 color-8 mb-0 flex items-center">{{ cc.texts()['screen_common_journals'] }} <app-svg *ngIf="syncConfig['syncJournals']()" [name]="calendarAccountForm.value.syncJournals === 'syncNotifications' ? 'notification' : 'noNotification'" [color]="calendarConfig['syncJournals'] === 'syncNotifications' ? cc.theme.color35 : cc.theme.color7" class="ri-ms-2" (click)="syncNotification('syncJournals')" role="button"></app-svg></p>
                    <app-input-toggle [signal]="syncConfig['syncJournals']" (change)="syncChange('syncJournals')"></app-input-toggle>
                </div>
            </div>
        </div>
        <div class="flex items-start ri-pb-4">
            <app-svg name="calendarToMevolve" [color]="cc.theme.color35"></app-svg>
            <div class="ri-ps-3 w-full">
                <p class="text-16-400 color-8 mb-0 ri-pb-2">{{ cc.texts()['screen_calendarAccount_calendarToMevolve'] }}</p>
                <p class="text-12-400 color-7 mb-0 ri-pb-2">{{ cc.texts()['screen_calendarAccount_calendarContent'] }}</p>
                <ng-container *ngFor="let calGroup of calendarAccountForm.value.calendarGroups">
                    <div class="flex justify-between items-center ri-pb-2" *ngIf="calGroup.title !== 'Mevolve'">
                        <p class="text-16-400 color-8 mb-0 flex items-center">{{ calGroup.title }}<app-svg *ngIf="calGroup.syncStatus !== 'disabled'" [name]="calGroup.syncStatus === 'syncNotifications' ? 'notification' : 'noNotification'" [color]="calendarConfig[calGroup.id] === 'syncNotifications' ? cc.theme.color35 : cc.theme.color7" class="ri-ms-2" (click)="syncNotification(calGroup.id)" role="button"></app-svg></p>
                        <app-input-toggle [signal]="syncConfig[calGroup.id]" (change)="syncChange(calGroup.id)"></app-input-toggle>
                    </div>
                </ng-container>
            </div>
        </div>
    </form>
</div>

<!-- <pre class="text-white">{{calendarAccountForm.value | json}}</pre>
initial----
 <pre class="text-white">{{calendarAccountInitial | json}}</pre> -->
<div class="bottom-section flex items-center justify-end ri-p-4 ri-bt-2">
    <button type="submit" class="btn-text text-16-500 color-1" (click)="(hasChanges() || data.mode === 'new' ) ? save() : closeDialog()">{{ (hasChanges() || data.mode === 'new' ) ? cc.texts()['screen_common_save'] : cc.texts()['screen_common_close'] }}</button>
</div>
import { CommonModule } from '@angular/common';
import { Component, Input, Signal, signal } from '@angular/core';
import { SvgComponent } from '../../svg/svg.component';
import { MatMenuModule } from '@angular/material/menu';
import { CacheService } from '@app/_services/cache.service';
import { RepeatFilterType } from '@app/_types/generic.type';
import { repeatTypes } from '@app/_datas/const.data';
import { MapService } from '@app/_services/map.service';

@Component({
  selector: 'app-filter-type',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    MatMenuModule
  ],
  templateUrl: './filter-type.component.html',
  styleUrl: './filter-type.component.scss'
})

export class FilterTypeComponent {

  @Input() value = signal<RepeatFilterType>('all');
  @Input() multiple: boolean = false;
  @Input() label: string = '';
  repeatTypes: RepeatFilterType[] = repeatTypes;
  repeatTypeMap: Signal<Record<RepeatFilterType, string>> = this.mapService.RepeatFilterTypeMap;

  constructor(public cc: CacheService, private mapService: MapService) {

  }

  onTypeSelected(value: RepeatFilterType, event: MouseEvent): void {
    this.value.set(value);
  }
}

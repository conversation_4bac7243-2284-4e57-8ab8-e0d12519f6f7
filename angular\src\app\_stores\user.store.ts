import { patchState, signalStore, withComputed, withHooks, withMethods, withState } from "@ngrx/signals";
import { Hashtag, User, UserData, UserMetaData, UserResource, UserViewSettings } from "@app/_interfaces/user.interface";
import { computed, inject, signal, Signal, WritableSignal } from "@angular/core";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { IndexDbService } from "@app/_services/index-db.service";
import { CryptographyService } from "@app/_services/cryptography.service";
import { FirebaseFunctionService } from "@app/_services/firebase-function.service";
import { StorageService } from "@app/_services/storage.servive";
import { firstValueFrom } from "rxjs";
import { FirestoreCollection } from "@app/_enums/firestore-collection.enum";
import { UtilsService } from "@app/_services/utils.service";
import { getNewViewSettings } from "@app/_datas/initial.data";
import { DependencyService } from "@app/_services/dependency.service";

type UserState = {
  user?: UserData;
  userResource: UserResource | null;
  userMetadata: UserMetaData | null;
  viewSettings: UserViewSettings;
};

const initialState: UserState = {
  user: undefined,
  userResource: null,
  userMetadata: null,
  viewSettings: getNewViewSettings(),
};

export const UserStore = signalStore(
  { providedIn: 'root' },

  withState(initialState),

  withComputed((store, ds = inject(DependencyService),) => ({
    hashtags: computed(() => {
      return store.userResource()?.tags ?? [];
    }),

    tagMap: computed(() => {
      return store.userResource()?.tags ? Object.fromEntries(store.userResource()!.tags.map(item => [item.id, item.tag])) : {};
    }),

    viewSignals: computed<Record<string, WritableSignal<any>>>(() => {
      return {
        // Notes
        noteGroupBy: signal(store.viewSettings().noteSettings.myNoteSettings.notesGroupBy),
        noteShowType: signal(store.viewSettings().noteSettings.myNoteSettings.viewType),
        noteShow: signal(ds.getShowValues(store.viewSettings().noteSettings.myNoteSettings)),
        noteDescriptionType: signal(store.viewSettings().noteSettings.myNoteSettings.noteDescriptionType),
        noteGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().noteSettings.myNoteSettings)),

        // Lists
        listGroupBy: signal(store.viewSettings().listSettings.myListSettings.groupByType),
        listShowType: signal(store.viewSettings().listSettings.myListSettings.viewSettingType),
        listShow: signal(ds.getShowValues(store.viewSettings().listSettings.myListSettings)),
        listGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().listSettings.myListSettings)),
        listItemShowType: signal(store.viewSettings().listSettings.myListItemSheetSettings.viewType),
        listItemShow: signal(ds.getShowValues(store.viewSettings().listSettings.myListItemSheetSettings)),

        // Features

        showTodoFeature: signal(store.viewSettings().featureSettings.showTodoFeature),
        showNoteFeature: signal(store.viewSettings().featureSettings.showNoteFeature),
        showListFeature: signal(store.viewSettings().featureSettings.showListFeature),

        // Entitys
        entityGroupBy: signal(store.viewSettings().featureSettings.viewType),
        entityShowType: signal(store.viewSettings().todaySettings.todayTabSettings.viewSettingType),
        entityShow: signal(ds.getShowValues({...store.viewSettings().todaySettings.todayTabSettings, showLabel: store.viewSettings().featureSettings.showFeatureLabels })),
        entityDescriptionType: signal(store.viewSettings().todaySettings.todayTabSettings.descriptionType),
        userGoal: signal(store.viewSettings().featureSettings.userGoal),
        showUserGoal: signal(store.viewSettings().featureSettings.showUserGoal),
        showTimebox: signal(store.viewSettings().featureSettings.showTimebox),
        showCalendarView: signal(store.viewSettings().featureSettings.showCalendarView),
        hideCompletedItems: signal(store.viewSettings().featureSettings.hideCompletedItems),

        // Past todo screen
        pastTodoGroupBy: signal(store.viewSettings().pastSettings.pastTodoSettings.groupBy),
        pastTodoGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().pastSettings.pastTodoSettings)),
        pastTodoShowType: signal(store.viewSettings().pastSettings.pastTodoSettings.viewSettingType),
        pastTodoShow: signal(ds.getShowValues(store.viewSettings().pastSettings.pastTodoSettings)),
        pastTodoDescriptionType: signal(store.viewSettings().pastSettings.pastTodoSettings.descriptionType),

        // Past journal screen
        pastJournalGroupBy: signal(store.viewSettings().pastSettings.pastJournalSettings.groupBy),
        pastJournalGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().pastSettings.pastJournalSettings)),
        pastJournalShowType: signal(store.viewSettings().pastSettings.pastJournalSettings.viewSettingType),
        pastJournalShow: signal(ds.getShowValues(store.viewSettings().pastSettings.pastJournalSettings)),
        pastJournalDescriptionType: signal(store.viewSettings().pastSettings.pastJournalSettings.descriptionType),

        // Past habit screen
        pastHabitGroupBy: signal(store.viewSettings().pastSettings.pastHabitSettings.groupBy),
        pastHabitGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().pastSettings.pastHabitSettings)),
        pastHabitShowType: signal(store.viewSettings().pastSettings.pastHabitSettings.viewSettingType),
        pastHabitShow: signal(ds.getShowValues(store.viewSettings().pastSettings.pastHabitSettings)),
        pastHabitDescriptionType: signal(store.viewSettings().pastSettings.pastHabitSettings.descriptionType),

        // Past money tracker screen
        pastMoneyTrackerGroupBy: signal(store.viewSettings().pastSettings.pastMoneyTrackerSettings.groupBySettings),
        pastMoneyTrackerGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().pastSettings.pastMoneyTrackerSettings)),
        pastMoneyTrackerShowType: signal(store.viewSettings().pastSettings.pastMoneyTrackerSettings.viewSettingType),
        pastMoneyTrackerShow: signal(ds.getShowValues(store.viewSettings().pastSettings.pastMoneyTrackerSettings)),
        pastMoneyTrackerDescriptionType: signal(store.viewSettings().pastSettings.pastMoneyTrackerSettings.descriptionType),

        // Past calendar event screen
        pastCalendarEventGroupBy: signal(store.viewSettings().pastSettings.pastCalendarEventSettings.groupBy),
        pastCalendarEventGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().pastSettings.pastCalendarEventSettings)),
        pastCalendarEventShowType: signal(store.viewSettings().pastSettings.pastCalendarEventSettings.viewSettingType),
        pastCalendarEventShow: signal(ds.getShowValues(store.viewSettings().pastSettings.pastCalendarEventSettings)),

        // Future todo screen
        futureTodoGroupBy: signal(store.viewSettings().futureSettings.futureTodoSettings.groupBy),
        futureTodoGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().futureSettings.futureTodoSettings)),
        futureTodoShowType: signal(store.viewSettings().futureSettings.futureTodoSettings.viewSettingType),
        futureTodoShow: signal(ds.getShowValues(store.viewSettings().futureSettings.futureTodoSettings)),
        futureTodoDescriptionType: signal(store.viewSettings().futureSettings.futureTodoSettings.descriptionType),

        // Future journal screen
        futureJournalGroupBy: signal(store.viewSettings().futureSettings.futureJournalSettings.groupBy),
        futureJournalGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().futureSettings.futureJournalSettings)),
        futureJournalShowType: signal(store.viewSettings().futureSettings.futureJournalSettings.viewSettingType),
        futureJournalShow: signal(ds.getShowValues(store.viewSettings().futureSettings.futureJournalSettings)),
        futureJournalDescriptionType: signal(store.viewSettings().futureSettings.futureJournalSettings.descriptionType),

        // Future habit screen
        futureHabitGroupBy: signal(store.viewSettings().futureSettings.futureHabitSettings.groupBy),
        futureHabitGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().futureSettings.futureHabitSettings)),
        futureHabitShowType: signal(store.viewSettings().futureSettings.futureHabitSettings.viewSettingType),
        futureHabitShow: signal(ds.getShowValues(store.viewSettings().futureSettings.futureHabitSettings)),
        futureHabitDescriptionType: signal(store.viewSettings().futureSettings.futureHabitSettings.descriptionType),

        // Future money tracker screen
        futureMoneyTrackerGroupBy: signal(store.viewSettings().futureSettings.futureMoneyTrackerSettings.groupBySettings),
        futureMoneyTrackerGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().futureSettings.futureMoneyTrackerSettings)),
        futureMoneyTrackerShowType: signal(store.viewSettings().futureSettings.futureMoneyTrackerSettings.viewSettingType),
        futureMoneyTrackerShow: signal(ds.getShowValues(store.viewSettings().futureSettings.futureMoneyTrackerSettings)),
        futureMoneyTrackerDescriptionType: signal(store.viewSettings().futureSettings.futureMoneyTrackerSettings.descriptionType),

        // Future calendar event screen
        futureCalendarEventGroupBy: signal(store.viewSettings().futureSettings.futureCalendarEventSettings.groupBy),
        futureCalendarEventGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().futureSettings.futureCalendarEventSettings)),
        futureCalendarEventShowType: signal(store.viewSettings().futureSettings.futureCalendarEventSettings.viewSettingType),
        futureCalendarEventShow: signal(ds.getShowValues(store.viewSettings().futureSettings.futureCalendarEventSettings)),

      };
    }),

  })),

  withMethods((
    store,
    idbService = inject(IndexDbService),
    cryptoService = inject(CryptographyService),
    firebaseFunctionService = inject(FirebaseFunctionService),
    ss = inject(StorageService),
    utilsService = inject(UtilsService),
  ) => ({

    addHashtag: async (hashtag: Hashtag) => {
      const userResource = store.userResource();
      if (!userResource) {
        return;
      }
      const oldUserResource = await firstValueFrom(idbService.getEntityById('userResources', userResource.id));

      userResource.tags.push(hashtag);
      userResource.localUpdatedAt = new Date();
      userResource.cloudUpdatedAt = null;
      userResource.lastUpdatedAt = new Date();

      const newUserResource = await idbService.update('userResources', userResource.id, userResource);

      const syncRequest = cryptoService.preparePatchData(
        [oldUserResource],
        [newUserResource],
        FirestoreCollection.UserResources
      );
      await firebaseFunctionService.uploadData(syncRequest);
    },

    updateHashtag: async (hashtag: Hashtag) => {
      const userResource = store.userResource();

      if (!userResource) {
        return;
      }
      const oldUserResource = await firstValueFrom(idbService.getEntityById('userResources', userResource.id));
      userResource.tags = userResource.tags.map(tag =>
        tag.id === hashtag.id ? { ...tag, ...hashtag } : tag
      );
      userResource.localUpdatedAt = new Date();
      userResource.cloudUpdatedAt = null;
      userResource.lastUpdatedAt = new Date();

      const newUserResource = await idbService.update('userResources', userResource.id, userResource);
      const syncRequest = cryptoService.preparePatchData(
        [oldUserResource],
        [newUserResource],
        FirestoreCollection.UserResources
      );
      await firebaseFunctionService.uploadData(syncRequest);
    },

    deleteHashtag: async (hashtag: Hashtag) => {
      const userResource = store.userResource();

      if (!userResource) {
        return;
      }

      userResource.tags = userResource.tags.filter(tag => tag.id !== hashtag.id);

      userResource.localUpdatedAt = new Date();
      userResource.cloudUpdatedAt = null;
      userResource.lastUpdatedAt = new Date();

      idbService.update('userResources', userResource.id, userResource);
      const syncRequest = cryptoService.prepareRawData({ ...userResource });
      await firebaseFunctionService.uploadData(syncRequest);
    },

    async updateViewSettings() {
      const viewSignals = store.viewSignals();
      const viewSettings = {
        ...store.viewSettings(),
        noteSettings: {
          ...store.viewSettings().noteSettings,
          myNoteSettings: {
            ...store.viewSettings().noteSettings.myNoteSettings,
            noteDescriptionType: viewSignals['noteDescriptionType'](),
            noteViewTime: viewSignals['noteShow']().includes('time'),
            noteViewDate: viewSignals['noteShow']().includes('date'),
            noteViewImage: viewSignals['noteShow']().includes('attachments'),
            noteViewMood: viewSignals['noteShow']().includes('mood'),
            noteViewTags: viewSignals['noteShow']().includes('hashtag'),
            notesGroupBy: viewSignals['noteGroupBy'](),
            viewType: viewSignals['noteShowType'](),
            collapsedView: viewSignals['noteGroupedViewType']().includes('collapsedView'),
            showCounts: viewSignals['noteGroupedViewType']().includes('showCounts'),
            showMemberCount: viewSignals['noteShow']().includes('collaboratorsCount'),
          },
        },
        listSettings: {
          ...store.viewSettings().listSettings,
          myListSettings: {
            ...store.viewSettings().listSettings.myListSettings,
            showDescription: viewSignals['listShow']().includes('description'),
            itemCount: viewSignals['listShow']().includes('itemCount'),
            showCollaboratorsCount: viewSignals['listShow']().includes('collaboratorsCount'),
            showInvitedUsersCount: viewSignals['listShow']().includes('invitedUsersCount'),
            showAwaitingUserCount: viewSignals['listShow']().includes('awaitingUserCount'),
            showHashtags: viewSignals['listShow']().includes('hashtag'),
            groupByType: viewSignals['listGroupBy'](),
            collapsedView: viewSignals['listGroupedViewType']().includes('collapsedView'),
            showCounts: viewSignals['listGroupedViewType']().includes('showCounts'),
            viewSettingType: viewSignals['listShowType'](),
          },
          myListItemSheetSettings: {
            ...store.viewSettings().listSettings.myListItemSheetSettings,
            showDescription: viewSignals['listItemShow']().includes('description'),
            viewType: viewSignals['listItemShowType'](),
            showLastUpdatedBy: viewSignals['listItemShow']().includes('lastUpdatedBy'),
            showLastUpdatedAt: viewSignals['listItemShow']().includes('lastUpdatedAt'),
          },
        },
        featureSettings: {
          ...store.viewSettings().featureSettings,
          viewType: viewSignals['entityGroupBy'](),
          userGoal: viewSignals['userGoal'](),
          showUserGoal: viewSignals['showUserGoal'](),
          showTimebox: viewSignals['showTimebox'](),
          showCalendarView: viewSignals['showCalendarView'](),
          hideCompletedItems: viewSignals['hideCompletedItems'](),
          showFeatureLabels: viewSignals['entityShow']().includes('label'),
          showTodoFeature: viewSignals['showTodoFeature'](),
          showNoteFeature: viewSignals['showNoteFeature'](),
          showListFeature: viewSignals['showListFeature'](),
        },
        todaySettings: {
          ...store.viewSettings().todaySettings,
          todayTabSettings: {
            ...store.viewSettings().todaySettings.todayTabSettings,
            descriptionType: viewSignals['entityDescriptionType'](),
            viewSettingType: viewSignals['entityShowType'](),
            showMood: viewSignals['entityShow']().includes('mood'),
            showTime: viewSignals['entityShow']().includes('time'),
            showReminder: viewSignals['entityShow']().includes('reminder'),
            showDuration: viewSignals['entityShow']().includes('duration'),
            showRepeat: viewSignals['entityShow']().includes('repeat'),
            showChecklist: viewSignals['entityShow']().includes('checklist'),
            showImage: viewSignals['entityShow']().includes('attachments'),
            showTags: viewSignals['entityShow']().includes('hashtag'),
            showHabitResponse: viewSignals['entityShow']().includes('habitResponse')
          },
        },
        pastSettings: {
          ...store.viewSettings().pastSettings,
          pastTodoSettings: {
            ...store.viewSettings().pastSettings.pastTodoSettings,
            groupBy: viewSignals['pastTodoGroupBy'](),
            viewSettingType: viewSignals['pastTodoShowType'](),
            collapsedView: viewSignals['pastTodoGroupedViewType']().includes('collapsedView'),
            showCounts: viewSignals['pastTodoGroupedViewType']().includes('showCounts'),
            descriptionType: viewSignals['pastTodoDescriptionType'](),
            showDate: viewSignals['pastTodoShow']().includes('date'),
            showTime: viewSignals['pastTodoShow']().includes('time'),
            showReminder: viewSignals['pastTodoShow']().includes('reminder'),
            showDuration: viewSignals['pastTodoShow']().includes('duration'),
            showRepeat: viewSignals['pastTodoShow']().includes('repeat'),
            showChecklist: viewSignals['pastTodoShow']().includes('checklist'),
            showImage: viewSignals['pastTodoShow']().includes('attachments'),
            showTags: viewSignals['pastTodoShow']().includes('hashtag'),
            showEmptyDays: viewSignals['pastTodoShow']().includes('emptyDays'),
            showInvalidEntries: viewSignals['pastTodoShow']().includes('invalidEntry'),
          },
          pastJournalSettings: {
            ...store.viewSettings().pastSettings.pastJournalSettings,
            groupBy: viewSignals['pastJournalGroupBy'](),
            viewSettingType: viewSignals['pastJournalShowType'](),
            collapsedView: viewSignals['pastJournalGroupedViewType']().includes('collapsedView'),
            showCounts: viewSignals['pastJournalGroupedViewType']().includes('showCounts'),
            descriptionType: viewSignals['pastJournalDescriptionType'](),
            showDate: viewSignals['pastJournalShow']().includes('date'),
            showTime: viewSignals['pastJournalShow']().includes('time'),
            showReminder: viewSignals['pastJournalShow']().includes('reminder'),
            showDuration: viewSignals['pastJournalShow']().includes('duration'),
            showRepeat: viewSignals['pastJournalShow']().includes('repeat'),
            showImage: viewSignals['pastJournalShow']().includes('attachments'),
            showMood: viewSignals['pastJournalShow']().includes('mood'),
            showTags: viewSignals['pastJournalShow']().includes('hashtag'),
            showEmptyDays: viewSignals['pastJournalShow']().includes('emptyDays'),
            showInvalidEntries: viewSignals['pastJournalShow']().includes('invalidEntry'),
          },
          pastHabitSettings: {
            ...store.viewSettings().pastSettings.pastHabitSettings,
            groupBy: viewSignals['pastHabitGroupBy'](),
            viewSettingType: viewSignals['pastHabitShowType'](),
            collapsedView: viewSignals['pastHabitGroupedViewType']().includes('collapsedView'),
            showCounts: viewSignals['pastHabitGroupedViewType']().includes('showCounts'),
            descriptionType: viewSignals['pastHabitDescriptionType'](),
            showHabitResponse: viewSignals['pastHabitShow']().includes('habitResponse'),
            showDate: viewSignals['pastHabitShow']().includes('date'),
            showTime: viewSignals['pastHabitShow']().includes('time'),
            showReminder: viewSignals['pastHabitShow']().includes('reminder'),
            showDuration: viewSignals['pastHabitShow']().includes('duration'),
            showRepeat: viewSignals['pastHabitShow']().includes('repeat'),
            showImage: viewSignals['pastHabitShow']().includes('attachments'),
            showTags: viewSignals['pastHabitShow']().includes('hashtag'),
            showEmptyDays: viewSignals['pastHabitShow']().includes('emptyDays'),
            showInvalidEntries: viewSignals['pastHabitShow']().includes('invalidEntry'),
          },
          pastMoneyTrackerSettings: {
            ...store.viewSettings().pastSettings.pastMoneyTrackerSettings,
            collapsedView: viewSignals['pastMoneyTrackerGroupedViewType']().includes('collapsedView'),
            descriptionType: viewSignals['pastMoneyTrackerDescriptionType'](),
            groupBySettings: viewSignals['pastMoneyTrackerGroupBy'](),
            viewSettingType: viewSignals['pastMoneyTrackerShowType'](),
            showImage: viewSignals['pastMoneyTrackerShow']().includes('attachments'),
            showHashtag: viewSignals['pastMoneyTrackerShow']().includes('hashtag'),
            showEmptyDays: viewSignals['pastMoneyTrackerShow']().includes('emptyDays'),
            showNetAmount: viewSignals['pastMoneyTrackerGroupedViewType']().includes('showNetAmount'),
            showSetupTitle: viewSignals['pastMoneyTrackerShow']().includes('setup'),
          },
          pastCalendarEventSettings: {
            ...store.viewSettings().pastSettings.pastCalendarEventSettings,
            groupBy: viewSignals['pastCalendarEventGroupBy'](),
            viewSettingType: viewSignals['pastCalendarEventShowType'](),
            collapsedView: viewSignals['pastCalendarEventGroupedViewType']().includes('collapsedView'),
            showCounts: viewSignals['pastCalendarEventGroupedViewType']().includes('showCounts'),
            showTime: viewSignals['pastCalendarEventShow']().includes('time'),
            showReminder: viewSignals['pastCalendarEventShow']().includes('reminder'),
            showDuration: viewSignals['pastCalendarEventShow']().includes('duration'),
            showRepeat: viewSignals['pastCalendarEventShow']().includes('repeat'),
            showCalendarName: viewSignals['pastCalendarEventShow']().includes('calendarName'),
          },
        },
        futureSettings: {
          ...store.viewSettings().futureSettings,
          futureTodoSettings: {
            ...store.viewSettings().futureSettings.futureTodoSettings,
            groupBy: viewSignals['futureTodoGroupBy'](),
            viewSettingType: viewSignals['futureTodoShowType'](),
            collapsedView: viewSignals['futureTodoGroupedViewType']().includes('collapsedView'),
            showCounts: viewSignals['futureTodoGroupedViewType']().includes('showCounts'),
            descriptionType: viewSignals['futureTodoDescriptionType'](),
            showTime: viewSignals['futureTodoShow']().includes('time'),
            showReminder: viewSignals['futureTodoShow']().includes('reminder'),
            showDuration: viewSignals['futureTodoShow']().includes('duration'),
            showRepeat: viewSignals['futureTodoShow']().includes('repeat'),
            showChecklist: viewSignals['futureTodoShow']().includes('checklist'),
            showImage: viewSignals['futureTodoShow']().includes('attachments'),
            showTags: viewSignals['futureTodoShow']().includes('hashtag'),
            showEmptyDays: viewSignals['futureTodoShow']().includes('emptyDays'),
            showInvalidEntries: viewSignals['futureTodoShow']().includes('invalidEntry'),
          },
          futureJournalSettings: {
            ...store.viewSettings().futureSettings.futureJournalSettings,
            groupBy: viewSignals['futureJournalGroupBy'](),
            viewSettingType: viewSignals['futureJournalShowType'](),
            collapsedView: viewSignals['futureJournalGroupedViewType']().includes('collapsedView'),
            showCounts: viewSignals['futureJournalGroupedViewType']().includes('showCounts'),
            descriptionType: viewSignals['futureJournalDescriptionType'](),
            showTime: viewSignals['futureJournalShow']().includes('time'),
            showReminder: viewSignals['futureJournalShow']().includes('reminder'),
            showDuration: viewSignals['futureJournalShow']().includes('duration'),
            showRepeat: viewSignals['futureJournalShow']().includes('repeat'),
            showImage: viewSignals['futureJournalShow']().includes('attachments'),
            showTags: viewSignals['futureJournalShow']().includes('hashtag'),
            showEmptyDays: viewSignals['futureJournalShow']().includes('emptyDays'),
            showInvalidEntries: viewSignals['futureJournalShow']().includes('invalidEntry'),
          },
          futureHabitSettings: {
            ...store.viewSettings().futureSettings.futureHabitSettings,
            groupBy: viewSignals['futureHabitGroupBy'](),
            viewSettingType: viewSignals['futureHabitShowType'](),
            collapsedView: viewSignals['futureHabitGroupedViewType']().includes('collapsedView'),
            showCounts: viewSignals['futureHabitGroupedViewType']().includes('showCounts'),
            descriptionType: viewSignals['futureHabitDescriptionType'](),
            showHabitResponse: viewSignals['futureHabitShow']().includes('habitResponse'),
            showDate: viewSignals['futureHabitShow']().includes('date'),
            showTime: viewSignals['futureHabitShow']().includes('time'),
            showReminder: viewSignals['futureHabitShow']().includes('reminder'),
            showDuration: viewSignals['futureHabitShow']().includes('duration'),
            showRepeat: viewSignals['futureHabitShow']().includes('repeat'),
            showImage: viewSignals['futureHabitShow']().includes('attachments'),
            showTags: viewSignals['futureHabitShow']().includes('hashtag'),
            showEmptyDays: viewSignals['futureHabitShow']().includes('emptyDays'),
            showInvalidEntries: viewSignals['futureHabitShow']().includes('invalidEntry')
          },
          futureMoneyTrackerSettings: {
            ...store.viewSettings().futureSettings.futureMoneyTrackerSettings,
            collapsedView: viewSignals['futureMoneyTrackerGroupedViewType']().includes('collapsedView'),
            descriptionType: viewSignals['futureMoneyTrackerDescriptionType'](),
            groupBySettings: viewSignals['futureMoneyTrackerGroupBy'](),
            viewSettingType: viewSignals['futureMoneyTrackerShowType'](),
            showImage: viewSignals['futureMoneyTrackerShow']().includes('attachments'),
            showHashtag: viewSignals['futureMoneyTrackerShow']().includes('hashtag'),
            showEmptyDays: viewSignals['futureMoneyTrackerShow']().includes('emptyDays'),
            showNetAmount: viewSignals['futureMoneyTrackerGroupedViewType']().includes('showNetAmount'),
            showSetupTitle: viewSignals['futureMoneyTrackerShow']().includes('setup'),
          },
          futureCalendarEventSettings: {
            ...store.viewSettings().futureSettings.futureCalendarEventSettings,
            collapsedView: viewSignals['futureCalendarEventGroupedViewType']().includes('collapsedView'),
            groupBy: viewSignals['futureCalendarEventGroupBy'](),
            showCalendarName: viewSignals['futureCalendarEventShow']().includes('calendarName'),
            showCounts: viewSignals['futureCalendarEventGroupedViewType']().includes('showCounts'),
            showTime: viewSignals['futureCalendarEventShow']().includes('time'),
            showReminder: viewSignals['futureCalendarEventShow']().includes('reminder'),
            showDuration: viewSignals['futureCalendarEventShow']().includes('duration'),
            showRepeat: viewSignals['futureCalendarEventShow']().includes('repeat'),
            viewSettingType: viewSignals['futureCalendarEventShowType'](),
          },
        },
      };

      const oldViewSettings = await firstValueFrom(idbService.getEntityById('viewSettings', viewSettings.id));
      const newViewSettings = await idbService.update('viewSettings', viewSettings.id, viewSettings);
      newViewSettings!.localUpdatedAt = new Date();
      newViewSettings!.cloudUpdatedAt = null;

      const syncRequest = cryptoService.preparePatchData(
        [oldViewSettings],
        [newViewSettings],
        FirestoreCollection.ViewSettings
      );

      const res = await firebaseFunctionService.uploadData(syncRequest);
      console.log(" i am called res-->>", res);
      // if(res.success)
    },

    // getNewViewSettings: (): UserViewSettings => {
    //   const user = store.user?.();
    //   return {
    //     id: `vs_${user?.uid}`,
    //     uid: user?.uid ?? '',
    //     docVer: dbVersion,
    //     docCollection: FirestoreCollection.ViewSettings.toString(),
    //     encData: {
    //       dek: cryptoService.createEncryptedDocKey(),
    //       encFields: ['featureSettings.userGoal']
    //     },
    //     source: 'client',
    //     appSettings: {
    //       themeColor: "theme1",
    //       language: "english",
    //       supportLanguage: "english",
    //       isSpeechToTextEnabled: true,
    //       appTheme: "dark",
    //       isVibrationEnabled: false
    //     },
    //     featureSettings: {
    //       showFeatureLabels: true,
    //       userGoal: null,
    //       viewType: "chronological",
    //       showListFeature: true,
    //       showCalendarView: false,
    //       showTodoFeature: true,
    //       showNoteFeature: true,
    //       showUserGoal: false,
    //       hideCompletedItems: false,
    //       showTimebox: true
    //     },
    //     futureSettings: {
    //       futureHabitSettings: {
    //         showCounts: false,
    //         showCalendarName: false,
    //         showHabitResponse: false,
    //         showTime: true,
    //         viewSettingType: "compact",
    //         showRepeat: false,
    //         collapsedView: false,
    //         showReminder: false,
    //         showEmptyDays: false,
    //         descriptionType: "none",
    //         showDuration: false,
    //         showDate: false,
    //         showInvalidEntries: false,
    //         groupBy: "date",
    //         showTags: false,
    //         showImage: false
    //       },
    //       futureJournalSettings: {
    //         showMood: false,
    //         showDuration: false,
    //         collapsedView: false,
    //         showCounts: false,
    //         showTags: false,
    //         viewSettingType: "compact",
    //         showRepeat: false,
    //         descriptionType: "none",
    //         showInvalidEntries: false,
    //         showTime: true,
    //         showEmptyDays: false,
    //         showImage: false,
    //         showReminder: false,
    //         groupBy: "date",
    //         showDate: false
    //       },
    //       futureMoneyTrackerSettings: {
    //         showHashtag: false,
    //         groupBySettings: "date",
    //         viewSettingType: "compact",
    //         showDate: false,
    //         showEmptyDays: false,
    //         showImage: false,
    //         descriptionType: "none",
    //         showNetAmount: false,
    //         collapsedView: false,
    //         showSetupTitle: true
    //       },
    //       futureCalendarEventSettings: {
    //         showTime: true,
    //         showCounts: false,
    //         collapsedView: false,
    //         showCalendarName: false,
    //         showRepeat: false,
    //         viewSettingType: "compact",
    //         showDuration: false,
    //         showReminder: false,
    //         groupBy: "date"
    //       },
    //       futureTodoSettings: {
    //         showReminder: false,
    //         showEmptyDays: false,
    //         showImage: false,
    //         viewSettingType: "compact",
    //         groupBy: "date",
    //         showDuration: false,
    //         showCounts: false,
    //         collapsedView: false,
    //         showChecklist: false,
    //         showTime: true,
    //         descriptionType: "none",
    //         showTags: false,
    //         showRepeat: false
    //       }
    //     },
    //     insightSettings: {
    //       insightHabitSettings: {
    //         resultInPercentage: false,
    //       },
    //       insightJournalSettings: {
    //         resultInPercentage: false,
    //       },
    //       insightTodoSettings: {
    //         resultInPercentage: false,
    //       },
    //     },
    //     listSettings: {
    //       publicListItemSheetSettings: {
    //         showLastUpdatedBy: false,
    //         viewType: "compact",
    //         showDescription: true,
    //         showLastUpdatedAt: false
    //       },
    //       sharedListSettings: {
    //         collapsedView: false,
    //         groupByType: "none",
    //         viewSettingType: "compact",
    //         showHashtags: false,
    //         itemCount: true,
    //         showAccess: false,
    //         showCounts: false,
    //         showDescription: false
    //       },
    //       myListSettings: {
    //         collapsedView: true,
    //         itemCount: true,
    //         showDescription: false,
    //         showCollaboratorsCount: true,
    //         showAwaitingUserCount: false,
    //         viewSettingType: "custom",
    //         groupByType: "none",
    //         showInvitedUsersCount: false,
    //         showCounts: false,
    //         showHashtags: true
    //       },
    //       sharedListItemSettings: {
    //         showDescription: true,
    //         showLastUpdatedBy: false,
    //         viewType: "compact",
    //         showLastUpdatedAt: false
    //       },
    //       myListItemSheetSettings: {
    //         showDescription: true,
    //         showLastUpdatedBy: true,
    //         viewType: "custom",
    //         showLastUpdatedAt: true
    //       },
    //       publicListSettings: {
    //         itemCount: true,
    //         showDescription: false,
    //         viewSettingType: "compact"
    //       }
    //     },
    //     moneyTrackerSettings: {
    //       config: {
    //         title: null,
    //         currency: '₹',
    //         hasSetCurrency: false
    //       },
    //       listViewSettings: {
    //         showDate: true,
    //         showHashtag: false,
    //         descriptionType: 'none',
    //         showImage: false,
    //         groupBySettings: 'date',
    //         showEmptyDays: false,
    //         collapsedView: false,
    //         showNetAmount: false,
    //         viewSettingType: "compact"
    //       }
    //     },
    //     noteSettings: {
    //       savedNoteSettings: {
    //         showFilterRow: false,
    //         noteViewImage: false,
    //         noteViewTime: false,
    //         noteDescriptionType: "none",
    //         notesGroupBy: "none",
    //         collapsedView: false,
    //         noteViewDate: true,
    //         showCounts: false,
    //         viewType: "compact",
    //         noteViewMood: false
    //       },
    //       myNoteSettings: {
    //         showCounts: false,
    //         noteViewTime: true,
    //         showMemberCount: true,
    //         viewType: "custom",
    //         showFilterRow: false,
    //         noteViewTags: false,
    //         noteViewMood: true,
    //         notesGroupBy: "none",
    //         noteViewDate: true,
    //         noteDescriptionType: "short",
    //         noteViewImage: true,
    //         collapsedView: false
    //       },
    //       sharedNoteSettings: {
    //         noteDescriptionType: "none",
    //         noteViewDate: true,
    //         showAccess: false,
    //         noteViewImage: false,
    //         viewType: "compact",
    //         showFilterRow: false,
    //         collapsedView: false,
    //         notesGroupBy: "none",
    //         noteViewMood: false,
    //         showCounts: false,
    //         noteViewTime: false
    //       }
    //     },
    //     notificationSettings: {
    //       isDailyAgendaMobileNotificationEnabled: false,
    //       isDailyAgendaEmailNotificationEnabled: false,
    //       isOverdueEmailNotificationEnabled: false,
    //       emailNotificationTime: utilsService.getCustomDate(),
    //       muteAllDevice: null,
    //       emailNotificationTimezone: "+05:30",
    //       isMuteAllDeviceTmzDependent: true,
    //       muteAllDeviceStartAt: null,
    //       remindMeType: "sameDay",
    //       muteAllCustomPresetSelected: false,
    //       mobileDailyAgendaNotificationTime: utilsService.getCustomDate(),
    //       pinReminder: false,
    //       mobileSoundType: "mevolve1",
    //       mobileSnoozeType: "five",
    //       isDailyAgendaTmzDependent: false
    //     },
    //     pastSettings: {
    //       pastJournalSettings: {
    //         showTime: true,
    //         collapsedView: false,
    //         showTags: false,
    //         showDuration: false,
    //         descriptionType: "none",
    //         showMood: false,
    //         groupBy: "date",
    //         showDate: false,
    //         showRepeat: false,
    //         showEmptyDays: false,
    //         showInvalidEntries: false,
    //         showCounts: false,
    //         showReminder: false,
    //         showImage: false,
    //         viewSettingType: "compact"
    //       },
    //       pastHabitSettings: {
    //         showDate: false,
    //         showReminder: false,
    //         showDuration: false,
    //         showImage: false,
    //         showTime: true,
    //         descriptionType: "none",
    //         showTags: false,
    //         showEmptyDays: false,
    //         groupBy: "date",
    //         showHabitResponse: false,
    //         showInvalidEntries: false,
    //         collapsedView: false,
    //         showCounts: false,
    //         viewSettingType: "compact",
    //         showRepeat: false
    //       },
    //       pastCalendarEventSettings: {
    //         showTime: true,
    //         groupBy: "date",
    //         collapsedView: false,
    //         showCalendarName: false,
    //         showReminder: false,
    //         viewSettingType: "compact",
    //         showRepeat: false,
    //         showDuration: false,
    //         showCounts: false
    //       },
    //       pastTodoSettings: {
    //         showCounts: false,
    //         showChecklist: false,
    //         showEmptyDays: false,
    //         showImage: false,
    //         showReminder: false,
    //         showTime: true,
    //         descriptionType: "none",
    //         viewSettingType: "compact",
    //         showDate: false,
    //         groupBy: "date",
    //         collapsedView: false,
    //         showDuration: false,
    //         showRepeat: false,
    //         showTags: false
    //       },
    //       pastMoneyTrackerSettings: {
    //         groupBySettings: "date",
    //         descriptionType: "none",
    //         showNetAmount: false,
    //         showSetupTitle: true,
    //         showImage: false,
    //         showHashtag: false,
    //         collapsedView: false,
    //         showEmptyDays: false,
    //         showDate: false,
    //         viewSettingType: "compact"
    //       }
    //     },
    //     todaySettings: {
    //       unscheduleSettings: {
    //         showChecklist: false,
    //         descriptionType: "none",
    //         viewSettingType: "compact",
    //         showImage: false,
    //         showTags: false,
    //         showCompletedAt: true
    //       },
    //       todayTabSettings: {
    //         showTime: true,
    //         showDuration: true,
    //         showReminder: true,
    //         showCalendarName: true,
    //         showImage: true,
    //         viewSettingType: "custom",
    //         showRepeat: true,
    //         showHabitResponse: true,
    //         showTags: false,
    //         showMood: true,
    //         showInvalidEntries: false,
    //         showChecklist: true,
    //         descriptionType: "none"
    //       },
    //       overdueSettings: {
    //         showDuration: false,
    //         showChecklist: false,
    //         showTags: false,
    //         showReminder: false,
    //         showRepeat: false,
    //         viewSettingType: "compact",
    //         descriptionType: "none",
    //         showTime: true,
    //         showImage: false
    //       }
    //     },
    //     sessionId: utilsService.getNewId(),
    //     createdAt: new Date(),
    //     cloudUpdatedAt: new Date(),
    //     deletedAt: null,
    //     permaDeletedAt: null,
    //   }
    // }

  })),

  withHooks({
    async onInit(
      store,
      idbService = inject(IndexDbService),
      ss = inject(StorageService),
      cryptoService = inject(CryptographyService)
    ) {
      const userData = ss.getUser();

      if (userData) {
        const user = {
          uid: userData.uid,
          email: userData.userInfo.email,
          name: userData.userInfo.name,
          isNewUser: false,
          hashedEmail: ss.getHashedEmail()
        };
        patchState(store, { user: user });
      }

      idbService.userResource$.pipe(takeUntilDestroyed()).subscribe(data => {
        if (data) {
          patchState(store, { userResource: data[0] });
        }
      });

      idbService.userMetadata$.pipe(takeUntilDestroyed()).subscribe(data => {
        if (data) {
          patchState(store, { userMetadata: data[0] });
        }
      });

      idbService.viewSettings$.pipe(takeUntilDestroyed()).subscribe(data => {
        if (data) {
          console.log('viewSettings =======================', data[0]);
          patchState(store, { viewSettings: data[0] });
        }
      });
    },
  }),
);

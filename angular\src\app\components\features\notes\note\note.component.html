<div class="top-section">
  <h4 class="heading mb-0">My Notes</h4>
  <div class="">
    <app-svg [name]="isFilter ? 'filterOutline' : 'filter'" class="filter-icon ri-me-6" role="button" (click)="toggleFilter()"></app-svg>

    <app-svg name="more" class="more-icon" [matMenuTriggerFor]="infoMenu" role="button" [color]="cc.theme.color12"></app-svg>
    <mat-menu #infoMenu="matMenu" class="me-menu info-menu ri-w-350px">
      <button mat-menu-item (click)="$event.stopPropagation();">
        <div class="ri-me-6">
          <span class="text-14-400 color-8">Group By</span>
          <app-input-dropdown *ngIf="viewSignals()['noteGroupBy']() !== 'none'" class="ri-pt-6px" [multiple]="true" [signal]="vs.type()['noteGroupedViewType']" [config]="vs.groupedViewTypeConfig()" (select)="vs.updateView()" textClass="text-12-400"></app-input-dropdown>
        </div>
        <app-input-dropdown [signal]="vs.type()['noteGroupBy']" [config]="vs.noteGroupByConfig" (select)="vs.updateView()" textClass="text-12-400 whitespace-nowrap"></app-input-dropdown>
      </button>
      <button mat-menu-item (click)="$event.stopPropagation()">
        <div class="ri-me-6">
          <span class="text-14-400 color-8">Show</span>
          <app-input-dropdown class="ri-pt-6px" [multiple]="true" [signal]="vs.type()['noteShow']" [config]="vs.noteShowConfig" (select)="vs.updateView()" (selectNested)="vs.updateView()" textClass="text-12-400" [disabled]="viewSignals()['noteShowType']() === 'compact'"></app-input-dropdown>
        </div>
        <app-input-dropdown [signal]="vs.type()['noteShowType']" [config]="vs.showTypeConfig()" (select)="vs.updateShowType('noteShowType')" textClass="text-12-400 whitespace-nowrap"></app-input-dropdown>
      </button>
    </mat-menu>
  </div>
</div>

<div class="body-section bg-3 position-relative">
  <div class="filter-block" [ngClass]="{'h-auto filtered': isFilter}" dragScroll>
    <app-filter-search class="me-2" [signal]="noteStore.filter().query"></app-filter-search>
    
    <app-filter-hashtag [signal]="noteStore.filter().tags" class="me-2"></app-filter-hashtag>

    <app-filter-favourite [signal]="noteStore.filter().isFav" class="me-2"></app-filter-favourite>

    <app-filter-date-range [startDate]="noteStore.filter().startDate" [endDate]="noteStore.filter().endDate" class="me-2"></app-filter-date-range>

    <app-filter-attachment [signal]="noteStore.filter().attachments" [multiple]="true" class="me-2"></app-filter-attachment>

    <app-filter-mood [signal]="noteStore.filter().moods" [multiple]="true" class="me-2"></app-filter-mood>

    <button class="btn btn-filter btn-clear" (click)="noteStore.clearFilter()" *ngIf="noteStore.isFiltered()">Clear all</button>
  </div>

  <div class="note-block" [ngClass]="isFilter ? 'filter-applied' : 'no-filter'">

    <ng-container *ngIf="viewSignals()['noteGroupBy']() === 'none' && noteStore.filteredNotes().length !== 0">
      <ng-container
        *ngTemplateOutlet="notesBlock; context: { $implicit: noteStore.filteredNotes(), groupId: 'noGroup' }"></ng-container>
    </ng-container>
    <ng-container *ngIf="viewSignals()['noteGroupBy']() !== 'none' && noteStore.filteredNotes().length !== 0">
      <ng-container *ngFor="let group of viewSignals()['noteGroupBy']() === 'hashtag' ? noteStore.hashtagGroup() : viewSignals()['noteGroupBy']() === 'mood' ? noteStore.moodsGroup() : noteStore.dateGroup();let i = index;">
        <div class="text-16-400 color-1 ri-p-4 ri-bb-2 flex items-center justify-between" role="button" [attr.isOpen]="getIsOpen()" #tgroupHead (click)="toggleChildGroup(tgroupHead,'noteGroup' + i)">
          <span>{{ group.name }}</span>
          <span class="group-count" *ngIf="vs.type()['listGroupedViewType']().includes('showCounts')">{{ group.data.length }}</span>
        </div>
        <ng-container
          *ngTemplateOutlet="notesBlock; context: { $implicit: group.data, groupId: 'noteGroup' + i }"></ng-container>
      </ng-container>
    </ng-container>

    <ng-template #notesBlock let-element let-groupId="groupId">
      <div [ngClass]="viewSignals()['noteGroupBy']() === 'none' ? 'row-nogroup' : 'row-grouped'" [attr.id]="groupId"
        [attr.isGroupOpened]="getIsOpen()">
        <div class="note-data ri-px-4 ri-py-3" *ngFor="let note of element; let i = index;"
          (click)="openNoteForm('edit', note)" role="button">
          <div class="flex justify-between items-start">
            <p class="text-16-400 color-8 mb-0">{{ note.title }}</p>
            <app-svg name="star" [color]="cc.theme.color35" *ngIf="note.isFav" width="24px" height="24px" class="ri-ms-4"></app-svg>
          </div>
          <p class="text-12-400 color-7 mb-0 ri-pt-2" *ngIf="viewSignals()['noteDescriptionType']() !== 'none' && note.description">{{ note.description | parseText: viewSignals()['noteDescriptionType']() === 'short' ? 'short' : 'full' }}</p>
          <p class="text-12-400 color-7 mb-0 ri-pt-2 flex items-center" *ngIf="viewSignals()['noteShow']().includes('mood') && (note.emotion || note.emotion === 0)"><app-svg [name]="moodEmojiMap()[note.emotion]" [color]="cc.theme.color22" class="pe-1" style="height: 18px; width: 18px;"></app-svg>{{ moodMap()[note.emotion] }}</p>
          <div class="mb-0 ri-pt-2" *ngIf="viewSignals()['noteShow']().includes('date') || viewSignals()['noteShow']().includes('time') || viewSignals()['noteShow']().includes('collaboratorsCount')">
            <div class="inline-flex items-center ri-pe-3" *ngIf="viewSignals()['noteShow']().includes('date')">
              <app-svg name="calendar" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
              <span class="text-12-400 color-7">{{ note.noteUpdatedAt | date:'dd MMM YYYY'}}</span>
            </div>
            <div class="inline-flex items-center ri-pe-3" *ngIf="viewSignals()['noteShow']().includes('time')">
              <app-svg name="clock" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
              <span class="text-12-400 color-7">{{ note.noteUpdatedAt | date:'h:mm a'}}</span>
            </div>
            <div class="inline-flex items-center ri-pe-3" *ngIf="viewSignals()['noteShow']().includes('attachments') && note.attachments.length > 0">
              <app-svg name="attachment" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
              <span class="text-12-400 color-7">{{ note.attachments.length }}</span>
            </div>
            <div class="inline-flex items-center ri-pe-3" *ngIf="viewSignals()['noteShow']().includes('collaboratorsCount') && ((note.members && note.members.memberHashedEmails?.length > 0) || note.isPublic)">
              <app-svg [name]="note.isPublic ? 'multiUser' : 'users'" [color]="cc.theme.color7" class="pe-1" style="height: 16px; width: 16px;"></app-svg>
              <span class="text-12-400 color-7">{{ note.members.memberHashedEmails?.length }}</span>
            </div>
          </div>
          <p class="text-12-400 color-7 mb-0 ri-pt-2 hashtags-block" *ngIf="viewSignals()['noteShow']().includes('hashtag') && note.tags.length > 0">
            <span class="hashtag" *ngFor="let tagId of note.tags">#{{userStore.tagMap()[tagId]}}</span>
          </p>
        </div>
      </div>
    </ng-template>
    
    <div class="no-result-block h-full flex items-center justify-center flex-col" *ngIf="noteStore.activeNotes().length === 0 || (noteStore.activeNotes().length > 0 && noteStore.filteredNotes().length === 0)">
      <app-svg [name]="noteStore.activeNotes().length === 0 ? 'notePlaceholder' : 'searchPlaceholder'" [color]="cc.theme.color1"></app-svg>
      <p class="text-16-400 color-8 mb-0 ri-pt-4">{{ noteStore.activeNotes().length === 0 ? 'No Notes' : 'No Data' }}</p>
    </div>
    <div class="extra-block" *ngIf="noteStore.filteredNotes().length > 0"></div>
  </div>
</div>

<div class="add-button">
  <button class="btn" (click)="openNoteForm('new')"><app-svg name="plus"></app-svg></button>
</div>
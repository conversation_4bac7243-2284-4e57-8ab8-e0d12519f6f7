<button class="btn btn-filter btn-toggle h-full" [matMenuTriggerFor]="filterAttachmentMenu" [ngClass]="{ 'active': signal().length > 0 }">
    <app-svg name="attachment" [color]="signal().length ? cc.theme.color12 : cc.theme.color7" class="me-1" style="height: 15px; width: 15px;" *ngIf="signal().length !== 1"></app-svg>
    <app-svg [name]="signal()[0] === 'video' ? 'playOutline' : signal()[0]" [color]="cc.theme.color12" class="me-1" style="height: 15px; width: 15px;" *ngIf="signal().length === 1"></app-svg>
    <span>{{ getLabel() }}</span>

    <div class="clear-icon ms-2" *ngIf="signal().length > 0" role="button" (click)="signal.set([]);$event.stopPropagation()">
        <app-svg class="d-flex align-items-center justify-content-center" name="close" [color]="cc.theme.color1"></app-svg>
    </div>
</button>
<mat-menu #filterAttachmentMenu="matMenu" class="me-menu ri-w-350px">
    <button mat-menu-item *ngFor="let type of attachmentTypes" (click)="onSelected(type, $event)">
        <div class="flex items-center justify-between">
            <span class="text-14-400 flex color-8">
                <app-svg [name]="type === 'video' ? 'playOutline' : type" [color]="cc.theme.color35" class="ri-pe-4"></app-svg>{{attachmentMap()[type]}}
            </span>
            <app-svg *ngIf="multiple" name="tick" [color]="signal().includes(type) ? cc.theme.color35 : cc.theme.color10"></app-svg>
        </div>
    </button>
</mat-menu>
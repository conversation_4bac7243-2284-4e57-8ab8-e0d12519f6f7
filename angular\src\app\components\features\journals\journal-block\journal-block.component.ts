import { CommonModule } from '@angular/common';
import { Component, computed, inject, Input, Signal } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Journal, JournalSetup } from '@app/_interfaces/journal.interface';
import { CacheService } from '@app/_services/cache.service';
import { JournalStore, UserStore } from '@app/_stores';
import { getDateString } from '@app/_utils/utils';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { JournalFormComponent } from '../journal-form/journal-form.component';
import { ParseMinutesPipe } from '@app/_pipes/parse-minutes.pipe';
import { ParseTimePipe } from '@app/_pipes/parse-time.pipe';
import { InputCheckmarkAdvancedComponent } from '@app/components/shared/inputs/input-checkmark-advanced/input-checkmark-advanced.component';
import { EntitySetup } from '@app/_interfaces/feature.interface';
import { ParseRulePipe } from '@app/_pipes/parse-rule.pipe';
import { ParseTextPipe } from '@app/_pipes/parse-text.pipe';
import { MapService } from '@app/_services/map.service';
import { DependencyService } from '@app/_services/dependency.service';

@Component({
  selector: 'app-journal-block',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    ParseTimePipe,
    ParseMinutesPipe,
    InputCheckmarkAdvancedComponent,
    ParseRulePipe,
    ParseTextPipe
  ],
  templateUrl: './journal-block.component.html',
  styleUrl: './journal-block.component.scss'
})

export class JournalBlockComponent {

  readonly userStore = inject(UserStore);
  readonly journalStore = inject(JournalStore);
  @Input() show: string[] = [];
  @Input() blockClass: string = '';
  @Input() dateString: string = getDateString();
  @Input() entity!: EntitySetup;
  @Input() descriptionType: string = 'none';
  @Input() isLabel: boolean = true;
  
  journal: Signal<Journal | null> = computed(() => {
    return this.journalStore.journalMap().setups[this.entity.id || '']?.[this.dateString] || null;
  });
  setup: Signal<JournalSetup> = computed(() => {
    return this.journalStore.idToSetup()[this.entity.id || ''];
  });

  constructor(public cc: CacheService, public dialog: MatDialog, public mapService: MapService, public ds: DependencyService) {
    
  }
  
  ngOnInit() {
    
  }

  // get journal(): Journal | null {
  //   return this.journalData();
  // }

  openJournal(setupId: string) {
    const confirmDialog = this.dialog.open(JournalFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      minHeight: '90vh',
      disableClose: true,
      data: {
        mode: this.journal() ? 'edit' : 'new',
        value: this.journal(),
        setupId: setupId,
        dateString: this.dateString
      },
    });
    return confirmDialog.afterClosed();
  }
}

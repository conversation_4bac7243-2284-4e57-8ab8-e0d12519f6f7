import { CommonModule } from '@angular/common';
import { Component, inject, Input, TemplateRef, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { EntitySetup } from '@app/_interfaces/feature.interface';
import { CacheService } from '@app/_services/cache.service';
import { CalendarModule, CalendarEvent, DateAdapter, CalendarUtils, CalendarA11y, CalendarDateFormatter, CalendarView, CalendarEventTitleFormatter } from 'angular-calendar';
import { adapterFactory } from 'angular-calendar/date-adapters/date-fns';
import { JournalFormComponent } from '../../journals/journal-form/journal-form.component';
import { CalendarIntegrationStore, HabitStore, JournalStore, TodoStore } from '@app/_stores';
import { getDateString } from '@app/_utils/utils';
import { TodoFormComponent } from '../../todos/todo-form/todo-form.component';
import { CalendarEventFormComponent } from '../../calendar-integration/calendar-event-form/calendar-event-form.component';
import { HabitFormComponent } from '../../habits/habit-form/habit-form.component';

@Component({
  selector: 'app-entity-calendar',
  standalone: true,
  imports: [
    CommonModule,
    CalendarModule,
  ],
  providers: [
    { provide: DateAdapter, useFactory: adapterFactory },
    CalendarUtils,
    CalendarA11y,
    CalendarDateFormatter,
    CalendarEventTitleFormatter,
  ],
  templateUrl: './entity-calendar.component.html',
  styleUrl: './entity-calendar.component.scss'
})

export class EntityCalendarComponent {

  @ViewChild('customEventTemplate') customEventTemplate!: TemplateRef<any>;
  view: CalendarView = CalendarView.Day;

  @Input() viewDate: Date = new Date();
  @Input() dateString: string = getDateString();
  @Input() events: CalendarEvent[] = [];
  @Input() withoutTimeEvents: EntitySetup[] = [];

  readonly todoStore = inject(TodoStore);
  readonly habitStore = inject(HabitStore);
  readonly journalStore = inject(JournalStore);
  readonly calendarStore = inject(CalendarIntegrationStore);

  constructor(private dialog: MatDialog, public cc: CacheService) {

  }

  ngOnInit() {
    console.log("changed-----", this.events);
    console.log("view Date-----", this.withoutTimeEvents);
    this.events = this.events;
  }

  handleScheduledEvent(event: CalendarEvent): void {
    console.log("event clicked", event);
    switch (event.meta.entity) {
      case 'journal':
        this.openJournal(event.id as string);
        break;
      case 'todo':
        this.openTodo(event.id as string);
        break;
      case 'calendarIntegration':
        this.openCalendarEvent(event.id as string);
        break;
      default:
        break;
    }
  }

  handleUnScheduledEvent(event: EntitySetup): void {
    switch (event.entityName) {
      case 'journal':
        this.openJournal(event.id);
        break;
      case 'todo':
        this.openTodo(event.id);
        break;
      default:
        break;
    }
  }

  openJournal(setupId: string) {
    const journal = this.journalStore.journalMap().setups[setupId]?.[this.dateString] || null;

    const confirmDialog = this.dialog.open(JournalFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      minHeight: '90vh',
      disableClose: true,
      data: {
        mode: journal ? 'edit' : 'new',
        value: journal,
        setupId: setupId,
        dateString: this.dateString
      },
    });
    return confirmDialog.afterClosed();
  }

  openTodo(id: string) {
    const todo = this.todoStore.idToTodo()[id] || null;

    const confirmDialog = this.dialog.open(TodoFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      minHeight: '90vh',
      disableClose: true,
      data: {
        mode: 'edit',
        value: todo,
        dateString: this.dateString
      },
    });
    return confirmDialog.afterClosed();
  }

  openHabit(setupId: string) {
    const habit = this.habitStore.habitMap().setups[setupId]?.[this.dateString] || null;
    const confirmDialog = this.dialog.open(HabitFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      minHeight: '90vh',
      disableClose: true,
      autoFocus: false,
      data: {
        mode: 'edit',
        value: habit,
        dateString: this.dateString,
        setupId: setupId
      },
    });
    // return confirmDialog.afterClosed();
  }

  openCalendarEvent(id: string) {
    const event = this.calendarStore.idToEventSetup()[id] || null;

    const confirmDialog = this.dialog.open(CalendarEventFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      minHeight: '90vh',
      disableClose: true,
      data: {
        mode: 'edit',
        value: event,
        dateString: this.dateString
      },
    });
    return confirmDialog.afterClosed();
  }

}

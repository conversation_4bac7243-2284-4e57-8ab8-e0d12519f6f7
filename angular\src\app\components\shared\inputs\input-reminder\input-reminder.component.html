<div class="flex items-start w-full me-input-reminder" [ngClass]="{'readonly': readonly}">
    <app-svg name="bell" [color]="cc.theme.color35" class="bell-icon ri-pe-4" *ngIf="!hideIcon"></app-svg>
    <div class="me-reminder-block">
        <ng-container *ngIf="control.value.length > 0">
            <span class="reminder" (click)="openReminderDialog('edit', reminder)" role="button" *ngFor="let reminder of control.value">{{ getReminderText(reminder) }}</span>
            <span class="add-reminder" (click)="openReminderDialog('add')" role="button"><app-svg name="plus" [color]="cc.theme.color8" style="height: 12px;"></app-svg></span>
        </ng-container>
        <p *ngIf="control.value.length === 0" class="color-7 mb-0" (click)="openReminderDialog('add')" role="button">{{ cc.texts()['screen_common_noReminder'] }}</p>
    </div>
</div>
<div class="filter-block" [ngClass]="{'h-auto filtered': filter}" dragScroll>
    <app-filter-search [signal]="filterSearch" [name]="'todo'" class="me-2"></app-filter-search>
    <app-filter-hashtag [signal]="filterHashtag" class="me-2"></app-filter-hashtag>
    <app-filter-date-range [startDate]="startDate" [endDate]="endDate" class="me-2"></app-filter-date-range>
    <app-filter-attachment [signal]="filterAttachment" [multiple]="true" class="me-2"></app-filter-attachment>
    <button class="btn btn-filter btn-toggle" (click)="clearFilter()" *ngIf="isFiltered()">{{ cc.texts()['screen_common_clearAll'] }}</button>
</div>

<div class="money-tracker-tab-block" [ngClass]="{'filter-applied': filter}">
  <cdk-virtual-scroll-viewport itemSize="15" class="viewport" #viewport autoSize (scrolledIndexChange)="onScrolledIndexChange($event)" *ngIf="isDataAvailable">
  <ng-container *cdkVirtualFor="let group of transactions(); let i = index;">
    <div class="group-title text-16-400 ri-p-4 ri-bb-2 flex justify-between items-center" #tgroupHead role="button" [attr.isOpen]="getIsOpen()" (click)="toggleChildGroup(tgroupHead,'todoEntityGroup' + i)" *ngIf="group.data.length !== 0 || (group.data.length === 0 && show.includes('emptyDays'))">
        <span [ngClass]="group.data.length === 0 ? 'color-4' : 'color-1'">{{ getGroupName(group) }}</span>
        <span class="group-count" *ngIf="groupedViewType.includes('showNetAmount') && group.data.length > 0" [ngClass]="getNetAmount(group.data).type">{{ getNetAmount(group.data).netAmount }}</span>
    </div>

    <div class="row-grouped" [attr.id]="'todoEntityGroup' + i" [attr.isGroupOpened]="getIsOpen()" *ngIf="group.data.length !== 0 || (group.data.length === 0 && show.includes('emptyDays'))">
        <app-money-tracker-block class="ri-bb-2 entity-block"  *ngFor="let entity of group.data; let i = index; trackBy: trackByForEntity;" [blockClass]="'ri-px-4 ri-py-3'" [show]="show" [entity]="entity" [descriptionType]="descriptionType" [isLabel]="false"></app-money-tracker-block>
    </div>
  </ng-container>
  </cdk-virtual-scroll-viewport>
  <div class="no-result-block h-full flex items-center justify-center flex-col" *ngIf="!isDataAvailable">
    <app-svg name="moneyTrackerPlaceholder" [color]="cc.theme.color1"></app-svg>
    <p class="text-16-400 color-8 mb-0 ri-pt-4">{{ cc.texts()['screen_common_moneyTrackerEmptyScreenContent'] }}</p>
  </div>
</div>
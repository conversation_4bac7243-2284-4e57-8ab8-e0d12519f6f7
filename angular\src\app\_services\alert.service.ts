import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { AlertDialogComponent, AlertDialogModel } from '@app/components/shared/alert-dialog/alert-dialog.component';
import { CacheService } from './cache.service';

@Injectable({
  providedIn: 'root',
})

export class AlertService {

  private alertDialog: any;
  constructor(public dialog: MatDialog, public cc: CacheService) { }

  alert(
    header: string | { var: string; eg: string; }[] = '',
    message: string | { var: string; eg: string; }[] = '',
    yesButtonText: string | { var: string; eg: string; }[] = 'Yes',
    yesButtonColor: string = 'color-35',
    secondaryMessage: string | { var: string; eg: string; }[] | null = null,
    secondaryMessageColor: string = 'color-11'
  ) {
    this.alertDialog = this.dialog.open(AlertDialogComponent, {
      width: '100%',
      maxWidth: '400px',
      disableClose: false,
      data: new AlertDialogModel(
        header instanceof Array ? this.cc.textsWithVariables(header) : header,
        message instanceof Array ? this.cc.textsWithVariables(message) : message,
        false,
        false,
        '',
        { yes: yesButtonText instanceof Array ? this.cc.textsWithVariables(yesButtonText) : yesButtonText, no: '', yesButtonColor: yesButtonColor, noButtonColor: 'color-7' },
        false,
        secondaryMessage instanceof Array ? this.cc.textsWithVariables(secondaryMessage) : secondaryMessage,
        secondaryMessageColor
      ),
    });
    if (!confirm) {
      return Promise.resolve(false);
    }
    return Promise.resolve(this.alertDialog.afterClosed().toPromise());
  }

  confirm(
    header: string | { var: string; eg: string; }[] = '',
    message: string | { var: string; eg: string; }[] = '',
    yesButtonText: string | { var: string; eg: string; }[] = 'Yes',
    noButtonText : string | { var: string; eg: string; }[] = 'No',
    yesButtonColor: string = 'color-35',
    noButtonColor: string = 'color-7'
  ) {
    this.alertDialog = this.dialog.open(AlertDialogComponent, {
      width: '100%',
      maxWidth: '400px',
      disableClose: false,
      data: new AlertDialogModel(
        header instanceof Array ? this.cc.textsWithVariables(header) : header,
        message instanceof Array ? this.cc.textsWithVariables(message) : message,
        false,
        false,
        '',
        { yes: yesButtonText instanceof Array ? this.cc.textsWithVariables(yesButtonText) : yesButtonText, no: noButtonText instanceof Array ? this.cc.textsWithVariables(noButtonText) : noButtonText, yesButtonColor: yesButtonColor, noButtonColor: noButtonColor },
        true
      ),
    });
    if (!confirm) {
      return Promise.resolve(false);
    }
    return Promise.resolve(this.alertDialog.afterClosed().toPromise());
  }

  checkAndConfirm(
    header: string,
    message: string,
    checkboxChecked: boolean = false,
    checkboxLabel: string,
    yesButtonText = 'Yes',
    noButtonText = 'No'
  ) {
    this.alertDialog = this.dialog.open(AlertDialogComponent, {
      minWidth: '500px',
      maxWidth: '500px',
      data: new AlertDialogModel(
        header,
        message,
        true,
        checkboxChecked,
        checkboxLabel,
        { yes: yesButtonText, no: noButtonText, yesButtonColor: 'color-35', noButtonColor: 'color-7' },
        true,
      ),
    });
    if (!confirm) {
      return Promise.resolve(false);
    }
    return Promise.resolve(this.alertDialog.afterClosed().toPromise());
  }
}

import { Component, computed, inject, Input, Signal } from '@angular/core';
import { CalendarIntegrationStore, UserStore } from '@app/_stores';
import { CalendarEventSetup } from '@app/_interfaces/calendar-integration.interface';
import { EntitySetup } from '@app/_interfaces/feature.interface';
import { getDateString } from '@app/_utils/utils';
import { CommonModule } from '@angular/common';
import { ParseTimePipe } from '@app/_pipes/parse-time.pipe';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { MatDialog } from '@angular/material/dialog';
import { CalendarEventFormComponent } from '../calendar-event-form/calendar-event-form.component';
import { CacheService } from '@app/_services/cache.service';
import { ParseTextPipe } from '@app/_pipes/parse-text.pipe';
import { DependencyService } from '@app/_services/dependency.service';
import { ParseMinutesPipe } from '@app/_pipes/parse-minutes.pipe';

@Component({
  selector: 'app-calendar-event-block',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    ParseTimePipe,
    ParseMinutesPipe,
    ParseTextPipe
  ],
  templateUrl: './calendar-event-block.component.html',
  styleUrl: './calendar-event-block.component.scss'
})

export class CalendarEventBlockComponent {

  readonly calendarStore = inject(CalendarIntegrationStore);
  readonly userStore = inject(UserStore);
  @Input() show: string[] = [];
  @Input() blockClass: string = '';
  @Input() dateString: string = getDateString();
  @Input() entity!: EntitySetup;
  @Input() descriptionType: string = 'none';
  @Input() isLabel: boolean = true;

  event: Signal<CalendarEventSetup> = computed(() => {
    return this.calendarStore.idToEventSetup()[this.entity.id];
  })

  constructor(public dialog: MatDialog, public cc: CacheService, public ds: DependencyService) {
    
  }

  openCalendarEventForm() {
      const confirmDialog = this.dialog.open(CalendarEventFormComponent, {
        width: '100%',
        maxWidth: '750px',
        maxHeight: '90vh',
        minHeight: '90vh',
        disableClose: true,
        data: {
          mode: 'edit',
          value: this.entity,
          dateString: this.dateString
        },
      });
      return confirmDialog.afterClosed();
    }
}

import { CommonModule } from '@angular/common';
import { Component, inject, signal, ViewChild, WritableSignal } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, NgForm, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { AlertService } from '@app/_services/alert.service';
import { UserStore } from '@app/_stores/user.store';
import { InputEmailComponent } from '@app/components/shared/inputs/input-email/input-email.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { Subject, takeUntil } from 'rxjs';
import * as _ from "lodash";
import { CacheService } from '@app/_services/cache.service';
import { CollaboraterRole } from '@app/_types/generic.type';
import { InputDropdownComponent } from '@app/components/shared/inputs/input-dropdown/input-dropdown.component';
import { ViewSettingService } from '@app/_services/view-setting.service';
import { emailValidator } from '@app/_directives/form-validator.directive';

@Component({
  selector: 'app-collaborator-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SvgComponent,
    InputEmailComponent,
    InputDropdownComponent
  ],
  templateUrl: './collaborator-form.component.html',
  styleUrl: './collaborator-form.component.scss'
})

export class CollaboratorFormComponent {

  @ViewChild('collabForm') collabForm!: NgForm;
  unSubscribe = new Subject<void>();
  collaboratorForm: FormGroup;
  collaboratorInitial: any;
  readonly userStore = inject(UserStore);
  roleSignal: WritableSignal<CollaboraterRole> = signal('viewer');

  constructor(
    public dialogRef: MatDialogRef<CollaboratorFormComponent>,
    private alertService: AlertService,
    private fb: FormBuilder,
    public cc: CacheService,
    public vs: ViewSettingService
  ) {

    this.collaboratorInitial = this.initiateForm();
    this.roleSignal.set(this.collaboratorInitial.role);

    this.collaboratorForm = this.fb.group({
      email: new FormControl(this.collaboratorInitial.email, [Validators.required, emailValidator(), Validators.maxLength(320)]),
      role: new FormControl(this.collaboratorInitial.role, Validators.required)
    });

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  getFc(fcName: string): FormControl {
    return this.collaboratorForm.get(fcName) as FormControl;
  }

  initiateForm(): any {
    return {
      email: '',
      role: 'viewer',
    };
  }

  hasChanges() {
    const initial = _.cloneDeep(this.collaboratorInitial);
    const current = _.cloneDeep(this.collaboratorForm.value);
    return !_.isEqual(initial, current);
  }

  async save() {
    this.collaboratorForm.get('role')?.setValue(this.roleSignal());
    this.dialogRef.close(this.collaboratorForm.value);
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
        this.dialogRef.close();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }

  reset() {
    this.collaboratorForm.reset();
    this.collabForm.resetForm();
    this.collaboratorForm.patchValue(this.collaboratorInitial);
  }

}
